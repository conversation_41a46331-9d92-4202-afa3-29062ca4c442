# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

DATABASE_URL="postgresql://book:book@localhost:5432/book?schema=public"


# Application variables
NODE_ENV="development"
HOST="127.0.0.1"
PORT=3000
BASE_URL="http://localhost:3000/api"
FRONTEND_BASE_URL="http://localhost:3000"

# Swagger variables
API_VERSION="0.1"
PROJECT_TITLE="Sisurace Book Service"
PROJECT_DESCRIPTION="API document for Sisurace book servie."

JWT_SECRET="dsl"
JWT_EXPIRES_IN='2h'
REFRESH_TOKEN_TTL=86400
GOOGLE_REDIRECT_URL="o"

DISCORD_CLIENT_ID="1247192060742733864"
DISCORD_CLIENT_SECRET="30QZBUMd8p1141vpEGgPSJolW5gw_HFA"

GOOGLE_CLIENT_ID="988158395171-2j8pqetqoderk9sv3kaq17mhkecigo82.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

TWITTER_CLIENT_ID="1247192060742733864"
TWITTER_CLIENT_SECRET="30QZBUMd8p1141vpEGgPSJolW5gw_HFA"


##Testnet Mainnet
BLOCK_CHAIN_NETWORK = "Testnet"

##eth-address-official
OFFICIAL_ADDRESS_ETH=""
##btc-address-official
OFFICIAL_ADDRESS_BTC=""
##btc-address-official
OFFICIAL_ADDRESS_CKB=""

#DOB DNA SDK URL
DOB_SDK_URL = "http://localhost:8090"
##Secp256k1_privateKey
SECP256K1_PRIVATE_KEY="0xbe0532a35b4e05026d02315f8c97e752da9f5503c0a9a4b439991b4d2307c9c2"

#rpc
ETH_RPC='["https://go.getblock.io/aefd01aa907c4805ba3c00a9e5b48c6b","https://eth.llamarpc.com","https://eth.drpc.org"]'
ETH_RPC_TEST_NET='["https://sepolia.infura.io/v3/","https://rpc2.sepolia.org/","https://rpc-sepolia.rockx.com/"]'
BTC_RPC='["https://blockchain.info/rawtx/{hash}","https://blockstream.info/api/tx/{hash}","https://api.blockcypher.com/v1/btc/main/txs/{hash}"]'
BTC_RPC_TEST_NET= '["https://blockstream.info/testnet/api/tx/{hash}","https://mempool.space/testnet/api/tx/{hash}"]'
CKB_RPC='["https://mainnet-api.explorer.nervos.org/api/v1/transactions/{hash}"]'
CKB_RPC_TEST_NET = '["https://testnet-api.explorer.nervos.org/api/v1/transactions/{hash}"]'

BTC_FEE_RPC="https://api.blockchain.info/mempool/fees"

# Mail Configuration
MAIL_HOST=""
MAIL_PORT=""
MAIL_IGNORETLS=""
MAIL_SECURE=""
MAIL_REQUIRETLS=""
MAIL_USER=""
MAIL_PASSWORD=""
MAIL_DEFAULT_NAME=""
MAIL_DEFAULT_EMAIL=""

## LULU Service
LULU_API_BASE_URI="https://api.sandbox.lulu.com"
LULU_API_CLIENT_ID="b2b30393-f9d8-45ee-b111-2974b6eca98b"
LULU_API_CLIENT_SECRET="Vv1fEXp2gU3sUZMx6rrRMBljkq0eK0dG"

## Stripe
STRIPE_SECRET_KEY="***********************************************************************************************************"
STRIPE_WEBHOOK_SECRET=

AWS_BUCKET=""
AWS_REGION=""
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""

AUDIT_DAYS= "5"
EARNINGS_CONFIG= '{
    "GOLD":{"author":"80","platform":"20","gold":"0","silver":"0","copper":"0"},
    "SILVER":{"author":"20","platform":"20","gold":"60","silver":"0","copper":"0"},
    "COPPER":{"author":"20","platform":"20","gold":"24","silver":"36","copper":"0"},
    "BLUE":{"author":"20","platform":"20","gold":"20","silver":"20","copper":"20"}
}'
LEVEL_ICON_CONFIG= '{
    "GOLD":"btcfs://dd88b5cd2e8e8b1f720a2c8c65ba522310321eb5cfc7bdd590e910dd2c39a54ei0",
    "SILVER":"btcfs://0ee95549f04ce092438c2e5a9ca8482604bfb1ad92132a138e7eb494916f5e09i0",
    "COPPER":"btcfs://7bbba50aba747b9fb184f6540eb7ab5e74d78a16d1081f430c2f3c9a3222d36di0",
    "BLUE":"btcfs://82f12bf2d9ed04e7e5098f10898eb6f22c79d2514505711d529bed6a8d26e816i0"
}'

## task 配置--true|false
IS_TASK_ENABLED=false