{"name": "book-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:seed": "prisma db seed", "db:studio": "prisma studio", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "postinstall": "prisma generate", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test,prisma}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.620.0", "@aws-sdk/s3-request-presigner": "^3.705.0", "@ckb-ccc/core": "^1.2.2", "@ckb-ccc/spore": "^1.0.7", "@ckb-lumos/codec": "^0.23.0", "@ckb-lumos/config-manager": "^0.23.0", "@ckb-lumos/lumos": "^0.23.0", "@exact-realty/multipart-parser": "^1.0.14", "@joyid/ckb": "^1.0.1", "@lay2/pw-core": "^0.3.23", "@nervosnetwork/ckb-sdk-utils": "^0.109.4", "@nestjs/apollo": "^12.2.0", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.3.10", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.3.10", "@nestjs/graphql": "^12.2.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.5", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.10", "@nestjs/platform-fastify": "^10.3.10", "@nestjs/schedule": "^4.1.0", "@nestjs/swagger": "^7.4.0", "@node-libraries/nest-apollo-server": "^0.0.4", "@nodeteam/nestjs-prisma-pagination": "^1.0.6", "@prisma/client": "^5.16.2", "@spore-sdk/core": "0.2.0-beta.8", "@superfaceai/passport-twitter-oauth2": "^1.2.4", "apollo-server-express": "^3.13.0", "asposepdfnodejs": "^24.12.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "bitcoinjs-lib": "^6.1.6", "bitcoinjs-message": "^2.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "combine-async-iterators": "^3.0.0", "dataloader": "^2.2.2", "ethers": "^6.13.1", "express-useragent": "^1.0.15", "graphql": "^16.9.0", "handlebars": "^4.7.8", "http-proxy-agent": "^7.0.2", "joi": "^17.13.3", "json5": "^2.2.3", "nestjs-prisma": "^0.23.0", "nodemailer": "^6.9.14", "passport": "^0.7.0", "passport-discord": "^0.1.4", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "stripe": "^16.2.0", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@nestjs/cli": "^10.4.2", "@nestjs/schematics": "^10.1.2", "@nestjs/testing": "^10.3.10", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.14.10", "@types/passport-jwt": "^4.0.1", "@types/passport-twitter": "^1.0.40", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.3.3", "prisma": "^5.16.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.2", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}