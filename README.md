## Description

Book service for SisuRace.

## Installation

```bash
$ pnpm install
```

## Running the app

```bash
# development
$ pnpm run start

# watch mode
$ pnpm run start:dev

# production mode
$ pnpm run start:prod
```

## Test

```bash
# unit tests
$ pnpm run test

# e2e tests
$ pnpm run test:e2e

# test coverage
$ pnpm run test:cov
```

## 模块架构

本项目采用 NestJS 框架，具有清晰的模块化架构。以下是主要模块的概述：

- `src/`: 应用源码根目录。
  - `common/`: 存放全局可用的公共组件，如工具函数、装饰器、DTO 等。
  - `config/`: 负责应用配置的管理。
  - `iam/`: 身份与访问管理 (IAM) 模块，处理认证和授权。
  - `mail/` & `mailer/`: 邮件服务相关模块，用于发送各类邮件。
  - `resources/`: 核心业务逻辑模块，按领域划分。
    - `author/`: 作者管理。
    - `books/`: 书籍管理。
    - `clusters/`: 集群管理: 对应DOB Cluster。
    - `crypto/`: 加密货币相关操作。
    - `dobs/`: 数字原创书籍（DOB）管理。
    - `earnings/`: 收益与收入管理。
    - `issue/`: 问题或工单管理。
    - `lulu/`: 与 Lulu（按需打印服务）的集成。
    - `payment/`: 支付处理。
    - `print-orders/`: 印刷订单管理。
    - `stripe/`: 与 Stripe 支付网关的集成。
    - `users/`: 用户管理。
    - `withdraw/`: 提现管理。
  - `task/`: 定时任务和后台作业模块。
- `prisma/`: 包含 Prisma schema 定义、数据库迁移和种子数据。

ER diagram:

```mermaid
erDiagram
    User
    Account }o--|| User : logins
    Discord o| -- || User : logins
    Google o| -- || User : logins
    Twitter o| -- || User : logins
    Wallet o| -- || User : logins

    Token }o -- || User : controls

    MagicLinkToken

    User || -- |{ Book : manages
    Book || -- o{ Contributor : contributed
    Book || -- |o Copyright : owned
    Copyright || -- |o Isbn  : identified
    Book || -- |o Specification : described
    Book || -- |o Price : priced

    Book || -- |o Cluster : onchained
    Cluster || -- o{ Dob : describes
    Dob || -- o{ DobLogs : recorded
    Cluster || -- o{ DobLogs : recorded

    Cluster || -- o{ DobOrder : requested

    User || -- o{ Payment : creates
    Book || -- o{ BookEarningsLog : recorded

    Isbn || -- |o IsbnFree : described

    User || -- o{ WithdrawalOrder : creates
    Issue

    User }o -- || Author : "follows?"
    BookSku


    Book || -- o{ WhiteList : purchased

    Book || -- o{ DobEarningLog : recorded
    Dob || -- o{ DobEarningLog : recorded

    GlobalConfig

    platformDobs
    Dob || -- |o EncryptedBookSource : recorded

    User || -- o{ PrintOrder : creates
    Book || -- o{ PrintOrder : requested
    Dob || -- o{ PrintOrder : consumed

```
