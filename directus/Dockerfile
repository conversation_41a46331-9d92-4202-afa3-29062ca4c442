ARG DOCKER_DIRECTUS_VERSION=10.13.1

FROM directus/directus:${DOCKER_DIRECTUS_VERSION} AS builder

USER root
RUN corepack enable \
    && corepack prepare pnpm@9.6.0 --activate \
    # Currently required, we'll probably address this in the base image in future release
    && chown node:node /directus

WORKDIR /build
ENV NODE_ENV=development


FROM directus/directus:${DOCKER_DIRECTUS_VERSION}

USER root
RUN corepack enable \
    && corepack prepare pnpm@9.6.0 --activate \
    # Currently required, we'll probably address this in the base image in future release
    && chown node:node /directus

USER node
# RUN pnpm config set registry https://registry.npmmirror.com
# RUN pnpm config set sharp_binary_host "https://npmmirror.com/mirrors/sharp"
# RUN pnpm config set sharp_libvips_binary_host "https://npmmirror.com/mirrors/sharp-libvips"

RUN pnpm install directus-extension-board-layout
RUN pnpm install directus-extension-grid-layout
#RUN pnpm install directus-extension-comment-interface
RUN pnpm install directus-extension-computed-interface
RUN pnpm install directus-extension-currency-interface
RUN pnpm install directus-extension-masked-interface
RUN pnpm install directus-extension-vgrid-interface
RUN pnpm install directus-extension-editorjs
RUN pnpm install directus-extension-field-actions
RUN pnpm install directus-extension-sparkline-display
#RUN pnpm install directus-extension-schema-management-module
RUN pnpm install directus-extension-randomized-endpoint