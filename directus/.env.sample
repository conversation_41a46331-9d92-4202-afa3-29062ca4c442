####################################################################################################
### Database

# All DB_* environment variables are passed to the connection configuration of a Knex instance.
# Based on your project's needs, you can extend the DB_* environment variables with any config
# you need to pass to the database instance.

DB_CLIENT=pg
DB_HOST=postgres
DB_PORT=5432
DB_DATABASE=book
DB_USER=book
DB_PASSWORD=book

# Directus other settings


####################################################################################################
### Security
TELEMETRY=false
KEY=255d861b-5ea1-5996-9aa3-922530ec40b1
SECRET=6116487b-cda1-52c2-b5b5-c8022c45e263
REFRESH_TOKEN_COOKIE_NAME=silentberry_refresh_token
SESSION_COOKIE_NAME=silentberry_session_token

# LOG_LEVEL=debug
WEBSOCKETS_ENABLED=false
CORS_ENABLED=true

EMAIL_TRANSPORT=smtp
EMAIL_FROM=
EMAIL_SMTP_HOST=smtp.exmail.qq.com
EMAIL_SMTP_PORT=465
EMAIL_SMTP_USER=
EMAIL_SMTP_PASSWORD=
EMAIL_SMTP_SECURE=true


PUBLIC_URL=http://localhost:8055
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=d1r3ctu8

ASSETS_CONTENT_SECURITY_POLICY_DIRECTIVES__MEDIA_SRC="array:'self',https://api.silentberry.co"

####################################################################################################
### Token
ACCESS_TOKEN_TTL="1d"
REFRESH_TOKEN_TTL="7d"

####################################################################################################
### File Storage

# A CSV of storage locations (eg: local,digitalocean,amazon) to use. You can use any names you'd like for these keys ["local"]
STORAGE_LOCATIONS="local"
STORAGE_LOCAL_DRIVER="local"
STORAGE_LOCAL_ROOT="./uploads"

####################################################################################################
### Cache
CACHE_ENABLED=true
CACHE_STORE=redis
REDIS=redis://cache:6379
CACHE_AUTO_PURGE=true
CACHE_PERMISSIONS=false

RATE_LIMITER_STORE=redis
SYNCHRONIZATION_STORE=redis
MESSENGER_STORE=redis

####################################################################################################
### Extensions

# Path to your local extensions folder ["./extensions"]
EXTENSIONS_PATH="./extensions"

# Automatically reload extensions when they have changed [false]
EXTENSIONS_AUTO_RELOAD=true
