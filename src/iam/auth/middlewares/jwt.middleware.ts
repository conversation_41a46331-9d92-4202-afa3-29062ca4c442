import { Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { JwtService } from '@nestjs/jwt';
import { NextFunction } from 'express';

@Injectable()
export class JwtMiddleware implements NestMiddleware {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const token = this.getTokenFromHeader(req);

    if (token) {
      const payload = await this.jwtService
        .verifyAsync(token, {
          secret: this.configService.get<string>('JWT_SECRET'),
        })
        .catch((e) => {
          console.log('JWT_ERROR', e);
        });

      req['user'] = payload;
    }

    next();
  }

  private getTokenFromHeader(request: Request): string | undefined {
    // eslint-disable-next-line
    // @ts-ignore
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
