import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Redirect,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthGuard } from '@nestjs/passport';
import { ApiBody, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CryptoService } from 'src/resources/crypto/crypto.service';
import { CryptoSignDto } from 'src/resources/crypto/dto/crypto-sign.dto';
import { UsersService } from 'src/resources/users/users.service';
import { AuthService } from './auth.service';
import { Public } from './decorators/public.decorators';
import { LoginDto } from './dtos/login.dto';
import { MagicLinkDto, MagicLinkLoginDto } from './dtos/magic-link.dto';
import { AuthEntity } from './entities/auth.entity';

@Controller('auth')
@ApiTags('auth')
@Public()
export class AuthController {
  frontend_url: string;
  constructor(
    private authService: AuthService,
    private readonly cryptoService: CryptoService,
    private userService: UsersService,
    private config: ConfigService,
  ) {
    this.frontend_url = config.get<string>('FRONTEND_BASE_URL');
  }

  @Post('login')
  @ApiOperation({
    summary: '邮箱登录',
    description: '用电子邮件和密码登录用户',
  })
  @ApiBody({ type: LoginDto })
  @ApiOkResponse({ type: AuthEntity })
  @UseGuards(AuthGuard('local'))
  login(@Req() req) {
    return this.authService.createAuthToken(req.user);
  }

  @Post('magicLink')
  @ApiOperation({
    summary: '发送邮箱验证码',
  })
  @ApiBody({ type: MagicLinkDto })
  async magicLink(@Req() req, @Body() magicLinkDto: MagicLinkDto) {
    await this.authService.sendAuthCode(req.headers, magicLinkDto);
    return { code: 0, message: 'OK' };
  }

  @Post('magicLinkLogin')
  @ApiOperation({
    summary: '登录 电子邮件和验证码。',
  })
  @ApiBody({ type: MagicLinkLoginDto })
  @ApiOkResponse({ type: AuthEntity })
  async magicLinkLogin(@Body() magicLinkLoginDto: MagicLinkLoginDto) {
    const user = await this.authService.magicLoginValidate(
      magicLinkLoginDto,
      true,
    );
    return this.authService.createAuthToken(user);
  }

  @Get('google')
  @ApiOperation({
    summary: 'Google 登录',
    description: '将重定向到谷歌oauth2页面',
  })
  @UseGuards(AuthGuard('google'))
  async googleLogin(): Promise<void> {
    return;
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  @ApiOperation({
    summary: 'Google 登录回调接口',
  })
  @Redirect('/oauth', 302)
  async googleLoginCallback(@Req() req) {
    const { accessToken } = await this.authService.createAuthToken(req.user);
    return { url: `${this.frontend_url}?token=${accessToken}` };
  }

  @Get('twitter')
  @ApiOperation({
    summary: 'Twitter 登录',
    description: '将重定向到twitter oauth2页面',
  })
  @UseGuards(AuthGuard('twitter'))
  async twitterLogin() {
    return;
  }

  @Get('twitter/callback')
  @UseGuards(AuthGuard('twitter'))
  @ApiOperation({
    summary: 'twitter 登录回调接口',
  })
  @Redirect('/oauth', 302)
  async twitterLoginCallback(@Req() req) {
    const { accessToken } = await this.authService.createAuthToken(req.user);
    return { url: `${this.frontend_url}?token=${accessToken}` };
  }

  @Get('discord')
  @ApiOperation({
    summary: 'Discord 登录',
    description: '将重定向到discord oauth2页面',
  })
  @UseGuards(AuthGuard('discord'))
  async discordLogin() {
    return;
  }

  @Get('discord/callback')
  @UseGuards(AuthGuard('discord'))
  @ApiOperation({
    summary: 'discord 登录回调接口',
  })
  @Redirect('/oauth', 302)
  async discordLoginCallback(@Req() req) {
    const { accessToken } = await this.authService.createAuthToken(req.user);
    return {
      url: `${this.frontend_url}?token=${accessToken}`,
    };
  }

  @Get('signKey/:address')
  @Public()
  @ApiOperation({
    summary: '钱包登录 [获取签名字符串]',
    description: '钱包登录步骤1:获取签名所需的随机密钥',
  })
  getSignKey(@Param('address') address: string) {
    return this.cryptoService.getSignKey(address);
  }

  @Post('verifySign')
  @Public()
  @ApiOperation({
    summary: '钱包登录 [验证签名]',
    description:
      '钱包登录步骤2:使用钱包签名结果进行钱包登录，返回:登录成功令牌',
  })
  @ApiOkResponse({ type: AuthEntity })
  async verifySignLogin(@Body() key: CryptoSignDto) {
    const user = await this.userService.createForWallet(key);
    return this.authService.createAuthToken(user);
  }
}
