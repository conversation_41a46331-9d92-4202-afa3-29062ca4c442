import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { PrismaService } from 'nestjs-prisma';
// eslint-disable-next-line
// @ts-ignore
import { Strategy } from 'passport-discord';

@Injectable()
export class DiscordStrategy extends PassportStrategy(Strategy, 'discord') {
  constructor(
    config: ConfigService,
    private prisma: PrismaService,
  ) {
    super({
      clientID: config.get<string>('DISCORD_CLIENT_ID'),
      clientSecret: config.get<string>('DISCORD_CLIENT_SECRET'),
      callbackURL: `${config.get<string>('BASE_URL')}/auth/discord/callback`,
      scope: ['identify', 'email'],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any) {
    // check if there's a discord user
    const discordUser = await this.prisma.discord.findUnique({
      where: { id: profile.id },
      include: {
        user: true,
      },
    });

    if (discordUser) {
      return discordUser.user;
    }

    //If not, get Discord user profile and create a new user
    const user = await this.prisma.user.create({
      data: {
        name: profile.username,
        discord: { create: { accessToken, refreshToken, ...profile } },
      },
      include: { discord: true, twitter: true, google: true },
    });
    return user;
  }
}
