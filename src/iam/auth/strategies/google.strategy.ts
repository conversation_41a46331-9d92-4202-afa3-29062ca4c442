import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { PrismaService } from 'nestjs-prisma';
import { Profile, Strategy } from 'passport-google-oauth20';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(
    config: ConfigService,
    private prisma: PrismaService,
  ) {
    super({
      clientID: config.get<string>('GOOGLE_CLIENT_ID'),
      clientSecret: config.get<string>('GOOGLE_CLIENT_SECRET'),
      callbackURL: `${config.get<string>('BASE_URL')}/auth/google/callback`,
      scope: ['email', 'profile'],
    });
  }

  async validate(
    _accessToken: string,
    _refreshToken: string,
    profile: Profile,
  ) {
    const { id, displayName, emails, photos } = profile;
    const googleUser = await this.prisma.google.findUnique({
      where: {
        id,
      },
      include: {
        user: true,
      },
    });

    if (googleUser) {
      return googleUser.user;
    }

    const user = await this.prisma.user.create({
      data: {
        name: displayName,
        email: emails[0].value,
        avatar: photos[0]?.value,
        emailConfirmed: emails[0]?.verified,
        google: {
          create: {
            id,
            displayName,
            email: emails[0]?.value,
            avatar: photos[0]?.value,
            verified: emails[0]?.verified,
          },
        },
      },
    });

    return user;
  }
}
