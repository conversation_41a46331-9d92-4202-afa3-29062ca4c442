import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { PrismaService } from 'nestjs-prisma';
import { Strategy } from '@superfaceai/passport-twitter-oauth2';

@Injectable()
export class TwitterStrategy extends PassportStrategy(Strategy, 'twitter') {
  constructor(
    config: ConfigService,
    private prisma: PrismaService,
  ) {
    super({
      clientID: config.get('TWITTER_CLIENT_ID'),
      clientSecret: config.get('TWITTER_CLIENT_SECRET'),
      clientType: 'confidential',
      callbackURL: `${config.get('BASE_URL')}/auth/twitter/callback`,
      scope: ['tweet.read', 'users.read', 'offline.access', 'like.read'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
  ): Promise<any> {
    const { name, profile_image_url, username, id } = profile._json;
    const twitterUser = await this.prisma.twitter.findUnique({
      where: { id: id },
      include: {
        user: true,
      },
    });

    if (twitterUser) {
      return twitterUser.user;
    }

    //If not a user create user profile
    const newUser = await this.prisma.user.create({
      data: {
        name: name,
        twitter: {
          create: {
            name,
            profileImageUrl: profile_image_url,
            username,
            id,
            accessToken,
            refreshToken,
          },
        },
      },
      include: { discord: true, twitter: true, google: true },
    });

    return newUser;
  }
}
