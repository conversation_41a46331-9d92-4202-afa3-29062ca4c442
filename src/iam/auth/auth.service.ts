import {
  BadRequestException,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { User } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import * as useragent from 'express-useragent';
import { PrismaService } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { MailService } from 'src/mail/mail.service';
import { TokenService } from 'src/resources/users/token.service';
import { MagicLinkDto, MagicLinkLoginDto } from './dtos/magic-link.dto';
import { AuthEntity } from './entities/auth.entity';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private tokenService: TokenService,
    private configService: ConfigService,
    private stringService: StringService,
    private mailService: MailService,
  ) {}

  async sendAuthCode(headers: string, magicLinkDto: MagicLinkDto) {
    const code = this.stringService.randomNum(6);
    await this.prisma.magicLinkToken.upsert({
      where: {
        email: magicLinkDto.email,
      },
      create: {
        email: magicLinkDto.email,
        token: code,
      },
      update: {
        email: magicLinkDto.email,
        token: code,
        createdAt: new Date(),
      },
    });
    const agent = useragent.parse(headers['user-agent']);
    return await this.mailService.magicLink({
      to: magicLinkDto.email,
      data: {
        code,
        expired: '20 minutes',
        browser: agent.browser,
        os: agent.os,
      },
    });
  }

  async magicLoginValidate(
    magicLinkLoginDto: MagicLinkLoginDto,
    isCreate = true,
  ) {
    const verifiedToken = await this.prisma.magicLinkToken.findFirst({
      where: {
        email: magicLinkLoginDto.email,
        token: magicLinkLoginDto.token,
      },
    });

    if (!verifiedToken) {
      throw new BadRequestException('Invalid authentication code.');
    }
    const now = new Date();
    if (
      now.getUTCMilliseconds() >
      new Date(verifiedToken.createdAt).getUTCMilliseconds() + 15 * 60 * 1000
    ) {
      throw new BadRequestException('Authentication code expired.');
    }

    // find or create user
    let user = await this.prisma.user.findFirst({
      where: { email: magicLinkLoginDto.email },
    });
    if (!user && isCreate) {
      const min = Math.pow(10, 4) - 1;
      const max = Math.pow(10, 5) - 1;
      const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
      user = await this.prisma.user.create({
        data: {
          name: 'silentberry' + randomNumber,
          email: magicLinkLoginDto.email,
          emailConfirmed: true,
        },
      });
    }

    return user;
  }

  async validateUser(email: string, password: string): Promise<User> {
    const user = await this.prisma.user.findFirst({
      where: { email },
    });
    if (!user) throw new UnauthorizedException('Invalid email or password.');

    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid)
      throw new UnauthorizedException('Invalid email or password.');

    return user;
  }

  async createAuthToken(user: User): Promise<AuthEntity> {
    const refreshToken =
      await this.tokenService.createRefreshTokenForUser(user);
    const accessToken = this.jwtService.sign({
      sub: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      avatar: user.avatar,
      emailConfirmed: user.emailConfirmed,
      locked: user.locked,
    });
    return {
      accessToken,
      refreshToken,
    };
  }
}
