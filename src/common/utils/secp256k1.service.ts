import { Injectable } from '@nestjs/common';
import { Hash, hd, helpers, HexString, RPC, Script } from '@ckb-lumos/lumos';
import { bytes, number } from '@ckb-lumos/codec';
import {
  anyoneCanPay,
  secp256k1Blake160,
} from '@ckb-lumos/lumos/common-scripts';
import {
  defaultEmptyWitnessArgs,
  isScriptIdEquals,
  isScriptValueEquals,
  SporeConfig,
  updateWitnessArgs,
} from '@spore-sdk/core';

//secp256k1
@Injectable()
export class Secp256k1WalletService {
  createSecp256k1Wallet(privateKey: HexString, config: SporeConfig) {
    const Secp256k1Blake160 = config.lumos.SCRIPTS.SECP256K1_BLAKE160!;
    const AnyoneCanPay = config.lumos.SCRIPTS.ANYONE_CAN_PAY!;

    // blake160
    const blake160 = hd.key.privateKeyToBlake160(privateKey);
    const lock: Script = {
      codeHash: Secp256k1Blake160.CODE_HASH,
      hashType: Secp256k1Blake160.HASH_TYPE,
      args: blake160,
    };

    // lumos sdk create
    const address = helpers.encodeToAddress(lock, {
      config: config.lumos,
    });

    function removeHexPrefix(str: string): string {
      return str.startsWith('0x') ? str.slice(2) : str;
    }

    function isAcpLockMatches(
      lock: Script,
      blake160: Hash,
      config: SporeConfig,
    ): boolean {
      const AnyoneCanPay = config.lumos.SCRIPTS.ANYONE_CAN_PAY!;
      const acpScriptId = {
        codeHash: AnyoneCanPay.CODE_HASH,
        hashType: AnyoneCanPay.HASH_TYPE,
      };

      return (
        isScriptIdEquals(lock, acpScriptId) && lock.args.startsWith(blake160)
      );
    }

    // ACP lock
    function createAcpLock(props?: { minCkb?: number }): Script {
      const minCkb = props?.minCkb;
      const minimalCkb =
        minCkb !== void 0
          ? bytes.hexify(number.Uint8.pack(minCkb as number))
          : '';
      return {
        codeHash: AnyoneCanPay.CODE_HASH,
        hashType: AnyoneCanPay.HASH_TYPE,
        args: `${blake160}${removeHexPrefix(minimalCkb)}`,
      };
    }

    function signMessage(message: HexString): Hash {
      return hd.key.signRecoverable(message, privateKey);
    }

    function signTransaction(
      txSkeleton: helpers.TransactionSkeletonType,
    ): helpers.TransactionSkeletonType {
      const signingEntries = txSkeleton.get('signingEntries');
      const signatures = new Map<HexString, Hash>();
      const inputs = txSkeleton.get('inputs');

      let witnesses = txSkeleton.get('witnesses');
      for (let i = 0; i < signingEntries.size; i++) {
        const entry = signingEntries.get(i)!;
        if (entry.type === 'witness_args_lock') {
          const input = inputs.get(entry.index);
          if (!input) {
            continue;
          }

          if (
            !isScriptValueEquals(input.cellOutput.lock, lock) &&
            !isAcpLockMatches(input.cellOutput.lock, blake160, config)
          ) {
            continue;
          }
          if (!signatures.has(entry.message)) {
            const newSignature = signMessage(entry.message);
            signatures.set(entry.message, newSignature);
          }

          const signature = signatures.get(entry.message)!;
          const witness = witnesses.get(entry.index, defaultEmptyWitnessArgs);
          witnesses = witnesses.set(
            entry.index,
            updateWitnessArgs(witness, 'lock', signature),
          );
        }
      }

      return txSkeleton.set('witnesses', witnesses);
    }

    async function signAndSendTransaction(
      txSkeleton: helpers.TransactionSkeletonType,
    ): Promise<Hash> {
      // Env
      const rpc = new RPC(config.ckbNodeUrl);

      // Sign transaction
      txSkeleton = secp256k1Blake160.prepareSigningEntries(txSkeleton, {
        config: config.lumos,
      });
      txSkeleton = anyoneCanPay.prepareSigningEntries(txSkeleton, {
        config: config.lumos,
      });
      txSkeleton = signTransaction(txSkeleton);

      // Convert to Transaction
      const tx = helpers.createTransactionFromSkeleton(txSkeleton);

      // Send transaction
      return await rpc.sendTransaction(tx, 'passthrough');
    }

    return {
      lock,
      address,
      signMessage,
      signTransaction,
      signAndSendTransaction,
      createAcpLock,
    };
  }
}
