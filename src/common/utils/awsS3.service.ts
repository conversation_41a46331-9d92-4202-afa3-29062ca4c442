// src/s3/s3.service.ts
import {
  GetObjectCommand,
  HeadObjectCommand,
  HeadObjectCommandOutput,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ReadStream } from 'fs';

@Injectable()
export class AwsS3Service {
  private s3Client: S3Client;

  constructor(private configService: ConfigService) {
    this.s3Client = new S3Client({
      region: configService.get<string>('S3_REGION'),
      credentials: {
        accessKeyId: configService.get<string>('S3_ACCESS_KEY_ID'),
        secretAccessKey: configService.get<string>('S3_SECRET_ACCESS_KEY'),
      },
      endpoint: configService.get<string>('S3_CUSTOM_ENDPOINT'),
      bucketEndpoint: true,
    });
  }

  async checkObjectExistence(key: string): Promise<HeadObjectCommandOutput> {
    try {
      const bucket = this.configService.get<string>('S3_BUCKET_NAME');
      const command = new HeadObjectCommand({
        Bucket: bucket,
        Key: key,
      });

      const headData = await this.s3Client.send(command);
      return headData;
    } catch (err) {
      if (err.name === 'NotFound') {
        return null;
      } else {
        throw err;
      }
    }
  }

  // 获取s3预下载文件
  async generatePresignedUrl(bucketName, fileName, expiration = 60) {
    if (!bucketName) {
      bucketName = this.configService.get<string>('S3_BUCKET_NAME');
    }
    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileName,
    });

    try {
      // 获取预签名 URL
      const url = await getSignedUrl(this.s3Client, command, {
        expiresIn: expiration,
      });
      return url;
    } catch (error) {
      console.error('生成预签名 URL 时出错:', error);
      throw error;
    }
  }

  async putObject(
    bucketName: string | null,
    key: string,
    body: string | Buffer | Uint8Array | Blob | ReadableStream | ReadStream,
  ) {
    if (!bucketName) {
      bucketName = this.configService.get<string>('S3_BUCKET_NAME');
    }
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: body,
    });
    await this.s3Client.send(command);
    return true;
  }
}
