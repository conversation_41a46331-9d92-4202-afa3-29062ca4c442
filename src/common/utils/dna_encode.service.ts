import { randomInt } from 'crypto';
import { parse } from 'json5';
import { Injectable } from '@nestjs/common';

interface TraitSchema {
  name: string;
  offset: number;
  len: number;
  pattern: Pattern;
  args?: Array<number | string>;
}

interface EncodeDNA {
  dna: string;
}

enum Pattern {
  RawNumber = 'RawNumber',
  RawString = 'RawString',
  Utf8 = 'Utf8',
  Uri = 'Uri',
  Range = 'Range',
  Options = 'Options',
}

class EncodeError extends Error {
  static EncodeBadData = new EncodeError('EncodeBadData');
  static EncodeBadRawNumberFormat = new EncodeError('EncodeBadRawNumberFormat');
  static EncodeBadRawStringFormat = new EncodeError('EncodeBadRawStringFormat');
  static EncodeBadUtf8Format = new EncodeError('EncodeBadUtf8Format');
  static EncodeBadDataLength = new EncodeError('EncodeBadDataLength');
  static EncodeBadRangeFormat = new EncodeError('EncodeBadRangeFormat');
  static EncodeInvalidRangeArgs = new EncodeError('EncodeInvalidRangeArgs');
  static EncodeMissingOptionArgs = new EncodeError('EncodeMissingOptionArgs');

  constructor(message: string) {
    super(message);
    this.name = 'EncodeError';
  }
}

@Injectable()
export class DNAService {
  constructor() {}

  encodeDnaPattern(
    traitsBase: TraitSchema[],
    mapObj: Record<string, any>,
  ): EncodeDNA {
    let endLen = 0;
    let result: number[] = [];
    for (const value of traitsBase) {
      if ((endLen === 0 && value.offset !== 0) || endLen > value.offset) {
        continue;
      }
      endLen += value.len;
      const obj = mapObj[value.name];
      let resOne: number[];
      switch (value.pattern) {
        case Pattern.RawNumber:
          if (obj === undefined) throw EncodeError.EncodeBadData;
          const objU64 = Number(obj);
          if (isNaN(objU64)) throw EncodeError.EncodeBadRawNumberFormat;
          resOne = Array.from(
            Buffer.from(
              objU64
                .toString(16)
                .padStart(objU64.toString(16).length % 2 === 0 ? 0 : 2, '0'),
              'hex',
            ),
          );
          break;

        case Pattern.RawString:
          if (obj === undefined) throw EncodeError.EncodeBadData;
          const objStr = String(obj);
          if (objStr.length > value.len) throw EncodeError.EncodeBadDataLength;
          resOne = Array.from(Buffer.from(objStr, 'hex'));
          break;

        case Pattern.Utf8:
          if (obj === undefined) throw EncodeError.EncodeBadData;
          let utf8Str = String(obj);
          if (utf8Str.length > value.len) {
            utf8Str = utf8Str.substring(0, value.len);
          }
          resOne = Array.from(Buffer.from(utf8Str, 'utf8'));
          break;

        case Pattern.Range:
          if (!value.args || value.args.length !== 2)
            throw EncodeError.EncodeBadRangeFormat;
          const lower = Number(value.args[0]);
          const upper = Number(value.args[1]);
          if (isNaN(lower) || isNaN(upper) || upper <= lower)
            throw EncodeError.EncodeInvalidRangeArgs;
          const rangeValue = randomInt(lower, upper);
          resOne = Array.from(
            Buffer.from(
              rangeValue
                .toString(16)
                .padStart(
                  rangeValue.toString(16).length % 2 === 0 ? 0 : 2,
                  '0',
                ),
              'hex',
            ),
          );
          break;

        case Pattern.Options:
          if (!value.args || obj === undefined)
            throw EncodeError.EncodeMissingOptionArgs;
          // const randomIndex = randomInt(0, value.args.length);
          resOne = Array.from(
            Buffer.from(
              obj
                .toString(16)
                .padStart(obj.toString(16).length % 2 === 0 ? 0 : 2, '0'),
              'hex',
            ),
          );
          break;

        default:
          throw new Error('Unknown pattern type');
      }
      if (resOne.length > value.len) {
        resOne = resOne.slice(0, value.len);
      } else {
        while (resOne.length < value.len) {
          resOne.push(0);
        }
      }
      result = result.concat(resOne);
    }
    return {
      dna: result.map((byte) => byte.toString(16).padStart(2, '0')).join(''),
    };
  }

  // Decode Trait Schema Function
  decodeTraitSchema(traitsPool: any): TraitSchema[] {
    if (!Array.isArray(traitsPool)) {
      throw new EncodeError('ParseInvalidTraitsBase');
    }

    return traitsPool.map((schema: any) => {
      if (!Array.isArray(schema) || schema.length < 5) {
        throw new EncodeError('SchemaInsufficientElements');
      }
      const name = schema[0];
      const type_ = schema[1];
      if (typeof name !== 'string' || typeof type_ !== 'string') {
        throw new EncodeError('SchemaInvalidName or SchemaInvalidType');
      }

      let offset: number;
      if (typeof schema[2] === 'string') {
        offset = parseInt(schema[2], 10);
        if (isNaN(offset)) {
          throw new EncodeError('SchemaInvalidOffset');
        }
      } else if (typeof schema[2] === 'number') {
        offset = schema[2];
      } else {
        throw new EncodeError('SchemaInvalidOffset');
      }

      let len: number;
      if (typeof schema[3] === 'string') {
        len = parseInt(schema[3], 10);
        if (isNaN(len)) {
          throw new EncodeError('SchemaInvalidLen');
        }
      } else if (typeof schema[3] === 'number') {
        len = schema[3];
      } else {
        throw new EncodeError('SchemaInvalidLen');
      }

      const patternStr = schema[4];
      if (typeof patternStr !== 'string') {
        throw new EncodeError('SchemaInvalidPattern');
      }

      const pattern: Pattern = (() => {
        switch (patternStr) {
          case 'options':
            return Pattern.Options;
          case 'rawNumber':
            return Pattern.RawNumber;
          case 'rawString':
            return Pattern.RawString;
          case 'utf8':
            return Pattern.Utf8;
          case 'range':
            return Pattern.Range;
          case 'uri':
            return Pattern.Uri;
          default:
            throw new EncodeError('SchemaPatternMismatch');
        }
      })();

      let args: Array<number | string> | undefined = undefined;
      if (schema.length > 5) {
        const argsField = schema[5];
        if (typeof argsField === 'string') {
          try {
            args = parse(argsField);
            if (!Array.isArray(args)) {
              throw new EncodeError('SchemaInvalidArgs');
            }
          } catch (e) {
            throw new EncodeError('SchemaInvalidArgs');
          }
        } else if (Array.isArray(argsField)) {
          args = argsField;
        } else {
          throw new EncodeError('SchemaInvalidArgs');
        }
      }

      return {
        name,
        type_,
        offset,
        len,
        pattern,
        args,
      };
    });
  }
}
