import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDate, IsInt, IsOptional, Matches } from 'class-validator';

/**
 * Class contains fields to perform common Read operation
 * (Paging, Search, Sort)
 */
export class QueryTimeParamDto {
  // Pagination
  @ApiProperty({
    required: false,
    description: 'Skip number of items.',
  })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  @Expose()
  skip?: number = 0;

  @ApiProperty({
    required: false,
    description: 'Limit amount of items to be returned.',
  })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  @Expose()
  take?: number = 10;

  allowCount?: boolean = true; // if true, allow count all row in query

  // sort
  @ApiProperty({
    required: false,
    description:
      '按字段排序数据,_desc降序排序,_asc升序排序(默认将以时间倒叙排序)',
    example: 'updatedAt_desc',
  })
  @IsOptional()
  @Matches(/^\w+(_desc|_asc)+$/, {
    message: "'sort' must end with _desc or _asc",
  })
  @Expose()
  sort?: string = 'updatedAt_desc';

  @ApiProperty({
    required: false,
    name: 'startTime',
    description: 'start Time (date) ',
  })
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  @Expose({ name: 'startTime' })
  startTime?: Date;

  @ApiProperty({
    required: false,
    name: 'endTime',
    description: 'end Time (date) ',
  })
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  @Expose({ name: 'endTime' })
  endTime?: Date;
}
