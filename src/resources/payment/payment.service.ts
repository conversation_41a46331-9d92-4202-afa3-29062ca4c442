import { Injectable } from '@nestjs/common';
import { PaymentProvider, PaymentStatus, Prisma } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { StripeService } from '../stripe/stripe.service';
import { CreatePaymentDto } from './dto/create-payment.dto';

@Injectable()
export class PaymentService {
  constructor(
    private prisma: PrismaService,
    private stripeService: StripeService,
    private stringService: StringService,
  ) {}

  findByUserAndId(userId: string, id: string) {
    return this.prisma.payment.findUnique({
      where: { id, userId },
    });
  }

  async createPayment(createPaymentDto: CreatePaymentDto) {
    switch (createPaymentDto.provider) {
      case PaymentProvider.STRIPE:
        const payment = await this.prisma.payment.findFirst({
          where: {
            printOrder: { id: createPaymentDto.printOrderId ?? undefined },
            dobOrder: { id: createPaymentDto.dobOrderId ?? undefined },
            provider: PaymentProvider.STRIPE,
            status: PaymentStatus.PENDING,
          },
        });
        if (payment) {
          await this.stripeService.cancelIntent(payment.transactionId);
          await this.prisma.payment.update({
            where: { id: payment.id },
            data: { status: PaymentStatus.CANCELED },
          });
        }
        const amount = this.stringService.parseFloat(
          createPaymentDto.amount,
          2,
        );
        const intent = await this.stripeService.createPaymentIntent(
          amount * 100,
        );
        console.log(
          'intent',
          intent.amount,
          'amount',
          amount,
          'paymentDto',
          createPaymentDto.amount,
        );
        const createdata: Prisma.PaymentCreateInput = {
          transactionId: intent.id,
          currency: createPaymentDto.currency,
          amount: createPaymentDto.amount,
          user: { connect: { id: createPaymentDto.userId } },
        };
        console.log(intent, createdata, 'createPaymentIntent');
        if (createPaymentDto.printOrderId) {
          createdata.printOrder = {
            connect: { id: createPaymentDto.printOrderId },
          };
        } else if (createPaymentDto.dobOrderId) {
          createdata.dobOrder = {
            connect: { id: createPaymentDto.dobOrderId },
          };
        } else {
          break;
        }
        const newPayment = await this.prisma.$transaction(async (tx) => {
          //doborder-更新订单为STRIPE支付
          if (createPaymentDto.dobOrderId) {
            await tx.dobOrder.update({
              where: { id: createPaymentDto.dobOrderId },
              data: {
                network: 'USD',
                status: 'PAID',
              },
            });
          }
          // 2. update base info of the book, and return book
          return await tx.payment.create({
            data: createdata,
          });
        });

        return {
          metadata: {
            clientSecret: intent.client_secret,
          },
          ...newPayment,
        };
        break;
      default:
        break;
    }
  }
}
