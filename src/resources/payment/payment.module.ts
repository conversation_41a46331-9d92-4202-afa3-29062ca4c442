import { Module } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { LuluModule } from '../lulu/lulu.module';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { StripeModule } from '../stripe/stripe.module';

@Module({
  imports: [PrismaModule, StripeModule, LuluModule],
  controllers: [PaymentController],
  providers: [PaymentService, StringService],
  exports: [PaymentService],
})
export class PaymentModule {}
