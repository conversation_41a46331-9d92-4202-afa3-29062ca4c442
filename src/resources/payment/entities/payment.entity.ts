import { ApiProperty } from '@nestjs/swagger';
import { $Enums, Payment } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Exclude, Transform } from 'class-transformer';

export class PaymentEntity implements Payment {
  constructor({ ...data }: Partial<PaymentEntity>) {
    Object.assign(this, data);
  }

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  amount: Decimal;

  @ApiProperty()
  currency: $Enums.CURRENCY;

  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  transactionId: string;

  @ApiProperty({
    description: '状态[STRIPE:法币,CRYTPO:加密货币]',
  })
  provider: $Enums.PaymentProvider;

  @ApiProperty({
    description: '状态[PENDING:进行中,DONE:已完成,FAILED:失败,CANCELED:取消]',
  })
  status: $Enums.PaymentStatus;

  @Exclude()
  printOrderId?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
