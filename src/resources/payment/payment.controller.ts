import { Controller, Get, Param, Req } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { PaymentEntity } from './entities/payment.entity';
import { PaymentService } from './payment.service';

@ApiTags('payment')
@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '用id查找我的付款',
  })
  @ApiOkResponse({ type: PaymentEntity })
  async findMyPayment(@Req() req, @Param('id') id: string) {
    return new PaymentEntity(
      await this.paymentService.findByUserAndId(req.user.sub, id),
    );
  }
}
