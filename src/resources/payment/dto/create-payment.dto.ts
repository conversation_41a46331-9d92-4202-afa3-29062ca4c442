import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { IsDecimal, IsOptional } from 'class-validator';

export class CreatePaymentDto {
  @ApiProperty()
  userId: string;

  @ApiProperty({
    enum: $Enums.PaymentProvider,
    default: $Enums.PaymentProvider.STRIPE,
  })
  provider: $Enums.PaymentProvider = $Enums.PaymentProvider.STRIPE;

  @ApiProperty()
  currency: $Enums.CURRENCY = $Enums.CURRENCY.USD;

  @ApiProperty({
    default: '0.00',
  })
  @IsDecimal({ force_decimal: true })
  amount: string;

  @ApiProperty()
  @IsOptional()
  printOrderId?: string;

  @ApiProperty()
  @IsOptional()
  dobOrderId?: string;
}
