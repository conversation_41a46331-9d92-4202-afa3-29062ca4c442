import { ccc } from '@ckb-ccc/core';
import { createSpore } from '@ckb-ccc/spore';
// import { addressToScript } from '@nervosnetwork/ckb-sdk-utils';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  DnaLevel,
  EarningsType,
  PaymentProvider,
  PaymentStatus,
  PrintOrderStatus,
  Prisma,
} from '@prisma/client';
import {
  // bytifyRawString,
  // createSpore,
  predefinedSporeConfigs,
} from '@spore-sdk/core';
import { PrismaService } from 'nestjs-prisma';
import { Secp256k1WalletService } from 'src/common/utils/secp256k1.service';
import Stripe from 'stripe';

@Injectable()
export class StripeService {
  private logger = new Logger(StripeService.name);
  client: Stripe;

  private config_secp256k1_key;
  private config_network;
  private chain_network;

  constructor(
    private config: ConfigService,
    private prisma: PrismaService,
    private configService: ConfigService,
    private secp256k1Wallet: Secp256k1WalletService,
  ) {
    const secretKey = this.config.get<string>('STRIPE_SECRET_KEY');
    this.client = new Stripe(secretKey);
    this.chain_network = configService.get<string>('BLOCK_CHAIN_NETWORK');
    this.config_secp256k1_key = configService.get<string>(
      'SECP256K1_PRIVATE_KEY',
    );
    this.config_network =
      configService.get<string>('BLOCK_CHAIN_NETWORK') == 'Mainnet'
        ? predefinedSporeConfigs.Mainnet
        : predefinedSporeConfigs.Testnet;
  }

  async paymentIntentWebhook(
    event: Stripe.Event,
    headers: Record<string, string>,
  ) {
    const sig = headers['stripe-signature'];
    const webhookSecret = this.config.get<string>('STRIPE_WEBHOOK_SECRET');

    try {
      event = this.client.webhooks.constructEvent(
        event as any,
        sig,
        webhookSecret,
      );
    } catch (err) {
      throw new BadRequestException(`Webhook Error: ${err.message}`);
    }

    if (
      ![
        'payment_intent.succeeded',
        'payment_intent.canceled',
        'payment_intent.payment_failed',
      ].includes(event.type)
    ) {
      return;
    }
    const data = event.data.object as Stripe.PaymentIntent;
    const payment = await this.prisma.payment.findFirst({
      where: { transactionId: data.id, provider: PaymentProvider.STRIPE },
      include: {
        printOrder: true,
        dobOrder: true,
      },
    });
    switch (event.type) {
      case 'payment_intent.succeeded':
        await this.prisma.$transaction(async (tx) => {
          await tx.payment.update({
            where: { id: payment.id },
            data: { status: PaymentStatus.DONE },
          });
          // update order status
          if (payment.printOrder) {
            await tx.printOrder.update({
              where: {
                id: payment.printOrder.id,
              },
              data: {
                status: PrintOrderStatus.PAID,
              },
            });
          }
          // 更新doborder状态
          if (payment.dobOrder) {
            //暂时暂停信用卡回调创建dob
            // await this.createSporeForStripe(payment.dobOrder.id);
          }
        });
        break;
      case 'payment_intent.canceled':
        await this.prisma.$transaction(async (tx) => {
          await tx.payment.update({
            where: { id: payment.id },
            data: { status: PaymentStatus.CANCELED },
          });
          // 回填数量和阶段
          if (payment.dobOrder) {
            await this.callBackDobOrder(payment.dobOrder.id);
          }
        });
        break;
      case 'payment_intent.payment_failed':
        await this.prisma.payment.update({
          where: { id: payment.id },
          data: { status: PaymentStatus.FAILED },
        });
        // 回填数量和阶段
        if (payment.dobOrder) {
          await this.callBackDobOrder(payment.dobOrder.id);
        }
        break;
      default:
        this.logger.warn(`Unhandled event type ${event.type}`);
    }
  }

  async createPaymentIntent(amount: number) {
    const paymentIntent = await this.client.paymentIntents.create({
      amount,
      currency: 'USD',
      automatic_payment_methods: {
        enabled: true,
      },
    });

    return paymentIntent;
  }

  async cancelIntent(id: string) {
    return await this.client.paymentIntents.cancel(id);
  }

  // 最后的步骤 创建dob-USDT支付回调
  private async createSporeForStripe(dobOrderId: string) {
    //check dna order
    const dobOrder = await this.prisma.dobOrder.findUnique({
      where: {
        id: dobOrderId,
        status: PaymentStatus.PENDING,
      },
      include: {
        cluster: true,
      },
    });
    if (!dobOrder) return false;
    const client =
      this.chain_network == 'Mainnet'
        ? new ccc.ClientPublicMainnet()
        : new ccc.ClientPublicTestnet();
    // 获取用户ckb地址
    const payUser = await this.prisma.wallet.findUnique({
      where: { userId: dobOrder.payUserId },
    });
    let userLock: any;
    if (payUser && payUser.address) {
      userLock = (await ccc.Address.fromString(payUser.address, client)).script;
    }
    const wallet = new ccc.SignerCkbPrivateKey(
      client,
      this.config_secp256k1_key,
    );
    const walletObj = await wallet.getRecommendedAddressObj();
    let toLock: any, isAdmin: boolean, ownerAddress: string;
    if (!userLock) {
      toLock = walletObj.script;
      isAdmin = true;
      ownerAddress = walletObj.toString();
    } else {
      toLock = userLock;
      isAdmin = false;
      ownerAddress = payUser.address;
    }
    const { tx, id: sporeId } = await createSpore({
      signer: wallet,
      to: toLock,
      clusterMode: 'lockProxy',

      data: {
        contentType: 'dob/1',
        content: ccc.bytesFrom(JSON.stringify({ dna: dobOrder.dna }), 'utf8'),
        clusterId: dobOrder.cluster.clusterId,
      },
    });
    await tx.completeFeeBy(wallet);
    const signedTx = await wallet.signTransaction(tx);
    const txHash = signedTx.hash();
    console.log(signedTx.stringify(), txHash, sporeId, 'hashData');
    // dob list
    for (let i = 1; i <= dobOrder.amount; i++) {
      // 创建spore
      const spore: Prisma.DobCreateInput = {
        dobId: sporeId,
        ownerAddress: ownerAddress,
        isAdmin: isAdmin,
        cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60,
        utxoData: 'null',
        dna: dobOrder.dna,
        level: dobOrder.level,
        metadata: dobOrder.dnaData,
        txHash: txHash,
        withdrawn: 0,
        cluster: {
          connect: {
            id: dobOrder.cluster.id,
          },
        },
        user: {
          connect: {
            id: dobOrder.payUserId,
          },
        },
        dobOrder: {
          connect: {
            id: dobOrder.id,
          },
        },
      };
      await this.prisma.$transaction(async (tx) => {
        const dob = await tx.dob.create({ data: spore });
        // 更新spore 订单
        await tx.dobOrder.update({
          where: { id: dobOrder.id },
          data: {
            status: PaymentStatus.DONE,
            earningsType: EarningsType.PROCESSING,
            dob: { connect: { id: dob.id } },
          },
        });
      });
    }
    // 更新book 销量
    await this.prisma.book.update({
      where: { id: dobOrder.cluster.bookId },
      data: {
        sales: {
          increment: dobOrder.amount,
        },
      },
    });
    console.log('database success');
    //最后发起交易
    await wallet.sendTransaction(tx);
    console.log('sendTransaction(tx) success');
  }

  private async callBackDobOrder(dobOrderId: string) {
    //check dna order
    const dobOrder = await this.prisma.dobOrder.findUnique({
      where: {
        id: dobOrderId,
      },
      include: {
        cluster: true,
      },
    });
    const bookId = dobOrder.cluster.bookId;
    if (!dobOrder || !bookId) return false;
    const bookData = await this.prisma.book.findUnique({
      where: { id: bookId },
      select: { id: true, phase: true, phaseRemain: true },
    });
    // 更新price 剩余
    let priceUp: Prisma.PriceUpdateInput;
    if (dobOrder.level == DnaLevel.GOLD) {
      priceUp = {
        goldRemain: {
          increment: dobOrder.amount,
        },
      };
    } else if (dobOrder.level == DnaLevel.SILVER) {
      priceUp = {
        silverRemain: {
          increment: dobOrder.amount,
        },
      };
    } else if (dobOrder.level == DnaLevel.COPPER) {
      priceUp = {
        copperRemain: {
          increment: dobOrder.amount,
        },
      };
    } else if (dobOrder.level == DnaLevel.BLUE) {
      priceUp = {
        copperRemain: {
          decrement: dobOrder.amount,
        },
      };
    }
    if (priceUp) {
      await this.prisma.price.update({
        where: { bookId: bookId },
        data: priceUp,
      });
    }
    let bookUp: Prisma.BookUpdateInput;
    //如果是当前阶段 则增加剩余 如果不是则回退
    if (bookData.phase == dobOrder.level) {
      bookUp = {
        phaseRemain: {
          increment: dobOrder.amount,
        },
      };
    } else {
      let level = dobOrder.level;
      let remain = dobOrder.amount;
      // 兼容越级回退
      if (
        (dobOrder.level == 'SILVER' && bookData.phase == 'GOLD') ||
        (dobOrder.level == 'COPPER' &&
          (bookData.phase == 'GOLD' || bookData.phase == 'SILVER'))
      ) {
        level = bookData.phase;
        remain = bookData.phaseRemain;
      }
      bookUp = {
        phase: level,
        phaseRemain: remain,
      };
    }
    await this.prisma.book.update({
      where: { id: bookId },
      data: bookUp,
    });
  }
}
