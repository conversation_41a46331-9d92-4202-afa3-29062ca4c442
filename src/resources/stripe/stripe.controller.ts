import { Controller, Headers, Post, RawBody } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Public } from 'src/iam/auth/decorators/public.decorators';
import { StripeService } from './stripe.service';

@Controller('stripe')
@ApiTags('stripe')
@Public()
export class StripeController {
  constructor(private readonly stripeService: StripeService) {}

  @Post('webhooks')
  async getAll(
    @RawBody() body: any,
    @Headers() headers: Record<string, string>,
  ) {
    return this.stripeService.paymentIntentWebhook(body, headers);
  }
}
