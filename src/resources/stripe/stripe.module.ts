import { Module } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { <PERSON>eController } from './stripe.controller';
import { StripeService } from './stripe.service';
import { ConfigService } from '@nestjs/config';
import { Secp256k1WalletService } from 'src/common/utils/secp256k1.service';

@Module({
  imports: [PrismaModule],
  controllers: [StripeController],
  providers: [StripeService, ConfigService, Secp256k1WalletService],
  exports: [StripeService],
})
export class StripeModule {}
