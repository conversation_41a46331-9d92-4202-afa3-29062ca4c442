import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { AwsS3Service } from 'src/common/utils/awsS3.service';
import { Secp256k1WalletService } from 'src/common/utils/secp256k1.service';
import { StringService } from 'src/common/utils/string.service';
import { EncryptBookSourceTaskService } from 'src/task/encrypt-book-source.service';
import { ClustersService } from '../clusters/clusters.service';
import { LuluModule } from '../lulu/lulu.module';
import { BooksController } from './books.controller';
import { BooksService } from './books.service';

@Module({
  imports: [PrismaModule, LuluModule],
  controllers: [BooksController],
  providers: [
    StringService,
    BooksService,
    AwsS3Service,
    ClustersService,
    Secp256k1WalletService,
    EncryptBookSourceTaskService,
  ],
  exports: [BooksService],
})
export class BooksModule {}
