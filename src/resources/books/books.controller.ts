import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ApiPaging } from 'src/common/decorators/api-paging.decorator';
import { PagingQuery } from 'src/common/decorators/paging-query.decorator';
import { QueryParamDto } from 'src/common/dtos/query-param.dto';
import { Public } from 'src/iam/auth/decorators/public.decorators';
import { EncryptBookSourceTaskService } from 'src/task/encrypt-book-source.service';
import { DobOrderEntity } from '../dobs/entities/dobOrder.entity';
import { BooksService } from './books.service';
import { AudiencesConfig } from './config/audiences.config';
import { CategoryConfig } from './config/category.config';
import { ContributorConfig } from './config/contributor.config';
import { LanguageConfig } from './config/language.config';
import { PrintConfig } from './config/print.config';
import { BookCofing } from './config/type';
import { CheckBookDto } from './dto/check-book.dto';
import { CreateCopyrightDto, UpdateCopyrightDto } from './dto/copyright.dto';
import { CreateBookDto } from './dto/create-book.dto';
import { EbookDownloadDto } from './dto/ebook-download.dto';
import {
  CreatePriceDto,
  UpdatePriceDto,
  UpdatePriceTimeDto,
} from './dto/price.dto';
import { QueryPodPackageIdDto } from './dto/query-pod-package-id.dto';
import {
  CreateSpecificationDto,
  UpdateSpecificationDto,
} from './dto/specification.dto';
import { UpdateBookDto } from './dto/update-book.dto';
import { BookEntity } from './entities/book.entity';
import { CopyrightEntity } from './entities/copyright.entity';
import { PriceEntity } from './entities/price.entity';
import { SpecificationEntity } from './entities/specification.entity';

@ApiTags('books')
@Controller('books')
export class BooksController {
  constructor(
    private readonly booksService: BooksService,
    private readonly encryptBookSourceTaskService: EncryptBookSourceTaskService,
  ) {}

  @ApiBearerAuth()
  @ApiOperation({
    summary: '创建书籍',
    description: '创建图书出版项目。<br/>所需角色:<b>EDITOR</b>',
  })
  @ApiCreatedResponse({ type: BookEntity })
  // @Roles(Role.EDITOR)
  @Post()
  async create(@Req() req, @Body() createBookDto: CreateBookDto) {
    return new BookEntity(
      await this.booksService.create(createBookDto, req.user.sub),
    );
  }

  @Get('my')
  @ApiPaging(BookEntity, QueryParamDto)
  @ApiBearerAuth()
  @ApiOperation({
    summary: '查询我的书籍',
    description: '查询，过滤，搜索我的书籍与页码',
  })
  findMyBooks(@Req() req, @PagingQuery(QueryParamDto) params: QueryParamDto) {
    return this.booksService.findPaginationByUser(req.user.sub, params);
  }

  @Get(':bookId')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '根据bookID查询书籍详情',
    description: '公共接口：需要在不登录的情况下展示书籍详情',
  })
  @ApiOkResponse({ type: BookEntity })
  @Public()
  async findBookById(@Param('bookId') bookId: string) {
    return new BookEntity(await this.booksService.findById(bookId));
  }

  @Get('my/:bookId')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '根据bookID查询我的书籍',
  })
  @ApiOkResponse({ type: BookEntity })
  async findMyBookById(@Req() req, @Param('bookId') bookId: string) {
    return new BookEntity(
      await this.booksService.findByUserAndId(req.user.sub, bookId),
    );
  }

  @Patch('my/:bookId')
  @ApiOperation({
    summary: '根据bookID更新书籍',
    description: '更新图书出版项目详细信息。<br/>所需角色:<b>EDITOR</b>',
  })
  // @Roles(Role.EDITOR)
  @ApiBearerAuth()
  @ApiOkResponse({ type: BookEntity })
  async updateBook(
    @Req() req,
    @Param('bookId') bookId: string,
    @Body() updateBookDto: UpdateBookDto,
  ) {
    return new BookEntity(
      await this.booksService.updateUsersBook(
        req.user.sub,
        bookId,
        updateBookDto,
      ),
    );
  }

  @Delete('my/:bookId')
  @ApiOperation({
    summary: '根据bookID删除我的书籍',
  })
  // @Roles(Role.EDITOR)
  @ApiBearerAuth()
  @ApiOkResponse({ type: BookEntity })
  remove(@Req() req, @Param('bookId') bookId: string) {
    return this.booksService.remove(req.user.sub, bookId);
  }

  @Get('my/:bookId/upDown')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '根据bookID上下架我的书籍',
  })
  @ApiOkResponse({ type: BookEntity })
  async upOrDownMyBook(@Req() req, @Param('bookId') bookId: string) {
    return new BookEntity(
      await this.booksService.upOrDownMyBook(req.user.sub, bookId),
    );
  }

  @Post('callback/book/publish')
  // @ApiOperation({
  //   summary: '把我的书发布到区块链上',
  //   description: '审核回调接口 将我的书籍推送到链上<br/>所需角色:<b>ADMIN</b>',
  // })
  // @Roles(Role.ADMIN)
  @ApiBearerAuth()
  @ApiExcludeEndpoint()
  publish(@Req() req, @Body() checkBook: CheckBookDto) {
    return this.booksService.publish(req.user.sub, checkBook);
  }

  @Post(':bookId/copyright')
  @ApiOperation({
    summary: '根据BookID创建版权信息',
  })
  // @Roles(Role.EDITOR)
  @ApiBearerAuth()
  async createCopyright(
    @Req() req,
    @Param('bookId') bookId: string,
    @Body() copyrightDto: CreateCopyrightDto,
  ) {
    return new CopyrightEntity(
      await this.booksService.updateBookCopyright(
        req.user.sub,
        bookId,
        copyrightDto,
      ),
    );
  }

  @Patch(':bookId/copyright')
  @ApiOperation({
    summary: '根据BookID更新版权信息',
  })
  // @Roles(Role.EDITOR)
  @ApiBearerAuth()
  async updateCopyright(
    @Req() req,
    @Param('bookId') bookId: string,
    @Body() copyrightDto: UpdateCopyrightDto,
  ) {
    return new CopyrightEntity(
      await this.booksService.updateBookCopyright(
        req.user.sub,
        bookId,
        copyrightDto,
      ),
    );
  }

  @Post(':bookId/price')
  @ApiOperation({
    summary: '根据BookID创建价格信息',
  })
  // @Roles(Role.EDITOR)
  @ApiBearerAuth()
  async createPrice(
    @Req() req,
    @Param('bookId') bookId: string,
    @Body() createpriceDto: CreatePriceDto,
  ) {
    return new PriceEntity(
      await this.booksService.updateBookPrice(
        req.user.sub,
        bookId,
        createpriceDto,
      ),
    );
  }

  @Patch(':bookId/price')
  @ApiOperation({
    summary: '根据BookID更新价格信息',
  })
  // @Roles(Role.EDITOR)
  @ApiBearerAuth()
  async updatePrice(
    @Req() req,
    @Param('bookId') bookId: string,
    @Body() updatePriceDto: UpdatePriceDto,
  ) {
    return new PriceEntity(
      await this.booksService.updateBookPrice(
        req.user.sub,
        bookId,
        updatePriceDto,
      ),
    );
  }

  @Post(':bookId/price/time')
  @ApiOperation({
    summary: '根据BookID-更新购买设置',
  })
  // @Roles(Role.EDITOR)
  @ApiBearerAuth()
  async updatePriceTime(
    @Req() req,
    @Param('bookId') bookId: string,
    @Body() updatePriceTimeDto: UpdatePriceTimeDto,
  ) {
    return new PriceEntity(
      await this.booksService.updateBookPriceTime(
        req.user.sub,
        bookId,
        updatePriceTimeDto,
      ),
    );
  }

  @Post(':bookId/specification')
  @ApiOperation({
    summary: '根据BookID创建打印规格信息',
  })
  // @Roles(Role.EDITOR)
  @ApiBearerAuth()
  async createSpecification(
    @Req() req,
    @Param('bookId') bookId: string,
    @Body() createSpecificationDto: CreateSpecificationDto,
  ) {
    return new SpecificationEntity(
      await this.booksService.updateBookSpecification(
        req.user.sub,
        bookId,
        createSpecificationDto,
      ),
    );
  }

  @Patch(':bookId/specification')
  @ApiOperation({
    summary: '根据BookID更新规格信息',
  })
  // @Roles(Role.EDITOR)
  @ApiBearerAuth()
  async updateSpecification(
    @Req() req,
    @Param('bookId') bookId: string,
    @Body() updateSpecificationDto: UpdateSpecificationDto,
  ) {
    return new SpecificationEntity(
      await this.booksService.updateBookSpecification(
        req.user.sub,
        bookId,
        updateSpecificationDto,
      ),
    );
  }

  @Get('sale/:bookId')
  @ApiPaging(DobOrderEntity, QueryParamDto)
  @ApiOperation({
    summary: '根据BookID获取销售记录表',
    description: '公共接口：需要在不登录的情况下展示书籍详情',
  })
  @ApiOkResponse({ type: DobOrderEntity })
  @Public()
  listBookSalesById(
    @Param('bookId') bookId: string,
    @PagingQuery(QueryParamDto) params: QueryParamDto,
  ) {
    return this.booksService.listBookSalesById(bookId, params);
  }

  @Get('config/:key')
  @ApiOperation({
    summary: '书籍下拉列表配置',
    description:
      '根据参数key来获取不同的配置 [language|contributor|category|audiences|print|all]',
  })
  @Public()
  getConfigBookByKey(@Param('key') key: string) {
    if (key == BookCofing.LANGUAGE) {
      return LanguageConfig.language;
    } else if (key == BookCofing.CONTRIBUTOR) {
      return ContributorConfig.contributor;
    } else if (key == BookCofing.CATEGORY) {
      return CategoryConfig.category;
    } else if (key == BookCofing.AUDIENCES) {
      return AudiencesConfig.audiences;
    } else if (key == BookCofing.PRINT) {
      return PrintConfig;
    } else if (key == BookCofing.ALL) {
      return {
        language: LanguageConfig.language,
        contributor: ContributorConfig.contributor,
        category: CategoryConfig.category,
        audiences: AudiencesConfig.audiences,
        print: PrintConfig,
      };
    } else {
      return null;
    }
  }

  @Post('config/podPackageId')
  @ApiOperation({
    summary: '打印规格信息(podPackageId)',
    description:
      '动态加载打印信息[根据传入的值进行动态配置获取],当所有参数传递后,能获取到podPackageId',
  })
  @Public()
  getConfigPodPackageId(@Body() params: QueryPodPackageIdDto) {
    return this.booksService.getConfigPodPackageId(params);
  }

  @Get('isbn/free')
  @ApiOperation({
    summary: '获取免费isbn',
  })
  @ApiBearerAuth()
  @ApiOkResponse({ type: String })
  async getIsbnFree() {
    return this.booksService.getIsbnFree();
  }

  @Post('download/ebook')
  @ApiOperation({
    summary: '获取电子书下载链接',
  })
  @ApiBearerAuth()
  @ApiOkResponse({ type: String })
  async getEbookDownloadLink(@Req() req, @Body() params: EbookDownloadDto) {
    return this.booksService.getEbookDownloadLink(req.user.sub, params.dobId);
  }

  @Post('encrypt-book-source')
  @Public()
  @ApiOperation({
    summary: '触发书籍源码加密任务',
  })
  async triggerEncryption() {
    return this.encryptBookSourceTaskService.executeTask();
  }
}
