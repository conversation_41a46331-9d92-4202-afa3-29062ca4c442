import { ApiProperty } from '@nestjs/swagger';
import { Specification } from '@prisma/client';
import { Exclude } from 'class-transformer';

export class SpecificationEntity implements Specification {
  constructor({ ...data }: Partial<SpecificationEntity>) {
    Object.assign(this, data);
  }
  @ApiProperty()
  printQuality: string;

  @ApiProperty()
  sourceValidateId: number;

  @ApiProperty()
  coverValidateId: number;

  @ApiProperty()
  podPackageId: string;

  @Exclude()
  sourceUrl: string;

  @ApiProperty()
  coverUrl: string;

  createdAt: Date;

  updatedAt: Date;

  @ApiProperty()
  id: string;

  @ApiProperty()
  bookSize: string;

  @ApiProperty()
  pageCount: number;

  @ApiProperty()
  // interiorColor: $Enums.InteriorColor;
  interiorColor: string;

  @ApiProperty()
  // paperType: $Enums.PaperType;
  paperType: string;

  @ApiProperty()
  // bookBinding: $Enums.BookBinding;
  bookBinding: string;

  @ApiProperty()
  // coverFinish: $Enums.CoverFinish;
  coverFinish: string;

  @Exclude()
  bookId: string;
}
