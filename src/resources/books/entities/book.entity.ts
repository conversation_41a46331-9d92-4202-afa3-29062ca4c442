import { ApiProperty } from '@nestjs/swagger';
import { $Enums, Book } from '@prisma/client';
import { Exclude, Type } from 'class-transformer';
import { ContributorEntity } from './contributor.entity';
import { CopyrightEntity } from './copyright.entity';
import { PriceEntity } from './price.entity';
import { SpecificationEntity } from './specification.entity';
import { ClusterEntity } from 'src/resources/clusters/entities/cluster.entity';
import { UserEntity } from 'src/resources/users/entities/user.entity';

export class BookEntity implements Book {
  constructor({ ...data }: Partial<BookEntity>) {
    Object.assign(this, data);
  }

  @ApiProperty()
  userId: string;

  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  language: string;

  @ApiProperty()
  category: string;

  @ApiProperty({
    description:
      '[PRINT_BOOK:打印书籍,PHOTO_BOOK:写真,COMIC_BOOK:漫画,MAGAZINE:杂志,YEARBOOK:年报,CALENDAR:日记]',
  })
  type: $Enums.BookType;

  @ApiProperty()
  subtitle: string;

  @ApiProperty()
  edition: string;

  @ApiProperty()
  editionStatement: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  contributorNotes: string;

  @ApiProperty()
  tableOfContents: string[];

  @ApiProperty()
  keywords: string;

  @Exclude()
  bisacMainCategory: string;

  @Exclude()
  bisacCategory2: string;

  @Exclude()
  bisacCategory3: string;

  @ApiProperty()
  audience: string;

  @Exclude()
  explicitContentType: string;

  @ApiProperty({
    description: '当前阶段[GOLD:金浆果,SILVER:银浆果,COPPER:铜浆果,BLUE:蓝莓]',
  })
  phase: $Enums.DnaLevel;

  @ApiProperty({
    description: '当前阶段剩余数量',
  })
  phaseRemain: number;

  @ApiProperty({
    description: '销量',
  })
  sales: number;

  @ApiProperty({
    description: '浏览量',
  })
  views: number;

  @ApiProperty()
  bannerImages: string[];

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({
    description:
      '[DRAFT:草稿,PENDING:审核中,APPROVED:审核通过,PUBLISHED:上架,FAILED:审核拒绝]',
  })
  status: $Enums.BookStatus;

  @ApiProperty()
  @Type(() => ContributorEntity)
  contributors?: ContributorEntity[];

  @ApiProperty()
  @Type(() => CopyrightEntity)
  copyright?: CopyrightEntity;

  @ApiProperty()
  @Type(() => SpecificationEntity)
  specification?: SpecificationEntity;

  @ApiProperty()
  @Type(() => PriceEntity)
  price?: PriceEntity;

  @ApiProperty()
  @Type(() => ClusterEntity)
  cluster?: ClusterEntity;

  @ApiProperty()
  @Type(() => UserEntity)
  user?: UserEntity;
}
