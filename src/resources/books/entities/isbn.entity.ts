import { ApiProperty } from '@nestjs/swagger';
import { Isbn } from '@prisma/client';

export class IsbnEntity implements Isbn {
  @ApiProperty()
  isFreeIsbn: boolean;

  @ApiProperty()
  copyrightId: string;

  createdAt: Date;

  updatedAt: Date;

  @ApiProperty()
  id: string;

  @ApiProperty()
  isbnNum: string;

  @ApiProperty()
  publisher: string;

  @ApiProperty()
  contactName: string;

  @ApiProperty()
  contactAddress: string;

  @ApiProperty()
  contactCity: string;

  @ApiProperty()
  contactPostalCode: string;

  @ApiProperty()
  contactState: string;

  @ApiProperty()
  contactCountry: string;

  @ApiProperty()
  contactPhone: string;
}
