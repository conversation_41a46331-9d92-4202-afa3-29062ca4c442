import { ApiProperty } from '@nestjs/swagger';
import { Contributor } from '@prisma/client';
import { Exclude } from 'class-transformer';

export class ContributorEntity implements Contributor {
  @ApiProperty()
  firstname: string;

  @ApiProperty()
  lastname: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  id: string;

  @ApiProperty()
  role: string;

  @Exclude()
  bookId: string;
}
