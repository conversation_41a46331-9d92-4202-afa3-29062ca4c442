import { ApiProperty } from '@nestjs/swagger';
import { $Enums, Price } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Exclude, Transform } from 'class-transformer';

export class PriceEntity implements Price {
  constructor({ ...data }: Partial<PriceEntity>) {
    Object.assign(this, data);
  }
  whiteLevel: $Enums.DnaLevel;
  whiteOpenTime: Date;

  createdAt: Date;
  updatedAt: Date;
  @Exclude()
  bookId: string;

  @ApiProperty()
  id: string;

  @ApiProperty()
  goldCount: number;

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  goldPrice: Decimal;

  @ApiProperty()
  silverCount: number;

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  silverPrice: Decimal;

  @ApiProperty()
  copperCount: number;

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  copperPrice: Decimal;

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  bluePrice: Decimal;

  @ApiProperty({
    description: '开售时间',
  })
  openTime: Date;

  @ApiProperty()
  goldRemain: number;

  @ApiProperty()
  silverRemain: number;

  @ApiProperty()
  copperRemain: number;

  @ApiProperty()
  blueSales: number;
}
