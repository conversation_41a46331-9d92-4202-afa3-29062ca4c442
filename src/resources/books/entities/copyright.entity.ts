import { ApiProperty } from '@nestjs/swagger';
import { $Enums, Copyright } from '@prisma/client';
import { Exclude, Type } from 'class-transformer';
import { IsbnEntity } from './isbn.entity';

export class CopyrightEntity implements Copyright {
  constructor({ ...data }: Partial<CopyrightEntity>) {
    Object.assign(this, data);
  }

  createdAt: Date;

  updatedAt: Date;

  @ApiProperty()
  id: string;

  @ApiProperty()
  license: $Enums.CopyrightLicense;

  @ApiProperty()
  holderName: string;

  @ApiProperty()
  copyrightYear: string;

  @Exclude()
  bookId: string;

  @ApiProperty()
  @Type(() => IsbnEntity)
  isbn?: IsbnEntity;
}
