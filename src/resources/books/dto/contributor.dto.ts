import { ApiProperty, OmitType, PartialType, PickType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';

export class ContributorDto {
  @ApiProperty({
    required: true,
    description: '姓',
  })
  @IsString()
  @MaxLength(50)
  firstname: string;

  @ApiProperty({
    required: true,
    description: '名',
  })
  @IsString()
  @MaxLength(50)
  lastname: string;

  @ApiProperty({
    required: true,
  })
  bookId: string;

  @ApiProperty({
    required: true,
  })
  id: string;

  @ApiProperty()
  @IsString()
  role: string;
}

export class CreateContributorDto extends OmitType(ContributorDto, [
  'id',
  'bookId',
] as const) {}

export class UpdateContributorDto extends PartialType(ContributorDto) {
  @ApiProperty()
  @IsString()
  id: string;
}
export class DeleteContributorDto extends PickType(ContributorDto, [
  'id',
] as const) {
  @ApiProperty()
  @IsString()
  id: string;
}

export class CUDContributorDto {
  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateContributorDto)
  create?: CreateContributorDto[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => UpdateContributorDto)
  update?: UpdateContributorDto[];

  @ApiProperty({
    description: 'Id array for the contributors that you want to delete.',
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DeleteContributorDto)
  delete?: DeleteContributorDto[];
}
