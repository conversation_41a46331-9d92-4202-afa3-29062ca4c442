import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsNumber, IsString, MaxLength } from 'class-validator';

export class CreateSpecificationDto {
  @ApiProperty({
    required: true,
    description: '书籍URL验证ID',
  })
  @IsNumber()
  sourceValidateId: number;

  @ApiProperty({
    required: true,
    description: '封面URL验证ID',
  })
  @IsNumber()
  coverValidateId: number;

  @ApiProperty({
    required: true,
    description: '书籍URL',
  })
  @IsString()
  sourceUrl: string;

  @ApiProperty({
    required: true,
    description: '封面URL',
  })
  @IsString()
  coverUrl: string;

  @ApiProperty({
    required: true,
    description: '书籍大小',
  })
  @IsString()
  bookSize: string;

  @ApiProperty({
    required: true,
    description: '包总数',
  })
  @IsNumber()
  pageCount: number;

  @ApiProperty({
    // enum: $Enums.InteriorColor,
    description: '内部颜色',
  })
  // @IsEnum($Enums.InteriorColor)
  @IsString()
  interiorColor: string;

  @ApiProperty({
    description: '内部颜色',
  })
  @IsString()
  printQuality: string;

  @ApiProperty({
    // enum: $Enums.PaperType,
    description: '纸张类型',
  })
  // @IsEnum($Enums.PaperType)
  @IsString()
  paperType: string;

  @ApiProperty({
    // enum: $Enums.BookBinding,
    description: '装订样式',
  })
  // @IsEnum($Enums.BookBinding)
  @IsString()
  bookBinding: string;

  @ApiProperty({
    // enum: $Enums.CoverFinish,
    description: '封面装饰',
  })
  // @IsEnum($Enums.CoverFinish)
  @IsString()
  coverFinish: string;

  @ApiProperty({
    description:
      'Reference from: [Product Specification Sheet](https://assets.lulu.com/media/specs/lulu-print-api-spec-sheet.xlsx)',
  })
  @MaxLength(27)
  podPackageId: string;
}

export class UpdateSpecificationDto extends PartialType(
  CreateSpecificationDto,
) {}
