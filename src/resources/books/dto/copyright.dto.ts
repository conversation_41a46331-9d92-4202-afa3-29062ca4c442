import { ApiProperty, PartialType } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { Type } from 'class-transformer';
import { IsEnum, IsString, IsUUID, ValidateNested } from 'class-validator';
import { ISBNDto } from './isbn.dto';

export class CreateCopyrightDto {
  @ApiProperty({
    enum: $Enums.CopyrightLicense,
    required: true,
    description: '书籍许可证',
  })
  @IsEnum($Enums.CopyrightLicense)
  license: $Enums.CopyrightLicense;

  @ApiProperty({
    required: true,
    description: '书籍持有者名',
  })
  @IsString()
  holderName: string;

  @ApiProperty({
    required: true,
    description: '版权（年）',
  })
  @IsString()
  copyrightYear: string;

  @ApiProperty({
    required: true,
    description: '书籍编号',
  })
  @ValidateNested({ each: true })
  @Type(() => ISBNDto)
  isbn: ISBNDto;
}

export class UpdateCopyrightDto extends PartialType(CreateCopyrightDto) {
  @ApiProperty()
  @IsUUID()
  id: string;
}
