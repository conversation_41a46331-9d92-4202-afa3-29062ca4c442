import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { CUDContributorDto } from './contributor.dto';
import { CreateBookDto } from './create-book.dto';

export class UpdateBookDto extends PartialType(
  OmitType(CreateBookDto, ['contributors'] as const),
) {
  @ApiProperty()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CUDContributorDto)
  contributors?: CUDContributorDto;
}
