import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { Type } from 'class-transformer';
import { IsArray, IsEnum } from 'class-validator';

export class CheckBookDto {
  @ApiProperty({
    required: true,
    description: '审核结果',
  })
  @IsEnum($Enums.BookStatus)
  status: $Enums.BookStatus;

  @ApiProperty({
    required: true,
    description: '书籍ID数组',
  })
  @IsArray()
  @Type(() => Array<string>)
  ids: string[];
}
