import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class QueryPodPackageIdDto {
  // search
  @ApiProperty({
    required: false,
    description: 'Book Size(打印纸张大小)[第一步]',
  })
  @Expose()
  @IsOptional()
  bookSize?: string;

  @ApiProperty({
    required: false,
    description:
      'Interior Color(打印内部颜色)[第二步]<br/>注意第二步需要传interiorColor+printQuality',
  })
  @IsString()
  @IsOptional()
  interiorColor?: string;

  @ApiProperty({
    required: false,
    description:
      'Interior Color(打印内部颜色)[第二步]<br/>注意第二步需要传interiorColor+printQuality',
  })
  @IsString()
  @IsOptional()
  printQuality?: string;

  @ApiProperty({
    required: false,
    description: 'Paper Type(打印内部样式)[第三步]',
  })
  @IsString()
  @IsOptional()
  paperType?: string;

  @ApiProperty({
    required: false,
    description: 'Book Binding(打印装订样式)[第四步]',
  })
  @IsString()
  @IsOptional()
  bookBinding?: string;

  @ApiProperty({
    required: false,
    description: 'Cover Finish(打印封面装饰)[第五步]',
  })
  @IsString()
  @IsOptional()
  coverFinish?: string;

  @ApiProperty({
    required: false,
    description: 'Page Count(页数)[最后]',
  })
  @IsNumber()
  @IsOptional()
  pageCount?: number;
}
