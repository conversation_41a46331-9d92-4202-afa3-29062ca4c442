import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { CreateContributorDto } from './contributor.dto';

export class CreateBookDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  @ApiProperty({
    required: true,
    description: '书籍名称',
  })
  title: string;

  @IsString()
  @MaxLength(20)
  @ApiProperty({
    required: true,
    description: '书籍语言',
  })
  language: string;

  @IsString()
  @ApiProperty({
    required: true,
    description: '书籍分类',
  })
  @IsOptional()
  category: string;

  @ApiProperty({
    default: $Enums.BookType.PRINT_BOOK,
    enum: $Enums.BookType,
    description: '书籍类型',
  })
  @IsEnum($Enums.BookType)
  type: $Enums.BookType;

  @IsString()
  @ApiProperty({
    required: false,
    description: '书籍副标题',
  })
  @IsOptional()
  subtitle: string;

  @IsString()
  @ApiProperty({
    required: false,
    description: '书籍版本',
  })
  @IsOptional()
  edition: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: '书籍版本说明',
  })
  editionStatement: string;

  @ApiProperty({
    required: true,
    description: '书籍封面图[数组 URL]',
  })
  @IsArray()
  @Type(() => Array<string>)
  bannerImages: string[];

  @IsString()
  @ApiProperty({
    required: true,
    description: '书籍介绍',
  })
  description: string;

  @IsString()
  @ApiProperty({
    required: true,
    description: '书籍贡献者',
  })
  contributorNotes: string;

  @ApiProperty({
    required: false,
    description: '书籍目录[数组 字符串]',
  })
  @IsOptional()
  @IsArray()
  @Type(() => Array<string>)
  tableOfContents: string[];

  @IsString()
  @ApiProperty({ description: '书籍关键字[关键字之间用逗号分隔]' })
  keywords: string;

  @IsString()
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  bisacMainCategory: string;

  @IsString()
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  bisacCategory2: string;

  @IsString()
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  bisacCategory3: string;

  @IsString()
  @ApiProperty({
    required: true,
    description: '书籍受众',
  })
  audience: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: '书籍显示内容类型',
  })
  explicitContentType: string;

  @ApiProperty({
    required: true,
    description: '书籍贡献者[数组]',
    example: [{ firstname: '姓', lastname: '名', role: '角色' }],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateContributorDto)
  contributors: CreateContributorDto[];
}
