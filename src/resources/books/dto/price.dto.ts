import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsDecimal,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsUUID,
  Min,
} from 'class-validator';

export class CreatePriceDto {
  @ApiProperty({
    required: true,
    description: '金果数量',
  })
  @IsNumber()
  @Min(1)
  goldCount: number;

  @ApiProperty({
    required: true,
    default: '0.00',
    description: '金果价格',
  })
  @IsDecimal({ force_decimal: true })
  goldPrice: string;

  @ApiProperty({
    required: true,
    description: '银果数量',
  })
  @IsNumber()
  @Min(1)
  silverCount: number;

  @ApiProperty({
    required: true,
    default: '0.00',
    description: '银果价格',
  })
  @IsDecimal({ force_decimal: true })
  silverPrice: string;

  @ApiProperty({
    required: true,
    description: '铜果数量',
  })
  @IsNumber()
  @Min(1)
  copperCount: number;

  @ApiProperty({
    required: true,
    default: '0.00',
    description: '铜果价格',
  })
  @IsDecimal({ force_decimal: true })
  copperPrice: string;

  @ApiProperty({
    required: true,
    default: '0.00',
    description: '蓝莓价格',
  })
  @IsDecimal()
  bluePrice: string;

  @ApiProperty({
    required: false,
    description: '开售时间(date[可选|没有的话不要传这个字段])',
  })
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  openTime?: Date;
}

export class UpdatePriceDto extends PartialType(CreatePriceDto) {
  @ApiProperty()
  @IsUUID()
  id: string;
}

export class UpdatePriceTimeDto {
  @ApiProperty({
    required: true,
    description: '开售时间(date[开放购买传当前时间])',
  })
  @Type(() => Date)
  @IsDate()
  @IsNotEmpty()
  openTime: Date;
}
