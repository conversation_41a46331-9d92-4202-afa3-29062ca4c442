import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsString } from 'class-validator';

export class ISBNDto {
  @ApiProperty({
    required: true,
    description: '是否免费ISBN',
  })
  @IsBoolean()
  isFreeIsbn: boolean;

  @ApiProperty({
    required: true,
  })
  @IsString()
  isbnNum: string;

  @ApiProperty({
    required: true,
    description: '出版商',
  })
  @IsString()
  publisher: string;

  @ApiProperty({
    required: true,
    description: '联系人姓名',
  })
  @IsString()
  contactName: string;

  @ApiProperty({
    required: true,
    description: '联系地址',
  })
  @IsString()
  contactAddress: string;

  @ApiProperty({
    required: true,
    description: '联系城市',
  })
  @IsString()
  contactCity: string;

  @ApiProperty({
    required: true,
    description: '联系邮编',
  })
  @IsString()
  contactPostalCode: string;

  @ApiProperty({
    required: true,
    description: '联系状态',
  })
  @IsString()
  contactState: string;

  @ApiProperty({
    required: true,
    description: '联系国家',
  })
  @IsString()
  contactCountry: string;

  @ApiProperty({
    required: true,
    description: '联系电话',
  })
  @IsString()
  contactPhone: string;
}
