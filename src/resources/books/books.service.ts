import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { paginator, PaginatorTypes } from '@nodeteam/nestjs-prisma-pagination';
import {
  $Enums,
  BookStatus,
  DnaLevel,
  IsbnFreeType,
  PaymentStatus,
  Prisma,
  Role,
} from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { PagedResponseDto } from 'src/common/dtos/paged-response.dto';
import { QueryParamDto } from 'src/common/dtos/query-param.dto';
import { StringService } from 'src/common/utils/string.service';
import { LuluService } from '../lulu/lulu.service';
import { CreateCopyrightDto, UpdateCopyrightDto } from './dto/copyright.dto';
import { CreateBookDto } from './dto/create-book.dto';
import {
  CreatePriceDto,
  UpdatePriceDto,
  UpdatePriceTimeDto,
} from './dto/price.dto';
import {
  CreateSpecificationDto,
  UpdateSpecificationDto,
} from './dto/specification.dto';
import { UpdateBookDto } from './dto/update-book.dto';
import { BookEntity } from './entities/book.entity';
// import { AwsS3Service } from 'src/common/utils/awsS3.service';
import { AwsS3Service } from 'src/common/utils/awsS3.service';
import { ClustersService } from '../clusters/clusters.service';
import { Create256K1ClusterDto } from '../clusters/dto/create-cluster.dto';
import { DobOrderEntity } from '../dobs/entities/dobOrder.entity';
import { CheckBookDto } from './dto/check-book.dto';
import { QueryPodPackageIdDto } from './dto/query-pod-package-id.dto';

@Injectable()
export class BooksService {
  private readonly logger = new Logger(BooksService.name);
  paginate: PaginatorTypes.PaginateFunction = paginator({
    page: 1,
    perPage: 10,
  });

  constructor(
    private prisma: PrismaService,
    private stringService: StringService,
    private luluService: LuluService,
    private clustersService: ClustersService,
    private awsS3Service: AwsS3Service,
  ) {}

  async create(createBookDto: CreateBookDto, userId: string) {
    //检验最新身份
    await this.checkUserEditorRole(userId);
    return new BookEntity(
      await this.prisma.book.create({
        data: {
          ...createBookDto,
          user: {
            connect: { id: userId },
          },
          contributors: createBookDto.contributors
            ? {
                createMany: { data: [...createBookDto.contributors] },
              }
            : undefined,
        },
      }),
    );
  }

  async findPaginationByUser(
    sub: string,
    params: QueryParamDto,
  ): Promise<PagedResponseDto<BookEntity>> {
    const { skip, take, allowCount, sort: order, keyword } = params;
    const filter: Prisma.BookWhereInput = {
      userId: sub,
      title: keyword ? { contains: keyword.trim() } : undefined,
    };
    const bookFindInputs: Prisma.BookFindManyArgs = {
      include: {
        price: true,
        user: true,
        cluster: true,
      },
      where: filter,
      orderBy: [
        this.stringService.toPrismaOrderByObject(order),
        { title: 'asc' },
      ],
      skip,
      take,
    };
    try {
      if (allowCount) {
        const [books, count] = await this.prisma.$transaction([
          this.prisma.book.findMany(bookFindInputs),
          this.prisma.book.count({ where: filter }),
        ]);

        const dataFormat = books.map((one) => {
          return new BookEntity(one);
        });

        return new PagedResponseDto<BookEntity>(dataFormat, skip, take, count);
      } else {
        const books = await this.prisma.book.findMany(bookFindInputs);

        const dataFormat = books.map((one) => {
          return new BookEntity(one);
        });

        return new PagedResponseDto<BookEntity>(dataFormat, skip, take, 0);
      }
    } catch (error) {
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException('Invalid query params.');
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  async findById(id: string) {
    const data = await this.prisma.book.findUnique({
      where: { id },
      include: {
        copyright: {
          include: {
            isbn: true,
          },
        },
        contributors: true,
        specification: true,
        price: true,
        cluster: true,
      },
    });
    if (!data) {
      throw new BadRequestException('Book Not Find');
    }
    try {
      await this.prisma.book.update({
        where: { id },
        data: {
          views: {
            increment: 1,
          },
        },
      });
    } catch (error) {
      this.logger.debug('books views +1 error' + error.toString());
    }
    return data;
  }

  async findByUserAndId(sub: string, id: string) {
    const data = await this.prisma.book.findUnique({
      where: {
        id,
        userId: sub,
      },
      include: {
        copyright: {
          include: {
            isbn: true,
          },
        },
        contributors: true,
        specification: true,
        price: true,
      },
    });
    if (!data) {
      throw new BadRequestException('Book Not Find');
    }
    return data;
  }

  async upOrDownMyBook(sub: string, id: string) {
    const data = await this.prisma.book.findUnique({
      select: { status: true },
      where: {
        id,
        userId: sub,
      },
    });
    if (!data || data.status == BookStatus.DRAFT) {
      throw new BadRequestException('Book Not Find');
    }
    const update = await this.prisma.book.update({
      where: { id },
      data: {
        status:
          data.status == BookStatus.PUBLISHED
            ? BookStatus.APPROVED
            : BookStatus.PUBLISHED,
      },
    });
    return update;
  }

  async updateUsersBook(sub: string, id: string, updateBookDto: UpdateBookDto) {
    //检验最新身份
    await this.checkUserEditorRole(sub);

    const book = this.prisma.book.findUnique({ where: { id, userId: sub } });
    if (!book) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'InvalidBookId',
      });
    }

    const { contributors, ...baseDetails } = updateBookDto;

    return await this.prisma.$transaction(async (tx) => {
      // 1. update relationship:  contributors
      if (contributors) {
        // delete
        if (contributors.delete) {
          await tx.contributor.deleteMany({
            where: {
              id: {
                in: contributors.delete.map((one) => one.id),
              },
            },
          });
        }

        //update
        if (contributors.update) {
          for (const contributor of contributors.update) {
            await tx.contributor.update({
              where: {
                id: contributor.id,
              },
              data: { ...contributor, bookId: id },
            });
          }
        }

        // create
        if (contributors.create) {
          for (const contributor of contributors.create) {
            await tx.contributor.create({
              data: { ...contributor, bookId: id },
            });
          }
        }
      }

      // 2. update base info of the book, and return book
      return await tx.book.update({
        where: { id },
        data: {
          ...baseDetails,
        },
        include: {
          copyright: {
            include: {
              isbn: true,
            },
          },
          price: true,
          contributors: true,
          specification: true,
        },
      });
    });
  }

  async remove(userId: string, id: string) {
    //检验最新身份
    await this.checkUserEditorRole(userId);

    const book = await this.prisma.book.findUnique({ where: { id, userId } });

    if (book.status === BookStatus.PUBLISHED) {
      throw new ForbiddenException();
    }

    return this.prisma.book.delete({ where: { id } });
  }

  async updateBookCopyright(
    userId: string,
    bookId: string,
    copyrightDto: CreateCopyrightDto | UpdateCopyrightDto,
  ) {
    //检验最新身份
    await this.checkUserEditorRole(userId);

    const book = await this.prisma.book.findUnique({
      where: { id: bookId, userId: userId },
      include: {
        copyright: { include: { isbn: true } },
      },
    });
    if (!book) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'InvalidBookId',
      });
    }
    const { isbn: isbnDetail, ...copyrightDetail } = copyrightDto;

    return await this.prisma.$transaction(async (tx) => {
      const copyright = await tx.copyright.upsert({
        where: {
          bookId: book.id,
        },
        create: {
          ...copyrightDetail,
          book: { connect: { id: book.id } },
        },
        update: {
          ...copyrightDetail,
          book: { connect: { id: book.id } },
          id: undefined,
        },
        include: {
          isbn: true,
        },
      });
      if (isbnDetail) {
        const isbn = await tx.isbn.upsert({
          where: {
            copyrightId: copyright.id,
          },
          create: {
            ...isbnDetail,
            copyright: { connect: { id: copyright.id } },
          } as Prisma.IsbnCreateInput,
          update: {
            ...isbnDetail,
            copyright: { connect: { id: copyright.id } },
            id: undefined,
          } as Prisma.IsbnUpdateInput,
        });
        if (isbnDetail.isFreeIsbn) {
          await tx.isbnFree.upsert({
            where: { isbnNum: isbnDetail.isbnNum },
            update: { status: IsbnFreeType.USED },
            create: { isbnNum: isbnDetail.isbnNum, status: IsbnFreeType.USED },
          });
        }
        return { ...copyright, isbn: isbn };
      }
      return copyright;
    });
  }

  async updateBookPrice(
    userId: string,
    bookId: string,
    priceDto: CreatePriceDto | UpdatePriceDto,
  ) {
    //检验最新身份
    await this.checkUserEditorRole(userId);

    const book = await this.prisma.book.findUnique({
      where: { id: bookId, userId: userId },
      include: {
        copyright: { include: { isbn: true } },
      },
    });
    if (!book) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'InvalidBookId',
      });
    }
    // 自动同步当前剩余总量
    priceDto['goldRemain'] = priceDto.goldCount;
    priceDto['silverRemain'] = priceDto.silverCount;
    priceDto['copperRemain'] = priceDto.copperCount;

    return await this.prisma.$transaction(async (tx) => {
      const price = await tx.price.upsert({
        where: {
          bookId: book.id,
        },
        create: {
          ...priceDto,
          book: { connect: { id: book.id } },
        } as Prisma.PriceCreateInput,
        update: {
          ...priceDto,
          book: { connect: { id: book.id } },
          id: undefined,
        } as Prisma.PriceUpdateInput,
      });
      // 将状态从草稿改为审核中
      await tx.book.update({
        where: { id: book.id },
        data: {
          phase: DnaLevel.GOLD,
          phaseRemain: priceDto.goldCount,
          status: BookStatus.PENDING,
        },
      });
      return price;
    });
  }

  async updateBookSpecification(
    userId: string,
    bookId: string,
    specificationDto: CreateSpecificationDto | UpdateSpecificationDto,
  ) {
    //检验最新身份
    await this.checkUserEditorRole(userId);

    const book = await this.prisma.book.findUnique({
      where: { id: bookId, userId: userId },
      include: {
        copyright: { include: { isbn: true } },
      },
    });
    if (!book) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'InvalidBookId',
      });
    }
    const specificationDetail = { ...specificationDto };
    // recheck lulu validation status
    if (specificationDto.sourceUrl || specificationDto.sourceValidateId) {
      const { page_count, source_url } =
        await this.luluService.retrieveValidateInteriorStatus(
          specificationDto.sourceValidateId,
        );
      if (specificationDto.sourceUrl != source_url) {
        throw new BadRequestException('Invalid Source file validation');
      }
      specificationDetail.pageCount = page_count;
    }
    if (specificationDto.coverUrl || specificationDto.coverValidateId) {
      const { status } = await this.luluService.retrieveValidateCoverFileStatus(
        specificationDto.coverValidateId,
      );
      if (status == 'ERROR') {
        throw new BadRequestException('Invalid cover file validation');
      }
    }

    const specification = await this.prisma.specification.upsert({
      where: {
        bookId: book.id,
      },
      create: {
        ...specificationDetail,
        book: { connect: { id: book.id } },
      } as Prisma.SpecificationCreateInput,
      update: {
        ...specificationDetail,
        book: { connect: { id: book.id } },
        id: undefined,
      } as Prisma.SpecificationUpdateInput,
    });
    return specification;
  }

  async publish(id: string, checkBook: CheckBookDto) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: { role: true },
    });
    if (!user) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'UserNotFind',
      });
    }
    if (user.role != Role.ADMIN) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'UserNotAuthor',
      });
    }

    const bookList = await this.prisma.book.findMany({
      where: { id: { in: checkBook.ids } },
      include: {
        specification: true,
        contributors: true,
        copyright: { include: { isbn: true } },
      },
    });
    if (!bookList || bookList.length == 0) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'InvalidBookId',
      });
    }
    for (const book of bookList) {
      if (book.status !== $Enums.BookStatus.PENDING) continue;

      // TODO pushlish with clusterService
      const clusterData: Create256K1ClusterDto = {
        uid: book.userId,
        name: book.title,
        description: book.subtitle ?? book.title,
        isPublic: false,
        minCkb: 0,
        bookId: book.id,
        coverImg: book.bannerImages?.[0],
        author:
          book.contributors.length > 0
            ? book.contributors[0].firstname + book.contributors[0].lastname
            : '',
        ISBN: book.copyright ? book.copyright.isbn.isbnNum : '',
        sourceUrl:
          'btcfs://968badc6c87622e5e9412adde79cc431480eb03500e7e7aea656624ccfb8e77ai0;btcfs://a0135609448ac38bb6fde6332200215ba3a69df7ca8bf1bae8e5f32fc460cad8i0;btcfs://d72ad6527381e527db2e253a8786707e941e9f37c3a1f39291efec961728a159i0;btcfs://f76292e56c454ef04c6a186f31db603e9fb3cf6c33b9de36019569a5ec3b89e0i0;btcfs://06a79fe027f6645aa74c3860e12bb6ce25fe3e7a324f132715a1ef4842cc2117i0',
      };

      const cluster =
        await this.clustersService.createClusterFor256K1(clusterData);
      //update book status
      if (cluster.id && cluster.hash) {
        await this.prisma.book.update({
          where: { id: book.id },
          data: {
            cluster: {
              connect: {
                id: cluster.id,
              },
            },
            status: checkBook.status,
          },
        });
      }
    }
    return bookList;
  }

  async listBookSalesById(
    id: string,
    params: QueryParamDto,
  ): Promise<PagedResponseDto<DobOrderEntity>> {
    const { skip, take, allowCount, sort: order } = params;
    // check books
    const book = await this.prisma.book.findUnique({
      where: {
        id,
        status: BookStatus.PUBLISHED,
      },
      include: {
        cluster: true,
      },
    });
    if (!book || !book.cluster || !book.cluster.id) {
      return new PagedResponseDto<DobOrderEntity>([], skip, take, 0);
    }
    // get order
    const filter: Prisma.DobOrderWhereInput = {
      clusterId: book.cluster.id,
      status: PaymentStatus.DONE,
    };
    const bookFindInputs: Prisma.DobOrderFindManyArgs = {
      where: filter,
      include: {
        payUser: { select: { name: true, avatar: true, isWallet: true } },
        dob: { select: { txHash: true } },
        payment: true,
      },
      orderBy: [this.stringService.toPrismaOrderByObject(order)],
      skip,
      take,
    };

    try {
      if (allowCount) {
        const [dobOreders, count] = await this.prisma.$transaction([
          this.prisma.dobOrder.findMany(bookFindInputs),
          this.prisma.dobOrder.count({ where: filter }),
        ]);
        // 获取用户钱包

        const dataFormat = await Promise.all(
          dobOreders.map(async (one) => {
            if (one['payUser'] && one['payUser']['isWallet']) {
              const addr = await this.prisma.wallet.findUnique({
                where: { userId: one.payUserId },
              });
              one['payUser']['address'] = addr.address;
              one['payUser']['internalAddress'] = addr.internalAddress;
            }
            return new DobOrderEntity(one);
          }),
        );

        return new PagedResponseDto<DobOrderEntity>(
          dataFormat,
          skip,
          take,
          count,
        );
      } else {
        const dobOreders = await this.prisma.dobOrder.findMany(bookFindInputs);

        const dataFormat = dobOreders.map((one) => {
          return new DobOrderEntity(one);
        });

        return new PagedResponseDto<DobOrderEntity>(dataFormat, skip, take, 0);
      }
    } catch (error) {
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException('Invalid query params.');
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  async getIsbnFree() {
    const ISBNfree = await this.prisma.isbnFree.findFirst({
      select: { isbnNum: true },
      where: {
        status: IsbnFreeType.UNUSED,
      },
    });
    if (!ISBNfree) {
      throw new BadRequestException('No More Free ISBN');
    }
    return ISBNfree.isbnNum.toString();
  }

  async getConfigPodPackageId(params: QueryPodPackageIdDto) {
    const {
      bookSize,
      interiorColor,
      printQuality,
      paperType,
      bookBinding,
      coverFinish,
      pageCount,
    } = params;

    if (!bookSize) {
      return await this.prisma.bookSku.groupBy({
        by: ['bookType'],
      });
    }
    //第五步
    if (
      bookSize &&
      interiorColor &&
      printQuality &&
      paperType &&
      bookBinding &&
      coverFinish
    ) {
      const skuData = await this.prisma.bookSku.findFirst({
        select: { minPage: true, maxPage: true, fullSku: true },
        where: {
          bookType: bookSize,
          interiorColor: interiorColor,
          paperType: paperType,
          bind: bookBinding,
          lamination: coverFinish,
        },
      });
      if (!pageCount) {
        throw new BadRequestException('pageCount Not Find');
      }
      if (pageCount < skuData.minPage || pageCount > skuData.maxPage) {
        throw new BadRequestException(
          'pageCount Exceed the limit,expect [' +
            skuData.minPage +
            '-' +
            skuData.maxPage +
            ']',
        );
      }
      return skuData.fullSku;
    }
    //第四步
    if (bookSize && interiorColor && printQuality && paperType && bookBinding) {
      return await this.prisma.bookSku.groupBy({
        where: {
          bookType: bookSize,
          interiorColor: interiorColor,
          paperType: paperType,
          bind: bookBinding,
        },
        by: ['lamination'],
      });
    }
    //第三步
    if (bookSize && interiorColor && printQuality && paperType) {
      return await this.prisma.bookSku.groupBy({
        where: {
          bookType: bookSize,
          interiorColor: interiorColor,
          paperType: paperType,
        },
        by: ['bind'],
      });
    }
    // 第二步
    if (bookSize && interiorColor && printQuality) {
      return await this.prisma.bookSku.groupBy({
        where: { bookType: bookSize, interiorColor: interiorColor },
        by: ['paperType'],
      });
    }

    // 第一步
    if (bookSize) {
      return await this.prisma.bookSku.groupBy({
        where: { bookType: bookSize },
        by: ['interiorColor', 'printQuality'],
      });
    }
    return null;
  }

  async updateBookPriceTime(
    userId: string,
    bookId: string,
    priceDto: UpdatePriceTimeDto,
  ) {
    //检验最新身份
    await this.checkUserEditorRole(userId);

    const price = await this.prisma.price.findUnique({
      where: {
        bookId: bookId,
        book: {
          userId: userId,
          status: { notIn: [BookStatus.DRAFT, BookStatus.PENDING] },
        },
      },
    });
    if (!price) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'DataNotFind',
      });
    }
    return await this.prisma.price.update({
      where: { id: price.id },
      data: { openTime: priceDto.openTime },
    });
  }

  async checkUserEditorRole(id: string) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: { role: true },
    });
    if (!user) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'UserNotFind',
      });
    }
    if (user.role != Role.EDITOR) {
      throw new BadRequestException({
        status: HttpStatus.FORBIDDEN,
        message: 'UserNotAuthor',
      });
    }
  }

  async getEbookDownloadLink(userId: string, dobId: string) {
    const dob = await this.prisma.dob.findUnique({
      where: { dobId: dobId },
      select: {
        cluster: true,
        userId: true,
        dobId: true,
        encryptedBookSource: true,
      },
    });
    if (!dob) {
      throw new BadRequestException('Dob Not Find');
    }
    if (dob.userId != userId) {
      throw new BadRequestException('Invalid Dob Owner');
    }
    // if (!dob.encryptedBookSource) {
    //   throw new BadRequestException(
    //     'Book Source Not Find, Please try again later',
    //   );
    // }
    const book = await this.prisma.book.findUnique({
      where: { id: dob.cluster.bookId },
      select: {
        specification: {
          select: {
            sourceUrl: true,
          },
        },
      },
    });
    if (!book) {
      throw new BadRequestException('Book Not Find');
    }

    // try {
    //   const exist = await this.awsS3Service.checkObjectExistence(
    //     `book-source/${dobId}`,
    //   );
    //   if (!exist) {
    //     return book.specification.sourceUrl;
    //   }
    // } catch (error) {
    //   throw new BadRequestException('Book Source Url Not Find');
    // }
    if (
      book.specification.sourceUrl.startsWith('https://') ||
      book.specification.sourceUrl.startsWith('http://')
    ) {
      return { sourceUrl: book.specification.sourceUrl };
    }

    // if (
    //   dob.encryptedBookSource.sourceUrl.startsWith('https://') ||
    //   dob.encryptedBookSource.sourceUrl.startsWith('http://')
    // ) {
    //   return dob.encryptedBookSource.sourceUrl;
    // }

    const sourceUrl = await this.awsS3Service.generatePresignedUrl(
      // 'https://static.silentberry.co',
      null,
      // dob.encryptedBookSource.sourceUrl,
      book.specification.sourceUrl,
      60 * 60 * 24,
    );

    return { sourceUrl };

    // TODO 加密电子书
    // return { sourceUrl, password: dob.encryptedBookSource.userPassword };
  }
}
