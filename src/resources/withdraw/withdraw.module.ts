import { Module } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { WithdrawController } from './withdraw.controller';
import { WithdrawService } from './withdraw.service';
import { StripeModule } from '../stripe/stripe.module';

@Module({
  imports: [PrismaModule, StripeModule],
  controllers: [WithdrawController],
  providers: [WithdrawService, StringService],
  exports: [WithdrawService],
})
export class WithdrawModule {}
