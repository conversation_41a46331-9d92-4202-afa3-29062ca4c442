import { ApiProperty } from '@nestjs/swagger';
import { $Enums, WithdrawalOrder } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Exclude, Transform, Type } from 'class-transformer';
import { UserEntity } from 'src/resources/users/entities/user.entity';

export class WithdrawEntity implements WithdrawalOrder {
  constructor(partial: Partial<WithdrawEntity>) {
    Object.assign(this, partial);
  }
  @ApiProperty()
  visaName: string;

  @ApiProperty()
  visaCVC: string;

  @ApiProperty({ description: '备注[拒绝的原因]' })
  remark: string;

  @Exclude()
  operand: string;

  @ApiProperty()
  id: string;

  @ApiProperty({
    description: '提现地址',
  })
  collectionAddress: string;

  @ApiProperty({
    description: '提现金额',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  withdrawalPrice: Decimal;

  @ApiProperty({
    enum: $Enums.WithdrawalType,
    description: '提现账户',
  })
  withdrawalType: $Enums.WithdrawalType;

  @Exclude()
  withdrawalSource: $Enums.WithdrawalSource;

  @ApiProperty({
    enum: $Enums.PaymentStatus,
    description:
      '提现状态[PENDING:进行中,DONE:已完成,FAILED:失败,CANCELED:取消]',
  })
  status: $Enums.PaymentStatus;

  @ApiProperty({
    description: '提现时间',
  })
  createdAt: Date;

  @ApiProperty({
    description: '更新时间',
  })
  updatedAt: Date;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  @Type(() => UserEntity)
  user?: UserEntity;
}
