import { Body, Controller, Get, Param, Post, Req } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { WithdrawEntity } from './entities/withdraw.entity';
import { WithdrawService } from './withdraw.service';
import { ApiPaging } from 'src/common/decorators/api-paging.decorator';
import { PagingQuery } from 'src/common/decorators/paging-query.decorator';
import { QueryTimeParamDto } from '../../common/dtos/query-time-param.dto';
import { CreateWithdrawDto } from './dto/create-withdraw.dto';
import { WithdrawalSource } from '@prisma/client';
import { RejectWithdrawDto } from './dto/reject-withdraw.dto';
import { Public } from 'src/iam/auth/decorators/public.decorators';

@ApiTags('withdraw')
@Controller('withdraw')
export class WithdrawController {
  constructor(private readonly withdrawService: WithdrawService) {}
  @ApiBearerAuth()
  @ApiOperation({
    summary: '提现开关',
  })
  @Public()
  @ApiCreatedResponse({ type: Boolean })
  @Post('check_withdraw')
  async checkWithdraw() {
    return await this.withdrawService.checkWithdraw();
  }

  @ApiBearerAuth()
  @ApiOperation({
    summary: '作品收益-余额提现',
    description: '创建作品收益提现订单',
  })
  @ApiCreatedResponse({ type: WithdrawEntity })
  @Post('book')
  async createWithdrawAuth(@Req() req, @Body() createDto: CreateWithdrawDto) {
    return new WithdrawEntity(
      await this.withdrawService.createWithdrawAuth(req.user.sub, createDto),
    );
  }

  @ApiBearerAuth()
  @ApiOperation({
    summary: '资产收益-余额提现',
    description: '创建资产收益提现订单',
  })
  @ApiCreatedResponse({ type: WithdrawEntity })
  @Post('assets')
  async createWithdrawDob(@Req() req, @Body() createDto: CreateWithdrawDto) {
    return new WithdrawEntity(
      await this.withdrawService.createWithdrawDob(req.user.sub, createDto),
    );
  }

  @Get('book/list')
  @ApiPaging(WithdrawEntity, QueryTimeParamDto)
  @ApiBearerAuth()
  @ApiOperation({
    summary: '列出作品收益-提现日志',
  })
  findBookEarnings(
    @Req() req,
    @PagingQuery(QueryTimeParamDto) params: QueryTimeParamDto,
  ) {
    return this.withdrawService.findPagination(
      req.user.sub,
      params,
      WithdrawalSource.AUTHOR,
    );
  }

  @Get('assets/list')
  @ApiPaging(WithdrawEntity, QueryTimeParamDto)
  @ApiBearerAuth()
  @ApiOperation({
    summary: '列出资产收益-提现日志',
  })
  findEarnings(
    @Req() req,
    @PagingQuery(QueryTimeParamDto) params: QueryTimeParamDto,
  ) {
    return this.withdrawService.findPagination(
      req.user.sub,
      params,
      WithdrawalSource.DOB,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '根据id获取单条记录',
  })
  @ApiOkResponse({ type: WithdrawEntity })
  async findMyPayment(@Param('id') id: string) {
    return new WithdrawEntity(await this.withdrawService.findById(id));
  }

  @Post('callback/withdraw/reject')
  // @ApiOperation({
  //   summary: '拒绝提现订单',
  //   description: '拒绝提现订单回调接口',
  // })
  // @Roles(Role.ADMIN)
  @ApiBearerAuth()
  @ApiExcludeEndpoint()
  publish(@Req() req, @Body() checkBook: RejectWithdrawDto) {
    return this.withdrawService.reject(req.user.sub, checkBook);
  }
}
