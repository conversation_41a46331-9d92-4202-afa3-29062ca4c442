import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { PagedResponseDto } from 'src/common/dtos/paged-response.dto';
import { StringService } from 'src/common/utils/string.service';
import { WithdrawEntity } from './entities/withdraw.entity';
import {
  DnaLevel,
  PaymentStatus,
  Prisma,
  Role,
  WithdrawalSource,
  WithdrawalType,
} from '@prisma/client';
import { QueryTimeParamDto } from '../../common/dtos/query-time-param.dto';
import { CreateWithdrawDto } from './dto/create-withdraw.dto';
import { RejectWithdrawDto } from './dto/reject-withdraw.dto';

@Injectable()
export class WithdrawService {
  private readonly logger = new Logger(WithdrawService.name);
  constructor(
    private prisma: PrismaService,
    private stringService: StringService,
  ) {}

  // 作者提现
  async checkWithdraw() {
    const config = await this.prisma.golbalConfig.findFirst();
    return config.isOpenwithdraw;
  }

  // 作者提现
  async createWithdrawAuth(userId: string, createBookDto: CreateWithdrawDto) {
    const config = await this.prisma.golbalConfig.findFirst();
    if (config.isOpenwithdraw == false) {
      throw new BadRequestException('Withdrawals are currently unavailable');
    }
    if (createBookDto.withdrawalType == WithdrawalType.VISA) {
      // check visa
      if (!createBookDto.visaName || !createBookDto.visaCVC)
        throw new BadRequestException(
          'Parameter error: visa information missing',
        );
    }
    // 作者总收益
    const authorTotal = await this.prisma.bookEarningsLog.findMany({
      select: { authorEarnings: true, bookId: true },
      where: { book: { userId: userId } },
      // _sum: { authorEarnings: true },
    });

    if (!authorTotal || authorTotal.length == 0)
      throw new BadRequestException('The withdrawal amount is 0');

    // 获取已经提现金额
    const withdrawn = await this.prisma.cluster.findMany({
      select: { withdrawn: true, bookId: true },
      where: { book: { userId } },
      // _sum: { withdrawn: true },
    });

    if (!withdrawn || withdrawn.length == 0)
      throw new BadRequestException('The withdrawal amount is 0');

    const withdrawnRes = withdrawn.reduce((map, obj) => {
      return {
        ...map,
        [obj.bookId]: obj.withdrawn ? obj.withdrawn.toNumber() : 0,
      };
    }, {});

    let withdrawalValue = 0;
    const operand = {
      code: 'book',
      data: {},
    }; //操作记录
    for (const author of authorTotal) {
      // 如果已提现金额 > 0 则取差值
      let val = 0;
      if (
        author.bookId &&
        withdrawnRes[author.bookId] &&
        withdrawnRes[author.bookId] > 0
      ) {
        val = author.authorEarnings.toNumber() - withdrawnRes[author.bookId];
      } else {
        val = author.authorEarnings.toNumber();
      }
      // 操作的cluster及金额
      if (val > 0) {
        operand['data'][author.bookId] = val;
        withdrawalValue += val;
      }
    }

    if (withdrawalValue == 0)
      throw new BadRequestException('The withdrawal amount is 0');

    return await this.prisma.$transaction(async (tx) => {
      // 锁定提现cluster 增加 已提现金额
      Object.keys(operand['data']).forEach(async (key) => {
        await tx.cluster.update({
          where: { bookId: key },
          data: {
            withdrawn: {
              increment: operand['data'][key] ?? 0,
            },
          },
        });
      });
      // 新增订单
      const order = await tx.withdrawalOrder.create({
        data: {
          ...createBookDto,
          user: {
            connect: { id: userId },
          },
          status: PaymentStatus.PENDING,
          withdrawalPrice: withdrawalValue,
          operand: JSON.stringify(operand),
          withdrawalSource: WithdrawalSource.AUTHOR,
        },
      });
      return order;
    });
  }

  // 资产提现
  async createWithdrawDob(userId: string, createBookDto: CreateWithdrawDto) {
    const config = await this.prisma.golbalConfig.findFirst();
    if (config.isOpenwithdraw == false) {
      throw new BadRequestException('Withdrawals are currently unavailable');
    }
    if (createBookDto.withdrawalType == WithdrawalType.VISA) {
      // check visa
      if (!createBookDto.visaName || !createBookDto.visaCVC)
        throw new BadRequestException(
          'Parameter error: visa information missing',
        );
    }
    // 获取持有所有dob 已经提现
    const dob = await this.prisma.dob.findMany({
      select: {
        id: true,
        withdrawn: true,
        level: true,
        cluster: {
          select: {
            bookId: true,
          },
        },
      },
      where: { userId: userId },
    });
    if (!dob || dob.length == 0)
      throw new BadRequestException('The withdrawal amount is 0');

    const bookId = dob
      .map((dobone) => dobone.cluster.bookId)
      .filter((id, index, self) => self.indexOf(id) === index);
    // 获取累计可提取
    const bookEarnings = await this.prisma.bookEarningsLog.findMany({
      select: {
        bookId: true,
        goldEarnings: true,
        silverEarnings: true,
        copperEarnings: true,
      },
      where: {
        bookId: {
          in: bookId,
        },
      },
    });
    const bookEarningsConfig = bookEarnings.reduce((map, obj) => {
      return { ...map, [obj.bookId]: obj };
    }, {});
    const operand = {
      code: 'dob',
      data: {},
    }; //操作记录
    let withdrawalValue = 0;
    for (const one of dob) {
      //累计可提现
      if (!one.cluster) continue;
      const bookid = one.cluster.bookId;
      let val = 0;
      if (bookEarningsConfig[bookid]) {
        if (
          one.level == DnaLevel.GOLD &&
          bookEarningsConfig[bookid]['goldEarnings'] > 0
        ) {
          val =
            bookEarningsConfig[bookid]['goldEarnings'] -
            one.withdrawn.toNumber();
        } else if (
          one.level == DnaLevel.SILVER &&
          bookEarningsConfig[bookid]['silverEarnings'] > 0
        ) {
          val =
            bookEarningsConfig[bookid]['silverEarnings'] -
            one.withdrawn.toNumber();
        } else if (
          one.level == DnaLevel.COPPER &&
          bookEarningsConfig[bookid]['copperEarnings'] > 0
        ) {
          val =
            bookEarningsConfig[bookid]['copperEarnings'] -
            one.withdrawn.toNumber();
        }
      }
      if (val > 0) {
        operand['data'][one.id] = val;
        withdrawalValue += val;
      }
    }
    if (withdrawalValue == 0)
      throw new BadRequestException('The withdrawal amount is 0');
    return await this.prisma.$transaction(async (tx) => {
      // 锁定提现cluster 增加 已提现金额
      Object.keys(operand['data']).forEach(async (key) => {
        await tx.dob.update({
          where: { id: key },
          data: {
            withdrawn: {
              increment: operand['data'][key] ?? 0,
            },
          },
        });
      });
      // 新增订单
      const order = await tx.withdrawalOrder.create({
        data: {
          ...createBookDto,
          user: {
            connect: { id: userId },
          },
          status: PaymentStatus.PENDING,
          withdrawalPrice: withdrawalValue,
          operand: JSON.stringify(operand),
          withdrawalSource: WithdrawalSource.DOB,
        },
      });
      return order;
    });
  }

  async findPagination(
    sub: string,
    params: QueryTimeParamDto,
    source: WithdrawalSource,
  ): Promise<PagedResponseDto<WithdrawEntity>> {
    const { skip, take, allowCount, sort: order, startTime } = params;
    const filter: Prisma.WithdrawalOrderWhereInput = {
      userId: sub,
      updatedAt: startTime
        ? {
            gte: startTime,
          }
        : undefined,
      withdrawalSource: source ?? undefined,
    };
    const withdrawalFindInputs: Prisma.WithdrawalOrderFindManyArgs = {
      where: filter,
      orderBy: [
        this.stringService.toPrismaOrderByObject(order),
        { updatedAt: 'desc' },
      ],
      skip,
      take,
      include: { user: true },
    };

    try {
      if (allowCount) {
        const [withdrawals, count] = await this.prisma.$transaction([
          this.prisma.withdrawalOrder.findMany(withdrawalFindInputs),
          this.prisma.withdrawalOrder.count({ where: filter }),
        ]);

        const dataFormat = withdrawals.map((withdrawal) => {
          return new WithdrawEntity(withdrawal);
        });

        return new PagedResponseDto<WithdrawEntity>(
          dataFormat,
          skip,
          take,
          count,
        );
      } else {
        const withdrawals =
          await this.prisma.withdrawalOrder.findMany(withdrawalFindInputs);

        const dataFormat = withdrawals.map((withdrawal) => {
          return new WithdrawEntity(withdrawal);
        });

        return new PagedResponseDto<WithdrawEntity>(dataFormat, skip, take, 0);
      }
    } catch (error) {
      this.logger.debug('Error Earnings Page', error);
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid query params.',
        });
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  async findById(id: string) {
    return this.prisma.withdrawalOrder.findUnique({
      where: { id },
    });
  }

  // 审核拒绝
  async reject(id: string, checkBook: RejectWithdrawDto) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: { role: true },
    });
    if (!user) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'UserNotFind',
      });
    }
    if (user.role != Role.ADMIN) {
      throw new BadRequestException({
        status: HttpStatus.FORBIDDEN,
        message: 'UserNotAuthor',
      });
    }

    const orderList = await this.prisma.withdrawalOrder.findMany({
      where: { id: { in: checkBook.ids } },
    });
    if (!orderList || orderList.length == 0) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'InvalidBookId',
      });
    }
    // 获取订单operand 拒绝后 要分别把值加回去
    for (const order of orderList) {
      if (!order.operand) continue;
      const operand = JSON.parse(order.operand);
      if (operand['code'] == 'dob' && operand['data']) {
        Object.keys(operand['data']).forEach(async (key) => {
          await this.prisma.dob.update({
            where: { id: key },
            data: {
              withdrawn: {
                decrement: operand['data'][key] ?? 0,
              },
            },
          });
        });
      } else if (operand['code'] == 'book' && operand['data']) {
        Object.keys(operand['data']).forEach(async (key) => {
          await this.prisma.cluster.update({
            where: { bookId: key },
            data: {
              withdrawn: {
                decrement: operand['data'][key] ?? 0,
              },
            },
          });
        });
      }
    }
    // 改变订单状态
    return await this.prisma.withdrawalOrder.updateMany({
      where: { id: { in: checkBook.ids } },
      data: {
        status: PaymentStatus.FAILED,
        remark: checkBook.remark,
      },
    });
  }
}
