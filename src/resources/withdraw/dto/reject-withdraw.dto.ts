import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsString } from 'class-validator';

export class RejectWithdrawDto {
  @ApiProperty({
    required: true,
    description: '拒绝原因',
  })
  @IsString()
  remark: string;

  @ApiProperty({
    required: true,
    description: '拒绝提现订单ID',
  })
  @IsArray()
  @Type(() => Array<string>)
  ids: string[];
}
