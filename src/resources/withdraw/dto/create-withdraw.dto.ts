import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class CreateWithdrawDto {
  @ApiProperty({
    required: false,
    description: '提现收款地址[web3地址或者Visa卡]',
  })
  @IsString()
  @IsOptional()
  collectionAddress: string;

  @ApiProperty({
    required: false,
    description: 'vsia 姓名',
  })
  @IsOptional()
  visaName?: string;

  @ApiProperty({
    required: false,
    description: 'vsia CVC',
  })
  @IsOptional()
  visaCVC?: string;

  @ApiProperty({
    required: true,
    description: '提现类型[METAMASK,COINBASE,VISA,BTC,ETH]',
  })
  @IsEnum($Enums.WithdrawalType)
  @IsOptional()
  withdrawalType: $Enums.WithdrawalType;
}
