import { ApiProperty } from '@nestjs/swagger';
import { $Enums, DobOrder, PayNetWork } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Exclude, Transform } from 'class-transformer';
import { ClusterEntity } from 'src/resources/clusters/entities/cluster.entity';
import { DobEntity } from 'src/resources/dobs/entities/dob.entity';
import { UserEntity } from 'src/resources/users/entities/user.entity';

export class CryptoOrderEntity implements DobOrder {
  constructor({ ...data }: Partial<CryptoOrderEntity>) {
    Object.assign(this, data);
  }

  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  network: PayNetWork;

  @ApiProperty()
  token: string;

  @ApiProperty()
  txhash: string;

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  value: Decimal;

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  cryptoPrice: Decimal;

  @Exclude()
  earningsType: $Enums.EarningsType; //任务状态[INIT:初始化 PROCESSING:待处理 FINISH:处理结束]

  @ApiProperty()
  amount: number;

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  price: Decimal;

  @ApiProperty()
  dnaData: string;

  @ApiProperty()
  dna: string;

  @ApiProperty({
    description: '等级[GOLD:金浆果,SILVER:银浆果,COPPER:铜浆果,BLUE:蓝莓]',
  })
  level: $Enums.DnaLevel;

  @ApiProperty({
    description: '状态[PENDING:进行中,DONE:已完成,FAILED:失败,CANCELED:取消]',
  })
  status: $Enums.PaymentStatus;

  @ApiProperty()
  payUserId: string;

  @ApiProperty()
  payUser?: UserEntity;

  @ApiProperty()
  clusterId: string;

  @ApiProperty()
  cluster?: ClusterEntity;

  @ApiProperty()
  paymentId: string;

  @ApiProperty()
  dobId: string;

  @ApiProperty()
  dob?: DobEntity;

  @ApiProperty()
  checkType: number;

  createdAt: Date;
  updatedAt: Date;
}
