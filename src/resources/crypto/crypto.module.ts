import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { CryptoController } from './crypto.controller';
import { CryptoService } from './crypto.service';
import { DNAService } from 'src/common/utils/dna_encode.service';

@Module({
  imports: [PrismaModule],
  controllers: [CryptoController],
  providers: [StringService, CryptoService, DNAService],
  exports: [CryptoService],
})
export class CryptoModule {}
