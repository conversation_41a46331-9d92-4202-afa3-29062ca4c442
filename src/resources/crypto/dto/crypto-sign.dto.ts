import { CredentialKeyType } from '@joyid/ckb';
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export enum SignerSignType {
  Unknown = 'Unknown',
  BtcEcdsa = 'BtcEcdsa',
  EvmPersonal = 'EvmPersonal',
  JoyId = 'JoyId',
  NostrEvent = 'NostrEvent',
  CkbSecp256k1 = 'CkbSecp256k1',
  CCC = 'CCC',
}

export class CryptoSignDto {
  @ApiProperty({
    required: true,
    description: '钱包地址',
  })
  @IsString()
  ckbAddress: string;

  @ApiProperty({
    required: true,
    description: '签名结果',
  })
  @IsString()
  signature: string;

  @ApiProperty({
    required: true,
    description: '验证链',
  })
  @IsString()
  signType: SignerSignType;

  @ApiProperty({
    required: false,
    description: 'btc|JoyId验签需要Publickey',
  })
  @IsOptional()
  @IsString()
  publicKey?: string;

  @ApiProperty({
    required: false,
    description: 'JoyId验签需要message',
  })
  @IsOptional()
  @IsString()
  joyIdMessage?: string;

  @ApiProperty({
    required: false,
    description: 'JoyId验签需要keyType',
  })
  @IsOptional()
  @IsString()
  joyIdKeyType?: CredentialKeyType;

  @ApiProperty({
    required: false,
    description: 'JoyId验签需要alg',
  })
  @IsOptional()
  @IsNumber()
  joyIdAlg?: number;
}

export class CryptoSignETHDto {
  @ApiProperty()
  @IsString()
  signature: string;

  @ApiProperty()
  @IsString()
  ckbAddress: string;
}
