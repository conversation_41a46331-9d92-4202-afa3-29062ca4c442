import {
  BadRequestException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { ConfigService } from '@nestjs/config';
import { ethers } from 'ethers';
import axios from 'axios';
import {
  getSporeById,
  predefinedSporeConfigs,
  unpackToRawSporeData,
} from '@spore-sdk/core';
import { SporesDataSource } from 'src/common/cryptoSource/spores';
import { Spore, SporeLoadKeys } from 'src/common/cryptoSource/types';
import { PayNetWork, Prisma } from '@prisma/client';
import { CryptoSignDto, SignerSignType } from './dto/crypto-sign.dto';
import { randomUUID } from 'crypto';
import { ccc, Signature } from '@ckb-ccc/core';
import { verify } from 'bitcoinjs-message';
import { payments } from 'bitcoinjs-lib';
import { DNAService } from 'src/common/utils/dna_encode.service';
import { verifySignature } from '@joyid/ckb';

@Injectable()
export class CryptoService {
  private readonly logger = new Logger(CryptoService.name);
  private chain_network;
  private cc_config_network;
  private config_eth_rpc;
  private config_eth_addr;
  private config_btc_rpc;
  private config_btc_addr;
  private config_ckb_rpc;
  private config_ckb_addr;
  private config_btc_fee_rpc;
  private config_dob_sdk_url;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private dnaService: DNAService,
  ) {
    const chain_network = configService.get<string>('BLOCK_CHAIN_NETWORK');
    this.chain_network = chain_network;

    this.config_btc_addr = configService.get<string>('OFFICIAL_ADDRESS_BTC');
    this.config_btc_rpc =
      chain_network == 'Mainnet'
        ? configService.get<string>('BTC_RPC')
        : configService.get<string>('BTC_RPC_TEST_NET');

    this.config_eth_addr = configService.get<string>('OFFICIAL_ADDRESS_ETH');
    this.config_eth_rpc =
      chain_network == 'Mainnet'
        ? configService.get<string>('ETH_RPC')
        : configService.get<string>('ETH_RPC_TEST_NET');

    this.config_ckb_addr = configService.get<string>('OFFICIAL_ADDRESS_CKB');
    this.config_ckb_rpc =
      chain_network == 'Mainnet'
        ? configService.get<string>('CKB_RPC')
        : configService.get<string>('CKB_RPC_TEST_NET');

    this.config_btc_fee_rpc = configService.get<string>('BTC_FEE_RPC');

    this.cc_config_network =
      chain_network == 'Mainnet'
        ? predefinedSporeConfigs.Mainnet
        : predefinedSporeConfigs.Testnet;

    this.config_dob_sdk_url = configService.get<string>('DOB_SDK_URL');
  }

  // TODO:Consider a sporeCount of a cluster of db traversed once a day
  async sporeCount(cid: string) {
    const flter: SporeLoadKeys = [
      '0x',
      'desc',
      Number.MAX_SAFE_INTEGER,
      undefined,
      [cid],
      undefined,
      undefined,
      undefined,
    ];
    const sporeSource = new SporesDataSource(this.cc_config_network);
    try {
      const data = await sporeSource.getSporesFor(flter);
      return data.length;
    } catch (error) {
      this.logger.debug('Find RPC Error:', error);
    }
  }

  // TODO:Consider a sporeCount of a cluster of db traversed once a day
  async sporesForCluster(cid: string) {
    const flter: SporeLoadKeys = [
      '0x',
      'desc',
      Number.MAX_SAFE_INTEGER,
      undefined,
      [cid],
      undefined,
      undefined,
      undefined,
    ];
    const sporeSource = new SporesDataSource(this.cc_config_network);
    try {
      const data = await sporeSource.getSporesFor(flter);
      return data;
    } catch (error) {
      this.logger.debug('Find RPC Error:', error);
    }
  }

  //find spore for sporeID
  async findSporeForID(sid: string) {
    let spore: Spore;
    const chainData = await getSporeById(sid, this.cc_config_network);
    const dataUnpacked = unpackToRawSporeData(chainData.data);
    spore.id = sid;
    spore.clusterId = dataUnpacked.clusterId;
    spore.content = dataUnpacked.content.toString();
    spore.contentType = dataUnpacked.contentType;
    spore.codeHash = chainData.cellOutput.lock.codeHash;
    spore.cell = chainData;
    return spore;
  }

  //Get gas for different networks
  async getFee(key: string) {
    const fee = {
      regular: 0,
      priority: 0,
    };
    try {
      switch (key) {
        case 'btc':
          const response = await axios.get(this.config_btc_fee_rpc, {
            timeout: 2000,
          });
          if (response.status == 200) {
            const btc_data = response.data;
            fee.regular = btc_data['regular'];
            fee.priority = btc_data['priority'];
          }
          break;
        case 'eth':
          const provider = new ethers.JsonRpcProvider(this.config_eth_rpc);
          const gasData = await provider.getFeeData();
          fee.regular = parseInt(ethers.formatUnits(gasData.gasPrice, 'gwei'));
          fee.priority = parseInt(
            ethers.formatUnits(gasData.maxPriorityFeePerGas, 'gwei'),
          );
          break;
      }
    } catch (error) {
      this.logger.debug('Error calling fee method:', error);
    }
    return fee;
  }

  // Timer traverses hash to obtain order status (subnetwork)
  // network[btc eth ckb]
  async findTransferByHash(network: PayNetWork, hash: string) {
    const OnChainData = {
      from: '',
      to: '',
      value: '0',
      isSuccess: false,
      error: false,
    };

    try {
      switch (network) {
        case PayNetWork.BTC:
          const btc_url = this.getRpcForConfig(this.config_btc_rpc)
            .toString()
            .replace('{hash}', hash);
          const _res_btc = await axios.get(btc_url, { timeout: 10000 });
          console.log(btc_url, _res_btc, 'btcccc');
          if (_res_btc.status == 200) {
            const btc_data = _res_btc.data;
            let from_addr;
            let to_addr;
            let value;
            if (btc_data['inputs']) {
              const from_data = btc_data['inputs'][0];
              from_addr = from_data['addresses']
                ? from_data['addresses'][0].toString()
                : from_data['prev_out']['addr'];

              const to_data = btc_data['out']
                ? btc_data['out'][0]
                : btc_data['outputs'][0];
              to_addr = to_data['addresses']
                ? to_data['addresses'][0].toString()
                : to_data['addr'].toString();

              console.log(from_data, to_data, 'aa');
              value = Math.round(to_data['value']);
            } else {
              const from_data = btc_data['vin'][0];

              from_addr = from_data['prevout']
                ? from_data['prevout']['scriptpubkey_address'].toString()
                : '';

              const to_data = btc_data['vout'][0];
              to_addr = to_data['scriptpubkey_address'].toString();

              value = Math.round(to_data['value']);
              console.log(from_data, to_data, 'aa');
            }
            //check addr
            if (to_addr == this.config_btc_addr) {
              OnChainData.from = from_addr;
              OnChainData.to = to_addr;
              OnChainData.value = value.toString();
              OnChainData.isSuccess = true;
            }
          } else {
            // 如果不是200 说明是服务得问题 放到下一次
            OnChainData.isSuccess = false;
          }
          break;
        case PayNetWork.ETH:
          const eth_url = this.getRpcForConfig(this.config_eth_rpc);
          const provider = new ethers.JsonRpcProvider(eth_url);
          const hashData = await provider.getTransaction(hash);
          console.log(hashData, 'ddd');
          if (hashData && hashData?.blockNumber && hashData?.blockNumber > 0) {
            //check addr
            if (
              hashData?.from &&
              hashData?.to.toString().toLowerCase() ==
                this.config_eth_addr.toLowerCase()
            ) {
              OnChainData.from = hashData?.from;
              OnChainData.to = hashData?.to.toLowerCase();
              OnChainData.isSuccess = true;
              OnChainData.value = hashData.value.toString();
            }
          } else {
            OnChainData.isSuccess = false;
          }
          break;
        case PayNetWork.CKB:
          const ckb_url = this.getRpcForConfig(this.config_ckb_rpc)
            .toString()
            .replace('{hash}', hash);
          const headers = {
            Accept: 'application/vnd.api+json',
            'Content-Type': 'application/vnd.api+json',
          };
          OnChainData.isSuccess = false;
          const _res_ckb = await axios.get(ckb_url, {
            timeout: 10000,
            headers: headers,
          });
          if (_res_ckb && _res_ckb.status == 200) {
            const res_data = _res_ckb.data;
            if (res_data) {
              let from_addr = '';
              let to_addr = '';
              let value = 0;
              const ckb_data = res_data['data'];
              console.log(ckb_data, 'ddd');
              if (ckb_data && ckb_data['attributes']) {
                const attributes = ckb_data['attributes'];
                // 状态是成功才行
                if (attributes['tx_status'] == 'committed') {
                  OnChainData.isSuccess = true;
                }
                if (
                  attributes['display_outputs'] &&
                  attributes['display_outputs'].length > 0
                ) {
                  const display_outputs = attributes['display_outputs'];
                  console.log(display_outputs, 'output');
                  from_addr = display_outputs[1]['address_hash'];
                  to_addr = display_outputs[0]['address_hash'];
                  value = Math.ceil(display_outputs[0]['capacity'] / 1e8);
                }
                //check addr
                console.log(from_addr, to_addr, value, 'aaaa');
                if (to_addr == this.config_ckb_addr) {
                  OnChainData.from = from_addr;
                  OnChainData.to = to_addr;
                  OnChainData.value = value.toString();
                }
              }
            }
          }
          break;
      }
    } catch (error) {
      this.logger.debug('Error calling JSON-RPC method:', error);
      OnChainData.error = true;
    }

    return OnChainData;
  }

  async decodeDNAForSporeID(sid: string) {
    const data = {
      id: Math.floor(Math.random() * 100) + 1,
      jsonrpc: '2.0',
      method: 'dob_decode',
      params: [sid],
    };
    let decode_data: string = '';
    await axios
      .post(this.config_dob_sdk_url, data, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000,
      })
      .then((response) => {
        decode_data = response.data['result']['render_output'];
      })
      .catch((error) => {
        this.logger.debug('Error calling JSON-RPC method:', error);
      });
    return decode_data;
  }

  async getSignKey(info: string) {
    // get wallet
    const oldData = await this.prisma.wallet.findFirst({
      where: { address: info, isSign: false },
      orderBy: [{ updatedAt: 'desc' }],
    });
    if (oldData) {
      // check exp time ｜ 1hour
      const expTime = new Date(oldData.updatedAt).getTime() + 3600000;
      if (expTime >= new Date().getTime()) {
        return oldData.signCode;
      }
    }
    //create code
    const newUUID = randomUUID();
    const newData = await this.prisma.wallet.upsert({
      where: {
        address: info,
      },
      create: {
        address: info,
        signCode: newUUID,
        isSign: false,
      },
      update: {
        signCode: newUUID,
        isSign: false,
      },
    });
    return newData.signCode;
  }

  async verifySign(info: CryptoSignDto): Promise<boolean> {
    // get sign code
    const signData = await this.prisma.wallet.findUnique({
      select: { signCode: true },
      where: { address: info.ckbAddress, isSign: false },
    });
    if (!signData || !signData.signCode) {
      return false;
    }
    try {
      switch (info.signType) {
        case SignerSignType.Unknown:
          return false;
        case SignerSignType.BtcEcdsa:
          if (!info.publicKey) return false;
          const { address } = payments.p2pkh({
            pubkey: Buffer.from(info.publicKey, 'hex'),
          });
          return verify(signData.signCode, address, info.signature);
        case SignerSignType.EvmPersonal:
          const hashMsg = ethers.hashMessage(signData.signCode);
          const recoveredAddress = ethers.recoverAddress(
            hashMsg,
            info.signature,
          );
          return (
            recoveredAddress.toLowerCase() === info.ckbAddress.toLowerCase()
          );
        case SignerSignType.JoyId:
          return await verifySignature({
            signature: info.signature,
            pubkey: info.publicKey,
            message: info.joyIdMessage,
            challenge: signData.signCode,
            alg: info.joyIdAlg,
            keyType: info.joyIdKeyType,
          });
        case SignerSignType.NostrEvent:
          return true;
        case SignerSignType.CkbSecp256k1:
          return true;
        case SignerSignType.CCC:
          const signature: Signature = JSON.parse(info.signature);
          return await ccc.Signer.verifyMessage(signData.signCode, signature);
        default:
          return false;
      }
    } catch (err) {
      this.logger.debug(err);
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: err.code,
      });
    }
  }

  // 获取本地dna
  async getLocalDna(pattern: any, data: Record<string, any>): Promise<string> {
    const traits_base = this.dnaService.decodeTraitSchema(pattern);
    const dna_string = this.dnaService.encodeDnaPattern(traits_base, data);
    return dna_string.dna;
  }

  // 获取rust-dna
  async getRustServiceDna(
    pattern: any,
    data: Record<string, any>,
  ): Promise<string> {
    try {
      const data_string = JSON.stringify(JSON.stringify(data));
      const reqData = {
        id: Math.floor(Math.random() * 100) + 1,
        jsonrpc: '2.0',
        method: 'encode_dna_for_pattern',
        params: [pattern, data_string],
      };
      let dna_data: string = '';
      await axios
        .post(this.config_dob_sdk_url, reqData, {
          headers: { 'Content-Type': 'application/json' },
          timeout: 5000,
        })
        .then((response) => {
          dna_data = response.data['result'];
        })
        .catch((error) => {
          this.logger.debug('Error calling JSON-RPC method:', error);
          throw new BadRequestException({
            status: HttpStatus.BAD_REQUEST,
            message: 'SDK JSON-RPC Error!',
          });
        });

      return dna_data;
    } catch (error) {
      this.logger.error(error);
    }
  }

  //解码rpc数字 随机返回一个rpc_url
  private getRpcForConfig(rpcJson: string) {
    const rpcList: Array<string> = rpcJson ? JSON.parse(rpcJson) : [];
    if (rpcList.length == 0) return '';
    const randomIndex = Math.floor(Math.random() * rpcList.length);
    return rpcList[randomIndex];
  }

  // 获取币安接口数据
  async getBinanceApi() {
    try {
      const response = await axios.get(
        'https://data-api.binance.vision/api/v3/ticker/price?symbols=["ETHUSDT","BTCUSDT","CKBUSDT"]',
        {
          timeout: 10000,
        },
      );
      if (response.status == 200) {
        const data = response.data;
        if (data.length > 0) {
          const config = await this.prisma.golbalConfig.findFirst();
          const updateData: Prisma.GolbalConfigCreateInput = {};
          for (const one of data) {
            if (one['symbol'] == 'BTCUSDT') {
              updateData.btcPrice = one['price'];
            } else if (one['symbol'] == 'ETHUSDT') {
              updateData.ethPrice = one['price'];
            } else if (one['symbol'] == 'CKBUSDT') {
              updateData.ckbPrice = one['price'];
            }
          }
          if (updateData) {
            await this.prisma.golbalConfig.upsert({
              where: { id: config ? config.id : randomUUID() },
              create: { ...updateData },
              update: updateData,
            });
          }
        }
      }
    } catch (error) {
      this.logger.debug('获取binance API 失败' + error.toString());
    }
  }
}
