import { Controller, Get, Param } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from 'src/iam/auth/decorators/public.decorators';
import { CryptoService } from './crypto.service';

@Controller('crypto')
@ApiTags('crypto')
export class CryptoController {
  constructor(private readonly cryptoService: CryptoService) {}

  @Get('/sporeCount/:cid')
  @Public()
  @ApiOperation({
    summary: '根据clusterID获取cluster下所有spore',
    description:
      '将遍历CKB链上的所有孢子,选择符合条件的孢子,因此需要一定的等待时间,请耐心等待<br/>公共接口:允许在不登录情况下获取',
  })
  sporeCount(@Param('cid') cid: string) {
    return this.cryptoService.sporeCount(cid);
  }

  @Get(':key/fee')
  @Public()
  @ApiOperation({
    summary: '获取gasFee',
    description:
      '获取不同链上的gasFee[btc|eth]<br/>公共接口:允许在不登录情况下获取',
  })
  getFee(@Param('key') key: string) {
    return this.cryptoService.getFee(key);
  }
}
