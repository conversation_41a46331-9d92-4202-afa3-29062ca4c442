import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { Secp256k1WalletService } from 'src/common/utils/secp256k1.service';
import { ClustersController } from './clusters.controller';
import { ClustersService } from './clusters.service';
import { DobLogsService } from '../dobs/dobLogs.service';

@Module({
  imports: [PrismaModule],
  controllers: [ClustersController],
  providers: [
    StringService,
    DobLogsService,
    ClustersService,
    Secp256k1WalletService,
  ],
})
export class ClustersModule {}
