import { Controller, Get, Param } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiPaging } from 'src/common/decorators/api-paging.decorator';
import { PagingQuery } from 'src/common/decorators/paging-query.decorator';
import { Public } from 'src/iam/auth/decorators/public.decorators';
import { ClustersService } from './clusters.service';
import { ClusterEntity } from './entities/cluster.entity';
import { QueryClusterParamDto } from './dto/query-cluster-param.dto';

@Controller('clusters')
@ApiTags('clusters')
export class ClustersController {
  constructor(private readonly clustersService: ClustersService) {}

  @Get()
  @ApiPaging(ClusterEntity, QueryClusterParamDto)
  @ApiOperation({
    summary: '市场列表',
    description:
      '支持分页的不用查询维度,<br/>公共接口：需要在不登录的情况下展示市场列表',
  })
  @Public()
  async findClusters(
    @PagingQuery(QueryClusterParamDto) params: QueryClusterParamDto,
  ) {
    return await this.clustersService.findPagination(params);
  }

  // delete（The exposed interface is risky and does not meet the audit logic）
  // @Post('create_cluster_256k1')
  // @ApiOperation({
  //   summary: 'Create Cluster For Secp256K1 Wallet',
  //   description:
  //     'Use a hosted wallet to create a cluster. Note: This only works if you do not use a web3 wallet.If you are connected or bound to a web3 wallet, use a wallet to create the cluster',
  // })
  // @ApiOkResponse({ type: ClusterEntity })
  // async createClusterFor256K1(@Body() clusterInfo: Create256K1ClusterDto) {
  //   return new ClusterEntity(
  //     await this.clustersService.createClusterFor256K1(clusterInfo),
  //   );
  // }

  @Get(':bid')
  @ApiOperation({
    summary: '根据book-ID 获取cluster',
    description: '公共接口：需要在不登录的情况下展示书籍详情',
  })
  @Public()
  @ApiOkResponse({ type: ClusterEntity })
  async findOneByBID(@Param('bid') bid: string) {
    return new ClusterEntity(await this.clustersService.findOne(bid));
  }

  @Get(':cid')
  @ApiOperation({
    summary: '根据cluster-ID 获取cluster',
    description:
      '如果缓存中没有集群，则返回链上的集群信息，id:-1表示链上<br/>公共接口：允许在不登录的情况下获取',
  })
  @Public()
  @ApiOkResponse({ type: ClusterEntity })
  async findOne(@Param('cid') cid: string) {
    return new ClusterEntity(await this.clustersService.findOne(cid));
  }

  @Get('price/:cid')
  @Public()
  @ApiOperation({
    summary: '根据ClusterID获取Cluster链上价格',
    description:
      '这是区块链上的交易费用|不是账本成本，请注意展示<br/>公共接口：允许在不登录情况下获取',
  })
  @ApiOkResponse({ type: ClusterEntity })
  async findOnePrice(@Param('cid') cid: string) {
    return new ClusterEntity(await this.clustersService.findOnePrice(cid));
  }
}
