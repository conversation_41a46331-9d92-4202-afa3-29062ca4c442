import { dob } from '@ckb-ccc/spore';
import { helpers } from '@ckb-lumos/lumos';
import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BookStatus, Prisma } from '@prisma/client';
import {
  createCluster,
  getClusterById,
  predefinedSporeConfigs,
  unpackToRawClusterData,
} from '@spore-sdk/core';
import { PrismaService } from 'nestjs-prisma';
import { PagedResponseDto } from 'src/common/dtos/paged-response.dto';
import { Secp256k1WalletService } from 'src/common/utils/secp256k1.service';
import { StringService } from 'src/common/utils/string.service';
import { BookEntity } from '../books/entities/book.entity';
import { Create256K1ClusterDto } from './dto/create-cluster.dto';
import { QueryClusterParamDto } from './dto/query-cluster-param.dto';
import { ccc } from '@ckb-ccc/core';

@Injectable()
export class ClustersService {
  private readonly logger = new Logger(ClustersService.name);
  private config_secp256k1_key;
  private config_network;
  private dob_decoders;
  private level_icon_config;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private secp256k1Wallet: Secp256k1WalletService,
    private stringService: StringService,
  ) {
    this.config_secp256k1_key = configService.get<string>(
      'SECP256K1_PRIVATE_KEY',
    );
    this.level_icon_config = JSON.parse(
      configService.get<string>('LEVEL_ICON_CONFIG'),
    );
    if (configService.get<string>('BLOCK_CHAIN_NETWORK') == 'Mainnet') {
      this.config_network = predefinedSporeConfigs.Mainnet;
      this.dob_decoders = Object.freeze({
        ['dob0']: {
          type: 'code_hash',
          hash: '0x13cac78ad8482202f18f9df4ea707611c35f994375fa03ae79121312dda9925c',
        },
        ['dob1']: {
          type: 'code_hash',
          hash: '0xda3525549b72970b4c95f5b5749357f20d1293d335710b674f09c32f7d54b6dc',
        },
      });
    } else {
      this.config_network = predefinedSporeConfigs.Testnet;
      this.dob_decoders = Object.freeze({
        ['dob0']: {
          type: 'code_hash',
          hash: '0x13cac78ad8482202f18f9df4ea707611c35f994375fa03ae79121312dda9925c',
        },
        ['dob1']: {
          type: 'code_hash',
          hash: '0xda3525549b72970b4c95f5b5749357f20d1293d335710b674f09c32f7d54b6dc',
        },
      });
    }
  }

  async findPagination(
    params: QueryClusterParamDto,
  ): Promise<PagedResponseDto<BookEntity>> {
    // search book table
    const {
      skip,
      take,
      allowCount,
      sort: order,
      keyword,
      minPrice,
      maxPrice,
      phase,
      category,
      language,
    } = params;
    const filter: Prisma.BookWhereInput = {
      AND: [
        { status: BookStatus.PUBLISHED },
        { phase: phase == 'BLUE' ? undefined : phase },
        { category: category ?? undefined },
        { language: language ?? undefined },
        {
          OR: [
            { title: keyword ? { contains: keyword } : undefined },
            { user: { name: keyword ? { contains: keyword } : undefined } },
          ],
        },
      ],
    };

    const orderBys = [];
    if (!order.includes('price')) {
      const strOrder = this.stringService.toPrismaOrderByObject(order);
      if (strOrder) orderBys.push(strOrder);
    }

    if (phase == 'GOLD') {
      if (minPrice || maxPrice) {
        filter.price = {
          goldPrice: {
            gte: minPrice ?? undefined,
            lte: maxPrice ?? undefined,
          },
        };
      }
      if (order.includes('price')) {
        orderBys.push({
          price: {
            goldPrice: order.includes('desc') ? 'desc' : 'asc',
          },
        });
      }
    } else if (phase == 'SILVER') {
      if (minPrice || maxPrice) {
        filter.price = {
          silverPrice: {
            gte: minPrice ?? undefined,
            lte: maxPrice ?? undefined,
          },
        };
      }
      if (order.includes('price')) {
        orderBys.push({
          price: {
            silverPrice: order.includes('desc') ? 'desc' : 'asc',
          },
        });
      }
    } else if (phase == 'COPPER') {
      if (minPrice || maxPrice) {
        filter.price = {
          copperPrice: {
            gte: minPrice ?? undefined,
            lte: maxPrice ?? undefined,
          },
        };
      }
      if (order.includes('price')) {
        orderBys.push({
          price: {
            copperPrice: order.includes('desc') ? 'desc' : 'asc',
          },
        });
      }
    } else if (phase == 'BLUE') {
      if (minPrice || maxPrice) {
        filter.price = {
          bluePrice: {
            gte: minPrice ?? 0,
            lte: maxPrice ?? undefined,
          },
        };
      }
      if (order.includes('price')) {
        orderBys.push({
          price: {
            bluePrice: order.includes('desc') ? 'desc' : 'asc',
          },
        });
      }
    } else {
      if (minPrice || maxPrice) {
        filter.price = {
          bluePrice: {
            gte: minPrice ?? undefined,
            lte: maxPrice ?? undefined,
          },
        };
      }
      if (order.includes('price')) {
        orderBys.push({
          price: {
            bluePrice: order.includes('desc') ? 'desc' : 'asc',
          },
        });
      }
    }

    const BookFindInputs: Prisma.BookFindManyArgs = {
      where: filter,
      orderBy: orderBys,
      skip,
      take,
      include: { cluster: true, user: true, price: true },
    };

    try {
      if (allowCount) {
        const [books, count] = await this.prisma.$transaction([
          this.prisma.book.findMany(BookFindInputs),
          this.prisma.book.count({ where: filter }),
        ]);

        const dataFormat = books.map((one) => {
          return new BookEntity(one);
        });

        return new PagedResponseDto<BookEntity>(dataFormat, skip, take, count);
      } else {
        const books = await this.prisma.book.findMany(BookFindInputs);

        const dataFormat = books.map((one) => {
          return new BookEntity(one);
        });

        return new PagedResponseDto<BookEntity>(dataFormat, skip, take, 0);
      }
    } catch (error) {
      this.logger.debug('Error Cluster Page', error);
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid query params.',
        });
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  async findOneByBID(bid: string) {
    return await this.prisma.cluster.findUnique({
      where: {
        bookId: bid,
      },
    });
  }

  async findOne(cid: string) {
    const db_cluster = await this.prisma.cluster.findUnique({
      where: {
        clusterId: cid,
      },
    });
    if (!db_cluster) {
      // get data on chain
      const chainData = await getClusterById(cid, this.config_network);
      const dataUnpacked = unpackToRawClusterData(chainData.data);

      // Generate address from the lock script
      db_cluster.ownerAddress = helpers.encodeToAddress(
        chainData.cellOutput.lock,
        {
          config: this.config_network.lumos,
        },
      );
      db_cluster.description = dataUnpacked.description;
      db_cluster.name = dataUnpacked.name;
      db_cluster.creator = 'wallet';
      db_cluster.id = '-1';
    }

    return db_cluster;
  }

  async findOnePrice(cid: string) {
    return this.prisma.cluster.findUnique({
      select: {
        name: true,
        ownerAddress: true,
        ckbCellCost: true,
        mintFee: true,
        perMintAmount: true,
      },
      where: {
        clusterId: cid,
      },
    });
  }

  async createClusterFor256K1(clusterInfo: Create256K1ClusterDto) {
    // Secp256K1 wallet
    const wallet = this.secp256k1Wallet.createSecp256k1Wallet(
      this.config_secp256k1_key,
      this.config_network,
    );
    const clusterDescription = clusterInfo.description;
    const dob0Pattern: dob.PatternElementDob0[] = [
      {
        traitName: 'ID',
        dobType: 'Number',
        dnaOffset: 0,
        dnaLength: 6,
        patternType: 'rawNumber',
      },
      {
        traitName: 'Level',
        dobType: 'String',
        dnaOffset: 6,
        dnaLength: 1,
        patternType: 'options',
        traitArgs: ['GOLD', 'SILVER', 'COPPER', 'BLUE'],
      },
      {
        traitName: 'Author',
        dobType: 'String',
        dnaOffset: 0,
        dnaLength: 1,
        patternType: 'options',
        traitArgs: [clusterInfo.author],
      },
      // 暂时去调isbn
      // {
      //   traitName: 'ISBN',
      //   dobType: 'String',
      //   dnaOffset: 0,
      //   dnaLength: 1,
      //   patternType: 'options',
      //   traitArgs: [clusterInfo.ISBN],
      // },
      // 新增source-存btcfs
      {
        traitName: 'Source',
        dobType: 'String',
        dnaOffset: 1,
        dnaLength: 1,
        patternType: 'options',
        traitArgs: [clusterInfo.sourceUrl],
      },
    ];
    const dob1Pattern: dob.PatternElementDob1[] = [
      {
        imageName: 'IMAGE.0',
        svgFields: 'attributes',
        traitName: '',
        patternType: 'raw',
        traitArgs: `xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256"`,
      },
      {
        imageName: 'IMAGE.0',
        svgFields: 'elements',
        traitName: '',
        patternType: 'raw',
        traitArgs: `<image width='100%' height='100%' preserveAspectRatio='none' href='${clusterInfo.coverImg}' />`,
      },
      {
        imageName: 'IMAGE.0',
        svgFields: 'elements',
        traitName: 'Level',
        patternType: 'options',
        traitArgs: [
          [
            'GOLD',
            `<image width='88' height='88' y='-10' x='-10' href='${this.level_icon_config['GOLD']}' />`,
          ],
          [
            'SILVER',
            `<image width='88' height='88' y='-10' x='-10' href='${this.level_icon_config['SILVER']}' />`,
          ],
          [
            'COPPER',
            `<image width='88' height='88' y='-10' x='-10' href='${this.level_icon_config['COPPER']}' />`,
          ],
          [
            'BLUE',
            `<image width='88' height='88' y='-10' x='-10' href='${this.level_icon_config['BLUE']}' />`,
          ],
        ],
      },
    ];
    const dob1: dob.Dob1 = {
      description: clusterDescription,
      dob: {
        ver: 1,
        decoders: [
          {
            decoder: this.dob_decoders['dob0'],
            pattern: dob0Pattern,
          },
          {
            decoder: this.dob_decoders['dob1'],
            pattern: dob1Pattern,
          },
        ],
      },
    };
    const dob1ClusterDescription = dob.encodeClusterDescriptionForDob1(dob1);
    //create Cluster Transfer
    const { txSkeleton, outputIndex } = await createCluster({
      data: {
        name: clusterInfo.name,
        description: dob1ClusterDescription,
      },
      fromInfos: [wallet.address],
      toLock: wallet.lock,
      config: this.config_network,
    });
    const hash = await wallet.signAndSendTransaction(txSkeleton);
    const clusterCell = txSkeleton.get('outputs').get(outputIndex)!;
    const clusterId = clusterCell.cellOutput.type!.args;
    const clusterContract = clusterCell.cellOutput.type!.codeHash;
    const typeHash = ccc.Script.from(clusterCell.cellOutput.type!).hash();
    const cluster: Prisma.ClusterCreateInput = {
      name: clusterInfo.name,
      description: dob1ClusterDescription,
      decoder: JSON.stringify(this.dob_decoders['dob0']) ?? '',
      pattern: JSON.stringify(dob0Pattern) ?? '',
      clusterId: clusterId,
      contractHash: clusterContract,
      typeHash: typeHash,
      creator: clusterInfo.uid,
      hash: hash,
      ownerAddress: wallet.address,
      timestamp: new Date().toString(),
      book: {
        connect: {
          id: clusterInfo.bookId,
        },
      },
      ckbCellCost: 15000,
      mintFee: 3000,
      sporeCount: 0,
      perMintAmount: clusterInfo.isPublic ? clusterInfo.minCkb : 0,
      withdrawn: 0,
    };
    //Insert DB
    return this.prisma.cluster.create({ data: cluster });
  }
}
