import { ApiProperty } from '@nestjs/swagger';
import { Cluster } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Exclude, Transform } from 'class-transformer';

export class ClusterEntity implements Cluster {
  constructor({ ...partial }: Partial<ClusterEntity>) {
    Object.assign(this, partial);
  }
  typeHash: string;

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  withdrawn: Decimal;

  @ApiProperty({
    description: '合约地址（链上）',
  })
  contractHash: string;

  @Exclude()
  decoder: string;

  @Exclude()
  pattern: string;

  @Exclude()
  sporeCount: number;

  @ApiProperty({
    description: 'ckb Gas Free（链上）',
  })
  ckbCellCost: number;

  @ApiProperty({
    description: 'mint Gas Free（链上）',
  })
  mintFee: number;

  @ApiProperty({
    description: '购买支付作者 Gas Free（链上）',
  })
  perMintAmount: number;

  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  createdAt: Date;
  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  bookId: string;

  @ApiProperty({
    description: '作品ID（链上）',
  })
  clusterId: string;

  @ApiProperty({
    description: '创作者ID',
  })
  creator: string;

  @ApiProperty({
    description: '哈希地址（链上）',
  })
  hash: string;

  @Exclude()
  ownerAddress: string;

  @ApiProperty({
    description: '简介数据（链上）',
  })
  description: string;

  @ApiProperty()
  timestamp: string;
}
