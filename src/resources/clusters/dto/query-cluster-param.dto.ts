import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { Expose, Type } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, IsString, Matches } from 'class-validator';

/**
 * Class contains fields to perform common Read operation
 * (Paging, Search, Sort)
 */
export class QueryClusterParamDto {
  // Pagination
  @ApiProperty({
    required: false,
    description: 'Skip number of items.',
  })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  @Expose()
  skip?: number = 0;

  @ApiProperty({
    required: false,
    description: 'Limit amount of items to be returned.',
  })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  @Expose()
  take?: number = 10;

  allowCount?: boolean = true; // if true, allow count all row in query

  // sort
  @ApiProperty({
    required: false,
    description:
      '排序[价格从低到高:price_asc,价格从高到地:price_desc,最新发布:updatedAt_desc,购买最多:sales_desc,浏览最多:views_desc]',
    example: 'updatedAt_desc',
  })
  @IsOptional()
  @Matches(/^\w+(_desc|_asc)+$/, {
    message: "'sort' must end with _desc or _asc",
  })
  @Expose()
  sort?: string = 'updatedAt_desc';

  // search
  @ApiProperty({
    required: false,
    name: 'keyword',
    description: '书籍名称|作者姓名 关键词',
  })
  @IsOptional()
  @Expose({ name: 'keyword' })
  keyword?: string;

  @ApiProperty({
    required: false,
    name: 'phase',
    description: '发布阶段',
    enum: $Enums.DnaLevel,
  })
  @IsOptional()
  @IsEnum($Enums.DnaLevel)
  @Expose()
  phase?: $Enums.DnaLevel;

  @ApiProperty({
    required: false,
    name: 'minPrice',
    description: '最小价格',
  })
  @IsOptional()
  @Expose()
  minPrice: number;

  @ApiProperty({
    required: false,
    name: 'maxPrice',
    description: '最大价格',
  })
  @IsOptional()
  @Expose()
  maxPrice: number;

  @ApiProperty({
    required: false,
    name: 'category',
    description: '书籍分类',
  })
  @IsOptional()
  @IsString()
  @Expose()
  category?: string;

  @ApiProperty({
    required: false,
    name: 'language ',
    description: '书籍语言',
  })
  @IsOptional()
  @IsString()
  @Expose()
  language?: string;
}
