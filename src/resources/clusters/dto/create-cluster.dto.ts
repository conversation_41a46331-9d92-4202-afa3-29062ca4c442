import { ApiProperty, PartialType } from '@nestjs/swagger';
import { DnaLevel } from '@prisma/client';
import { Expose, Type } from 'class-transformer';
import { IsInt, IsNumber, IsObject, IsOptional } from 'class-validator';

export class Create256K1ClusterDto {
  @ApiProperty({
    required: true,
    description: 'User ID',
  })
  @Type(() => String)
  @IsOptional()
  @Expose()
  uid?: string;

  // Pagination
  @ApiProperty({
    required: true,
    description: 'Cluster Name',
  })
  @Type(() => String)
  @IsOptional()
  @Expose()
  name?: string;

  @ApiProperty({
    required: false,
    description: 'Book Author',
  })
  @Type(() => String)
  @IsOptional()
  @Expose()
  author?: string;

  @ApiProperty({
    required: false,
    description: 'Book ISBN',
  })
  @Type(() => String)
  @IsOptional()
  @Expose()
  ISBN?: string;

  @ApiProperty({
    required: false,
    description: 'Book Source URL',
  })
  @Type(() => String)
  @IsOptional()
  @Expose()
  sourceUrl?: string;

  @ApiProperty({
    required: true,
    description: 'Cluster description',
  })
  @Type(() => String)
  @IsOptional()
  @Expose()
  description?: string;

  @ApiProperty({
    required: false,
    description: 'IS ACP Cluster ',
  })
  @Type(() => Boolean)
  @IsOptional()
  @Expose()
  isPublic?: boolean = false;

  @ApiProperty({
    required: false,
    description: 'Acp Cluster minCkb',
  })
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  @Expose()
  minCkb?: number;

  @ApiProperty({
    required: true,
    description: 'Book ID',
  })
  @Type(() => String)
  @IsOptional()
  @Expose()
  bookId?: string;

  @ApiProperty({
    required: true,
    description: 'Total Spore Config (-1:Express unlimited)',
    example: {
      GOLD: { total: 1000, price: 66 },
      SILVER: { total: 500, price: 88 },
      COPPER: { total: 100, price: 188 },
      BLUE: { total: -1, price: 50 },
    },
  })
  @Type(() => Object)
  @IsObject()
  @Expose()
  priceInfo?: Record<DnaLevel, TotalConfigDto>;

  @ApiProperty({
    required: true,
    description: '封面url',
  })
  @Type(() => String)
  @IsOptional()
  @Expose()
  coverImg?: string;
}

export class CreateClusterDto extends PartialType(Create256K1ClusterDto) {
  @ApiProperty({
    required: true,
    description: 'Cluster ID',
  })
  @Type(() => String)
  @IsOptional()
  clusterId: string;

  @ApiProperty({
    required: true,
    description: 'Transfer Hash',
  })
  @Type(() => String)
  @IsOptional()
  txHash: string;

  @ApiProperty({
    required: true,
    description: 'wallet address',
  })
  @Type(() => String)
  @IsOptional()
  wallet: string;
}

export class TotalConfigDto {
  @ApiProperty()
  @IsInt()
  total: number;

  @ApiProperty()
  @IsNumber()
  price: number;
}
