import { ApiProperty, PartialType } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';
import { QueryParamDto } from 'src/common/dtos/query-param.dto';

export class QuerySporeDobLogsDto extends PartialType(QueryParamDto) {
  // search
  @ApiProperty({
    required: false,
    name: '操作符',
    description: '操作选项 0:default 1:create 2:transfer',
  })
  @IsOptional()
  @IsInt()
  @Expose({ name: 'opcode' })
  operation?: number;

  // search
  @ApiProperty({
    required: false,
    name: 'Cluster id[book下有该字段|获取通过bookID获取cluster]',
  })
  @IsOptional()
  @Expose({ name: 'clusterId' })
  clusterID?: string;
}
