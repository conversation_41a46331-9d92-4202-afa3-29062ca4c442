import { ApiProperty, PartialType } from '@nestjs/swagger';
import { $Enums, DnaLevel } from '@prisma/client';
import { Expose } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryParamDto } from 'src/common/dtos/query-param.dto';

export class QueryMyDobDto extends PartialType(QueryParamDto) {
  // search
  @ApiProperty({
    enum: $Enums.DnaLevel,
    required: false,
    description: 'Dob等级(版权等级)',
  })
  @Expose()
  @IsOptional()
  @IsEnum($Enums.DnaLevel)
  level?: DnaLevel;
}
