import { ApiProperty, PartialType } from '@nestjs/swagger';
import { $Enums, DnaLevel, PayNetWork } from '@prisma/client';
import { Expose, Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  Max,
} from 'class-validator';

export class CreateSporeDto {
  @ApiProperty({
    required: true,
    description: 'User ID[用户登录后返回的ID]',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  uid?: string;

  // Pagination
  @ApiProperty({
    required: true,
    description:
      'Cluster ID[这个字段在books下获取|也可以通过bookID去获取cluster信息]',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  clusterId?: string;
}

// Create DNA
export class CreateDnaDto extends PartialType(CreateSporeDto) {
  @ApiProperty({
    required: true,
    description: '需要一个字段对于一个值',
    example: {
      BookID: '书籍ID',
      Name: '书籍名称',
      ISBN: '书籍ISBN 13位',
      Uri: '书籍PDF的s3url地址',
      Lang: '书籍语言',
    },
  })
  @Type(() => Object)
  @IsOptional()
  @IsObject()
  data: Record<string, any>;

  @ApiProperty({
    required: true,
    default: $Enums.DnaLevel.BLUE,
    enum: $Enums.DnaLevel,
    description: '购买等级[GOLD,SILVER,COPPER,BLUE]',
  })
  @IsEnum($Enums.DnaLevel)
  level: DnaLevel;

  @ApiProperty({
    required: true,
    default: 1,
    description: '购买数量',
  })
  @IsNumber()
  @Max(100) // 限制购买数量
  amount: number;

  // @ApiProperty({
  //   required: true,
  //   default: 1,
  //   description: '支付金额',
  // })
  // @IsNumber()
  // price: number;

  @ApiProperty({
    required: true,
    enum: $Enums.PayNetWork,
    description: '哪条链[BTC,ETH,CKB,USD]',
  })
  @IsEnum($Enums.PayNetWork)
  network: PayNetWork;

  @ApiProperty({
    required: true,
    description: 'clusterID',
  })
  @Type(() => String)
  @IsNotEmpty()
  clusterId: string;
}

/**
 * Create Spore
 */
export class CreateSporeDobDto {
  // Pagination
  @ApiProperty({
    required: true,
    description: 'OrderID[在dna接口成功后的返回值中取orderID]',
  })
  @Type(() => String)
  @IsNotEmpty()
  orderID: string;

  // txHash
  @ApiProperty({
    required: true,
    description: '这是钱包支付成功后的交易hash',
  })
  @Type(() => String)
  @IsNotEmpty()
  txhash: string;

  // tx network
  @ApiProperty({
    default: $Enums.PayNetWork.ETH,
    enum: $Enums.PayNetWork,
    description: '通过那个链进行的支付',
  })
  @IsEnum($Enums.PayNetWork)
  network: PayNetWork;
}

//save wallet spore
export class SaveSpore extends PartialType(CreateSporeDto) {
  @ApiProperty({
    required: true,
    description: 'ckb链的钱包地址',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  address: string;

  @ApiProperty({
    required: true,
    description: 'mint-spore后的sporeID',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  sporeId: string;

  @ApiProperty({
    required: true,
    description: 'mint-spore后的txHash',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  txhash: string;
}
