import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

/**
 * Transfer Spore
 */
export class TransferSporeDobDto {
  @ApiProperty({
    required: true,
    description: '持有的版权-dobID',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  sporeId?: string;

  @ApiProperty({
    required: true,
    description: '接受者CKB地址-用户填写',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  toCKBAddress?: string;
}
