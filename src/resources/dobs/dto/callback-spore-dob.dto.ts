import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional } from 'class-validator';

/**
 * CallBack find Spore
 */
export class CallbackFindDobDto {
  @ApiProperty({
    required: false,
    description: '用户支付给平台地址ckb的转账HASH',
  })
  @IsOptional()
  @IsArray()
  @Type(() => Array<string>)
  @IsNotEmpty()
  @Expose()
  txHashs?: string[];

  @ApiProperty({
    required: false,
    description: 'visa支付的Paymenet id',
  })
  @IsOptional()
  @IsArray()
  @Type(() => Array<string>)
  @IsNotEmpty()
  @Expose()
  paymentIDs?: string[];
}

/**
 * CallBack Publish Spore
 */
export class CallbackPublishDobDto {
  @ApiProperty({
    required: true,
    description: '订单ID',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  dobOrderId?: string;

  @ApiProperty({
    required: true,
    description: '转移spore的交易hash',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  txHash?: string;

  @ApiProperty({
    required: true,
    description: '链上sporeID',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  sporeId?: string;

  @ApiProperty({
    required: true,
    description: 'spore持有者',
  })
  @Type(() => String)
  @IsNotEmpty()
  @Expose()
  owner?: string;
}
