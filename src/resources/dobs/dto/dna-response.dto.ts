import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class DnaResponseDto {
  @ApiProperty({
    description: 'DNA 加密字符串(链上使用)',
  })
  @Expose()
  dna: string;

  @ApiProperty({
    description: '项目官方收款地址',
  })
  @Expose()
  collection_address: string;

  @ApiProperty({
    description: '订单ID',
  })
  @Expose()
  orderID: string;

  @ApiProperty({
    description: '支付金额(钱包)',
  })
  @Expose()
  value: string;

  constructor(
    dna: string,
    collection_address: string,
    orderID: string,
    value: string,
  ) {
    this.dna = dna ?? '';
    this.collection_address = collection_address ?? '';
    this.orderID = orderID ?? '';
    this.value = value ?? '';
  }
}
