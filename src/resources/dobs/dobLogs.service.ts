import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { Prisma } from '@prisma/client';
import { PagedResponseDto } from 'src/common/dtos/paged-response.dto';
import { DobLogsEntity } from './entities/dobLogs.entity';
import { QuerySporeDobLogsDto } from './dto/query-spore-dob-logs.dto';
import { StringService } from 'src/common/utils/string.service';

@Injectable()
export class DobLogsService {
  constructor(
    private prisma: PrismaService,
    private stringService: StringService,
  ) {}

  insertLog(
    operation: number,
    operationUid: string,
    dobId: string,
    clusterId: string,
  ) {
    this.prisma.dobLogs.create({
      data: {
        operation: operation,
        operationUid: operationUid,
        dobId: dobId,
        clusterId: clusterId,
        timestamp: new Date().toString(),
      },
    });
    return true;
  }

  //Get Log For Uid
  async findAll(uid: string) {
    return this.prisma.dobLogs.findMany({ where: { operationUid: uid } });
  }

  async findLogsPagination(
    params: QuerySporeDobLogsDto,
  ): Promise<PagedResponseDto<DobLogsEntity>> {
    const {
      skip,
      take,
      allowCount,
      sort: order,
      operation,
      clusterID,
    } = params;
    const filter: Prisma.DobLogsWhereInput = {
      operation: operation > 0 ? operation : undefined,
      clusterId: clusterID ? clusterID : undefined,
    };
    const dobsFindInputs: Prisma.DobLogsFindManyArgs = {
      where: filter,
      orderBy: [
        this.stringService.toPrismaOrderByObject(order),
        { id: 'desc' },
      ],
      skip,
      take,
    };

    try {
      if (allowCount) {
        const [dob_logs, count] = await this.prisma.$transaction([
          this.prisma.dobLogs.findMany(dobsFindInputs),
          this.prisma.dobLogs.count({ where: filter }),
        ]);
        const dataFormat = dob_logs.map((one) => {
          return new DobLogsEntity(one);
        });
        return new PagedResponseDto<DobLogsEntity>(
          dataFormat,
          skip,
          take,
          count,
        );
      } else {
        const dob_logs = await this.prisma.dobLogs.findMany(dobsFindInputs);
        const dataFormat = dob_logs.map((one) => {
          return new DobLogsEntity(one);
        });
        return new PagedResponseDto<DobLogsEntity>(dataFormat, skip, take, 0);
      }
    } catch (error) {
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException('Invalid query params.');
      } else {
        throw new InternalServerErrorException();
      }
    }
  }
}
