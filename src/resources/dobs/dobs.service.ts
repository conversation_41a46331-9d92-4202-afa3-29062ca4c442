import { addressToScript } from '@nervosnetwork/ckb-sdk-utils';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  DnaLevel,
  EarningsType,
  PaymentStatus,
  PayNetWork,
  Prisma,
  Role,
} from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import {
  // bytifyRawString,
  // createSpore,
  getSporeById,
  isAddressValid,
  predefinedSporeConfigs,
  transferSpore,
} from '@spore-sdk/core';
import * as bcrypt from 'bcryptjs';
import { parseUnits } from 'ethers';
import { PrismaService } from 'nestjs-prisma';
import { PagedResponseDto } from 'src/common/dtos/paged-response.dto';
import { QueryParamDto } from 'src/common/dtos/query-param.dto';
import { Secp256k1WalletService } from 'src/common/utils/secp256k1.service';
import { StringService } from 'src/common/utils/string.service';
import { PriceEntity } from '../books/entities/price.entity';
import { CryptoService } from '../crypto/crypto.service';
import { DobEarningsEntity } from '../earnings/entities/dob-earnings.entity';
import { CreatePaymentDto } from '../payment/dto/create-payment.dto';
import { PaymentService } from '../payment/payment.service';
import { CheckoutPaymentDto } from '../print-orders/dto/checkout-payment.dto';
import { DobLogsService } from './dobLogs.service';
import {
  CallbackFindDobDto,
  CallbackPublishDobDto,
} from './dto/callback-spore-dob.dto';
import { CreateDnaDto, CreateSporeDobDto } from './dto/create-spore-dob.dto';
import { DnaResponseDto } from './dto/dna-response.dto';
import { QueryMyDobDto } from './dto/query-my-dob.dto';
import { TransferSporeDobDto } from './dto/transfer-spore-dob.dto';
import { DobBriefEntity } from './entities/dob-dec.entity';
import { DobEntity } from './entities/dob.entity';
import { DobOrderEntity } from './entities/dobOrder.entity';
// import { ccc } from '@ckb-ccc/core';
// import { createSpore } from '@ckb-ccc/spore';
export const roundsOfHashing = 10;
@Injectable()
export class DobsService {
  private readonly logger = new Logger(DobsService.name);
  private config_secp256k1_key;
  private config_network;
  private collection_address;
  private earnings_config;
  private chain_network;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private secp256k1Wallet: Secp256k1WalletService,
    private dobLogsService: DobLogsService,
    private stringService: StringService,
    private cryptoService: CryptoService,
    private paymentService: PaymentService,
  ) {
    this.config_secp256k1_key = configService.get<string>(
      'SECP256K1_PRIVATE_KEY',
    );
    this.chain_network = configService.get<string>('BLOCK_CHAIN_NETWORK');
    this.config_network =
      configService.get<string>('BLOCK_CHAIN_NETWORK') == 'Mainnet'
        ? predefinedSporeConfigs.Mainnet
        : predefinedSporeConfigs.Testnet;
    this.collection_address = {
      eth: configService.get<string>('OFFICIAL_ADDRESS_ETH'),
      btc: configService.get<string>('OFFICIAL_ADDRESS_BTC'),
      ckb: configService.get<string>('OFFICIAL_ADDRESS_CKB'),
    };
    this.earnings_config = configService.get<string>('EARNINGS_CONFIG');
  }

  async findAll(cid: string) {
    if (cid) {
      return this.prisma.dob.findMany({ where: { clusterId: cid } });
    }
    return this.prisma.dob.findMany();
  }

  async findOne(uid: string, cid: string) {
    return await this.prisma.dob.findUnique({
      where: { id: cid, userId: uid },
      include: { cluster: true, dobOrder: true, printOrder: true },
    });
  }

  async findOnebrief(sid: string) {
    const db_spore = await this.prisma.dob.findUnique({
      select: {
        createdAt: true,
        level: true,
        cluster: {
          select: {
            book: {
              select: {
                price: true,
              },
            },
          },
        },
      },
      where: {
        id: sid,
      },
    });

    if (!db_spore) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Spore Info Not Find.',
      });
    }
    const setting = JSON.parse(this.earnings_config);
    const priceConfig = db_spore.cluster.book.price ?? [];
    if (!setting || !priceConfig) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Setting Not Find.',
      });
    }
    let earnings: number;
    let price: number;
    if (
      db_spore.level == DnaLevel.GOLD &&
      setting['SILVER'] &&
      setting['COPPER']
    ) {
      // 单卡金的计算方式 :所有银收益+所有铜收益
      const silverEarnings =
        (priceConfig['silverCount'] *
          priceConfig['silverPrice'] *
          setting['SILVER']['gold']) /
        100;
      const copperEarnings =
        (priceConfig['copperCount'] *
          priceConfig['copperPrice'] *
          setting['COPPER']['gold']) /
        100;
      earnings = Number(
        Number(
          (copperEarnings + silverEarnings) / priceConfig['goldCount'],
        ).toFixed(4),
      );
      price = Number(priceConfig['goldPrice']);
    } else if (db_spore.level == DnaLevel.SILVER) {
      // 单卡银的计算方式 :所有铜收益
      const copperEarnings = Number(
        (priceConfig['copperCount'] *
          priceConfig['copperPrice'] *
          setting['COPPER']['silver']) /
          100 /
          priceConfig['silverCount'],
      ).toFixed(4);
      earnings = Number(copperEarnings);
      price = Number(priceConfig['silverPrice']);
    } else if (db_spore.level == DnaLevel.COPPER) {
      earnings = 0;
      price = Number(priceConfig['copperPrice']);
    } else if (db_spore.level == DnaLevel.BLUE) {
      earnings = 0;
      price = Number(priceConfig['bluePrice']);
    }
    return new DobBriefEntity({
      level: db_spore.level,
      createdAt: db_spore.createdAt,
      price: new Decimal(price),
      earnings: new Decimal(earnings.toFixed(4)),
    });
  }

  //dobs
  async findPagination(
    uid: string,
    params: QueryMyDobDto,
  ): Promise<PagedResponseDto<DobEntity>> {
    const { skip, take, allowCount, keyword, level } = params;
    const filter: Prisma.DobWhereInput = {
      AND: [
        { userId: uid },
        { level: level ?? undefined },
        {
          OR: [
            {
              cluster: {
                book: { title: keyword ? { contains: keyword } : undefined },
              },
            },
            { user: { name: keyword ? { contains: keyword } : undefined } },
          ],
        },
      ],
    };
    const dobsFindInputs: Prisma.DobFindManyArgs = {
      where: filter,
      orderBy: [{ createdAt: 'desc' }],
      skip,
      take,
      include: {
        cluster: {
          select: {
            clusterId: true,
            book: {
              include: {
                price: true,
                copyright: { select: { isbn: true } },
                user: { select: { name: true, id: true, avatar: true } },
              },
            },
          },
        },
      },
    };

    try {
      if (allowCount) {
        const [dobs, count] = await this.prisma.$transaction([
          this.prisma.dob.findMany(dobsFindInputs),
          this.prisma.dob.count({ where: filter }),
        ]);
        const dataFormat = dobs.map((one) => {
          if (one['cluster'] && one['cluster']['book']['price']) {
            one['price'] = new PriceEntity(one['cluster']['book']['price']);
            delete one['cluster']['book']['price'];
          }
          return new DobEntity(one);
        });
        return new PagedResponseDto<DobEntity>(dataFormat, skip, take, count);
      } else {
        const dobs = await this.prisma.dob.findMany(dobsFindInputs);

        const dataFormat = dobs.map((one) => {
          return new DobEntity(one);
        });

        return new PagedResponseDto<DobEntity>(dataFormat, skip, take, 0);
      }
    } catch (error) {
      this.logger.debug('Error Spore Page:', error);
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid query params.',
        });
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  async findUserSpore(uid: string, sporeId: string) {
    const dobOne = await this.prisma.dob.findUnique({
      where: {
        dobId: sporeId,
        userId: uid,
      },
    });
    return dobOne ?? null;
  }

  // 不用这个接口--统一到createSporeForWallet 操作
  // async saveSpore(sporeInfo: SaveSpore) {
  //   // check dna order
  //   const dobOrder = await this.prisma.dobOrder.findFirst({
  //     where: {
  //       cluster: {
  //         clusterId: sporeInfo.clusterId,
  //       },
  //       payUserId: sporeInfo.uid,
  //       status: PaymentStatus.PENDING,
  //     },
  //   });
  //   if (!dobOrder) {
  //     throw new BadRequestException({
  //       status: HttpStatus.BAD_REQUEST,
  //       message: 'Dob Order Not Find',
  //     });
  //   }

  //   const spore: Prisma.DobCreateInput = {
  //     dobId: sporeInfo.sporeId,
  //     ownerAddress: sporeInfo.address,
  //     cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60, //1 hour
  //     utxoData: 'null',
  //     dna: dobOrder.dna,
  //     level: dobOrder.level,
  //     metadata: dobOrder.dnaData,
  //     txHash: sporeInfo.txhash,
  //     withdrawn: 0,
  //     cluster: {
  //       connect: {
  //         clusterId: sporeInfo.clusterId,
  //       },
  //     },
  //     user: {
  //       connect: {
  //         id: sporeInfo.uid,
  //       },
  //     },
  //     dobOrder: {
  //       connect: { id: dobOrder.id },
  //     },
  //   };
  //   dobOrder.status = PaymentStatus.DONE;
  //   dobOrder.network = 'ETH';
  //   dobOrder.txhash = sporeInfo.txhash;
  //   // save spore and change order
  //   const [dob] = await this.prisma.$transaction([
  //     this.prisma.dob.create({ data: spore }),
  //     this.prisma.dobOrder.update({
  //       where: { id: dobOrder.id },
  //       data: dobOrder,
  //     }),
  //   ]);
  //   // Insert Log
  //   try {
  //     this.dobLogsService.insertLog(
  //       1,
  //       sporeInfo.uid,
  //       sporeInfo.sporeId,
  //       sporeInfo.clusterId,
  //     );
  //   } catch (error) {
  //     this.logger.warn('Insert DOB Logs Error:' + error);
  //   }
  //   return dob;
  // }

  // 获取dna

  async getDnaForCluster(dnaInfo: CreateDnaDto) {
    if (!dnaInfo.network) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Network Not Found!',
      });
    }
    // cluster
    const cluster = await this.prisma.cluster.findUnique({
      select: { clusterId: true, pattern: true, bookId: true, id: true },
      where: { clusterId: dnaInfo.clusterId },
    });
    if (!cluster) throw new BadRequestException('Cluster Not Found!');
    // 检查价格配置
    const priceInfo = await this.prisma.price.findUnique({
      where: { bookId: cluster.bookId },
    });
    if (!priceInfo) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Book Price Not Found!',
      });
    }

    // 检测开始销售时间
    const now = new Date();
    if (now <= priceInfo.openTime) {
      //检测白名单
      const user = await this.prisma.user.findUnique({
        where: { id: dnaInfo.uid },
      });
      const wallet = await this.prisma.wallet.findUnique({
        where: { userId: dnaInfo.uid },
      });

      //白名单数据
      const whiteData = await this.prisma.whiteList.findFirst({
        where: {
          bookId: cluster.bookId,
          OR: [
            { address: wallet ? wallet.address : undefined },
            { email: user.email },
          ],
        },
      });
      // 不在白名单内 或者 在白名单 但是未到白名单时间
      if (!whiteData) {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          message: 'It is not yet the designated time for purchase',
        });
      } else {
        // 如果在白名单内
        if (whiteData && now <= priceInfo.whiteOpenTime) {
          throw new BadRequestException({
            status: HttpStatus.BAD_REQUEST,
            message: 'It is not yet the designated time for purchase',
          });
        }
        // 如果限定了只允许购买的等级 则强制重置level
        if (priceInfo.whiteLevel) {
          dnaInfo.level = priceInfo.whiteLevel;
        }
      }
    }
    // 价格 总量 配置
    const priceData = this.getpriceConfig(dnaInfo.level, priceInfo);
    // USD价格
    const price = Number((priceData.price * dnaInfo.amount).toFixed(2));
    // 链上价格
    let value: string;
    if (dnaInfo.network == 'USD') {
      value = price.toString();
    } else {
      value = await this.getNetWorkPrice(dnaInfo.network, price);
    }

    // 查看是否有未支付订单
    const orderOld = await this.prisma.dobOrder.findFirst({
      where: {
        clusterId: cluster.id,
        payUserId: dnaInfo.uid,
        level: dnaInfo.level,
        status: PaymentStatus.PENDING,
        earningsType: EarningsType.INIT,
      },
      orderBy: { updatedAt: 'desc' },
    });
    if (orderOld && orderOld.dna && orderOld.status == 'PENDING') {
      await this.prisma.dobOrder.update({
        where: { id: orderOld.id },
        data: { status: 'CANCELED' },
      });
      await this.callBackDobOrder(orderOld.id);
    }

    // 验证是否还有数量(blue 不参与数量验证)
    if (dnaInfo.level != DnaLevel.BLUE && dnaInfo.amount > priceData.remain) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Spore Level Total Overshoot, Please refresh',
      });
    }

    // 获取dna
    try {
      const dobs_count = await this.prisma.dob.count({
        where: { clusterId: cluster.id },
      });
      const dobs_order_count = await this.prisma.dobOrder.count({
        where: {
          clusterId: cluster.id,
          status: { in: ['PENDING', 'PAID'] },
        },
      });
      let levelNmu: number;
      if (dnaInfo.level == DnaLevel.GOLD) {
        levelNmu = 0;
      } else if (dnaInfo.level == DnaLevel.SILVER) {
        levelNmu = 1;
      } else if (dnaInfo.level == DnaLevel.COPPER) {
        levelNmu = 2;
      } else {
        levelNmu = 3;
      }
      const dna_data = {
        ID: dobs_count + dobs_order_count + 100,
        Level: levelNmu,
      };
      const dna_str = await this.cryptoService.getLocalDna(
        JSON.parse(cluster.pattern),
        {
          ID: dobs_count + dobs_order_count,
          Level: levelNmu,
        },
      );
      if (!dna_str) {
        return new DnaResponseDto('', this.collection_address, '', '');
      }

      // 写入订单表
      const dna: Prisma.DobOrderCreateInput = {
        dna: dna_str,
        level: dnaInfo.level,
        dnaData: JSON.stringify(dna_data),
        status: PaymentStatus.PENDING,
        price: price,
        cryptoPrice: value,
        amount: dnaInfo.amount,
        earningsType: EarningsType.INIT,
        cluster: {
          connect: {
            clusterId: dnaInfo.clusterId,
          },
        },
        payUser: {
          connect: {
            id: dnaInfo.uid,
          },
        },
      };
      // 修改book--当余数等于传入的amount的时候 说明是该阶段最后一次购买
      // const bookUpdate: Prisma.BookUpdateInput = {
      //   phaseRemain:
      //     priceData.remain > dnaInfo.amount
      //       ? priceData.remain - dnaInfo.amount
      //       : priceData.nextCount,
      //   phase:
      //     priceData.remain == dnaInfo.amount ? priceData.nextPhase : undefined,
      // };
      // 修改price的余额
      let priceUpdate: Prisma.PriceUpdateInput;
      if (dnaInfo.level == 'GOLD') {
        priceUpdate = {
          goldRemain: {
            decrement: dnaInfo.amount,
          },
        };
      } else if (dnaInfo.level == 'SILVER') {
        priceUpdate = {
          silverRemain: {
            decrement: dnaInfo.amount,
          },
        };
      } else if (dnaInfo.level == 'COPPER') {
        priceUpdate = {
          copperRemain: {
            decrement: dnaInfo.amount,
          },
        };
      } else {
        priceUpdate = {
          blueSales: {
            increment: dnaInfo.amount,
          },
        };
      }
      console.log('priceUpdate:, priceUpdate');
      const dobOrder = await this.prisma.$transaction(async (tx) => {
        // 创建订单
        const dobOrder = await tx.dobOrder.create({ data: dna });
        // 修改书籍阶段--专题销售的时候 不管阶段
        // if (!isSubject) {
        //   await tx.book.update({
        //     where: { id: cluster.bookId },
        //     data: bookUpdate,
        //   });
        // }
        // 修改余量
        await this.prisma.price.update({
          where: { id: priceInfo.id },
          data: priceUpdate,
        });
        return dobOrder;
      });

      return new DnaResponseDto(
        dna_str,
        this.collection_address,
        dobOrder.id,
        value,
      );
    } catch (error) {
      throw new BadRequestException('create DNA fail' + error);
    }
  }

  async dnaCanceled(uid: string, oid: string) {
    if (!oid) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Dob Order ID Not Find',
      });
    }
    const dobOrder = await this.prisma.dobOrder.findUnique({
      where: {
        id: oid,
        payUserId: uid,
      },
    });
    if (!dobOrder) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Dob Order Not Find',
      });
    }
    await this.callBackDobOrder(dobOrder.id);
    await this.prisma.dobOrder.update({
      where: { id: dobOrder.id },
      data: { status: 'CANCELED' },
    });
  }
  // 最后的步骤 创建dob-钱包支付回调
  async createSporeForWallet(uid: string, sporeInfo: CreateSporeDobDto) {
    // check dna order
    const dobOrder = await this.prisma.dobOrder.findFirst({
      where: {
        id: sporeInfo.orderID,
        payUserId: uid,
        status: PaymentStatus.PENDING,
      },
      include: { cluster: { select: { clusterId: true, bookId: true } } },
      orderBy: { createdAt: 'desc' },
    });
    if (!dobOrder) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Dob Order Not Find',
      });
    }
    //判定hash 是否已经存在
    const oldHashData = await this.prisma.dobOrder.findUnique({
      where: { txhash: sporeInfo.txhash },
    });
    if (oldHashData) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Transfer Hash used',
      });
    }

    // check transfer hash
    const txData = await this.cryptoService.findTransferByHash(
      sporeInfo.network,
      sporeInfo.txhash,
    );
    if (!txData.isSuccess) {
      //记录txhash 和 network dna|异步任务跑BTC交易结果
      await this.prisma.dobOrder.update({
        where: { id: dobOrder.id },
        data: {
          status: PaymentStatus.PAID,
          network: sporeInfo.network,
          txhash: sporeInfo.txhash,
        },
      });
      throw new HttpException(
        {
          status: HttpStatus.OK,
          message:
            'Order submitted - The system will automatically purchase the copyright after the order is confirmed',
        },
        HttpStatus.OK,
      );
    }
    if (txData.isSuccess && txData.value != dobOrder.cryptoPrice.toString()) {
      await this.prisma.dobOrder.update({
        where: {
          id: dobOrder.id,
        },
        data: {
          txhash: sporeInfo.txhash,
          network: sporeInfo.network,
          status: PaymentStatus.FAILED,
        },
      });
      this.callBackDobOrder(dobOrder.id);
      // 返回错误
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Wrong amount paid',
      });
    }
    //记录txhash 和 network dna|异步任务跑BTC交易结果
    await this.prisma.dobOrder.update({
      where: { id: dobOrder.id },
      data: {
        status: PaymentStatus.PAID,
        network: sporeInfo.network,
        txhash: sporeInfo.txhash,
      },
    });
    throw new HttpException(
      {
        status: HttpStatus.OK,
        message:
          'Order submitted - The system will automatically purchase the copyright after the order is confirmed',
      },
      HttpStatus.OK,
    );
    return null;
    // 不在这里执行创建逻辑-全部放到task里面
    // const client =
    //   this.chain_network == 'Mainnet'
    //     ? new ccc.ClientPublicMainnet()
    //     : new ccc.ClientPublicTestnet();
    // // 获取用户ckb地址
    // const payUser = await this.prisma.wallet.findUnique({
    //   where: { userId: uid },
    // });
    // let userLock: any;
    // if (payUser && payUser.address) {
    //   userLock = (await ccc.Address.fromString(payUser.address, client)).script;
    // }
    // const wallet = new ccc.SignerCkbPrivateKey(
    //   client,
    //   this.config_secp256k1_key,
    // );
    // const walletObj = await wallet.getRecommendedAddressObj();
    // let toLock: any, isAdmin: boolean, ownerAddress: string;
    // if (!userLock) {
    //   toLock = walletObj.script;
    //   isAdmin = true;
    //   ownerAddress = walletObj.toString();
    // } else {
    //   toLock = userLock;
    //   isAdmin = false;
    //   ownerAddress = payUser.address;
    // }
    // const { tx, id: sporeId } = await createSpore({
    //   signer: wallet,
    //   to: toLock,
    //   clusterMode: 'lockProxy',

    //   data: {
    //     contentType: 'dob/1',
    //     content: ccc.bytesFrom(JSON.stringify({ dna: dobOrder.dna }), 'utf8'),
    //     clusterId: dobOrder.cluster.clusterId,
    //   },
    // });
    // await tx.completeFeeBy(wallet);
    // const signedTx = await wallet.signTransaction(tx);
    // const txHash = signedTx.hash();
    // console.log(signedTx.stringify(), txHash, sporeId, 'hashData');
    // let dobRes;
    // for (let i = 1; i <= dobOrder.amount; i++) {
    //   // 创建spore
    //   const spore: Prisma.DobCreateInput = {
    //     dobId: sporeId,
    //     ownerAddress: ownerAddress,
    //     cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60, //1 hour
    //     utxoData: 'null',
    //     dna: dobOrder.dna,
    //     level: dobOrder.level,
    //     isAdmin: isAdmin,
    //     metadata: dobOrder.dnaData,
    //     txHash: txHash,
    //     withdrawn: 0,
    //     cluster: {
    //       connect: {
    //         id: dobOrder.clusterId,
    //       },
    //     },
    //     user: {
    //       connect: {
    //         id: dobOrder.payUserId,
    //       },
    //     },
    //     dobOrder: {
    //       connect: {
    //         id: dobOrder.id,
    //       },
    //     },
    //   };

    //   dobRes = await this.prisma.$transaction(async (tx) => {
    //     const dob = await tx.dob.create({ data: spore });
    //     // 更新spore 订单
    //     await tx.dobOrder.update({
    //       where: { id: dobOrder.id },
    //       data: {
    //         status: PaymentStatus.DONE,
    //         earningsType: EarningsType.PROCESSING,
    //         dob: { connect: { id: dob.id } },
    //       },
    //     });
    //     return dob;
    //   });
    // }
    // // 更新book 销量
    // await this.prisma.book.update({
    //   where: { id: dobOrder.cluster.bookId },
    //   data: {
    //     sales: {
    //       increment: dobOrder.amount,
    //     },
    //   },
    // });
    // console.log('database success');
    // //最后发起交易
    // await wallet.sendTransaction(tx);
    // console.log('sendTransaction(tx) success');
    // return dobRes;
  }

  //Transfer Change uid
  async transferSpore(uid: string, info: TransferSporeDobDto) {
    if (!isAddressValid(info.toCKBAddress, this.config_network)) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Illegal Address',
      });
    }
    // db Transfer
    const checkSpore = await this.findUserSpore(uid, info.sporeId);
    if (!checkSpore) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Spore Not Find',
      });
    }
    const sporeCell = await getSporeById(info.sporeId, this.config_network);
    const wallet = this.secp256k1Wallet.createSecp256k1Wallet(
      this.config_secp256k1_key,
      this.config_network,
    );
    const lock = addressToScript(info.toCKBAddress);
    const { txSkeleton, outputIndex } = await transferSpore({
      outPoint: sporeCell.outPoint!,
      fromInfos: [wallet.address],
      toLock: lock,
      config: this.config_network,
    });

    const hash = await wallet.signAndSendTransaction(txSkeleton);
    console.log('TransferSpore transaction sent, hash:', hash);
    console.log('Spore output index:', outputIndex);
    // 找ckbaddress用户
    const newUser = await this.prisma.wallet.findUnique({
      where: {
        address: info.toCKBAddress,
      },
      select: { userId: true },
    });
    return await this.prisma.$transaction(async (tx) => {
      let uid = newUser.userId;
      // 没有用户的时候 自动新增一个用户
      if (!uid) {
        const min = Math.pow(10, 4) - 1;
        const max = Math.pow(10, 5) - 1;
        const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
        const hashedPassword = await bcrypt.hash(
          info.toCKBAddress,
          roundsOfHashing,
        );
        const user = await tx.user.upsert({
          where: {
            email: info.toCKBAddress,
          },
          update: {
            password: hashedPassword,
          },
          create: {
            name: 'silentberry' + randomNumber,
            email: info.toCKBAddress,
            password: hashedPassword,
            isWallet: true,
          },
        });
        await tx.wallet.update({
          where: { address: info.toCKBAddress },
          data: { userId: user.id, isSign: true },
        });
        uid = user.id;
      }
      return await tx.dob.update({
        where: {
          id: checkSpore.id,
        },
        data: {
          userId: uid,
        },
      });
    });
  }

  async listMyBuyById(
    uid: string,
    params: QueryMyDobDto,
  ): Promise<PagedResponseDto<DobOrderEntity>> {
    const { skip, take, allowCount, sort: order, level } = params;
    const filter: Prisma.DobOrderWhereInput = {
      payUserId: uid,
      level: level ?? undefined,
      status: {
        in: [
          PaymentStatus.DONE,
          PaymentStatus.PAID,
          PaymentStatus.PENDING,
          PaymentStatus.FAILED,
        ],
      },
    };
    const dobsFindInputs: Prisma.DobOrderFindManyArgs = {
      where: filter,
      orderBy: [
        this.stringService.toPrismaOrderByObject(order),
        { createdAt: 'desc' },
      ],
      skip,
      take,
      include: {
        cluster: { include: { book: true } },
        dob: { select: { txHash: true } },
      },
    };

    try {
      if (allowCount) {
        const [dobs, count] = await this.prisma.$transaction([
          this.prisma.dobOrder.findMany(dobsFindInputs),
          this.prisma.dobOrder.count({ where: filter }),
        ]);
        const dataFormat = dobs.map((one) => {
          return new DobOrderEntity(one);
        });
        return new PagedResponseDto<DobOrderEntity>(
          dataFormat,
          skip,
          take,
          count,
        );
      } else {
        const dobs = await this.prisma.dobOrder.findMany(dobsFindInputs);

        const dataFormat = dobs.map((one) => {
          return new DobOrderEntity(one);
        });

        return new PagedResponseDto<DobOrderEntity>(dataFormat, skip, take, 0);
      }
    } catch (error) {
      this.logger.debug('Error My Spore Page:', error);
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid query params.',
        });
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  async checkoutOrderPayment(
    userId: string,
    checkoutPaymentDto: CheckoutPaymentDto,
  ) {
    const order = await this.prisma.dobOrder.findUnique({
      where: { id: checkoutPaymentDto.orderId, payUserId: userId },
    });
    if (!order) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Order not find.',
      });
    }
    // 订单价格=单价*数量 保留2位小数
    const amount = new Decimal(order.price)
      .mul(order.amount)
      .toDecimalPlaces(2);
    const paymentDto: CreatePaymentDto = {
      userId: userId,
      provider: checkoutPaymentDto.provider,
      currency: checkoutPaymentDto.currency,
      dobOrderId: order.id,
      amount: amount.toString(),
    };
    return await this.paymentService.createPayment(paymentDto);
  }

  async listSporeEarningsById(
    dobId: string,
    params: QueryParamDto,
  ): Promise<PagedResponseDto<DobEarningsEntity>> {
    //先查dob
    const dob = await this.prisma.dob.findUnique({
      select: { level: true },
      where: { id: dobId },
    });
    if (!dob) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Dob Not Find.',
      });
    }
    //根据level 查询不同的阶段收益
    const { skip, take, allowCount } = params;
    let filter: Prisma.DobEarningsLogWhereInput;

    const setting = JSON.parse(this.earnings_config);
    let ratioConfig;
    if (dob.level === DnaLevel.GOLD) {
      filter = {
        dobId: dobId,
        level: {
          in: [DnaLevel.COPPER, DnaLevel.SILVER, DnaLevel.BLUE],
        },
      };

      ratioConfig = setting['GOLD'];
    } else if (dob.level === DnaLevel.SILVER) {
      filter = {
        dobId: dobId,
        level: {
          in: [DnaLevel.COPPER, DnaLevel.BLUE],
        },
      };

      ratioConfig = setting['SILVER'];
    } else if (dob.level === DnaLevel.COPPER) {
      filter = {
        dobId: dobId,
        level: {
          in: [DnaLevel.BLUE],
        },
      };
      ratioConfig = setting['COPPER'];
    } else if (dob.level === DnaLevel.BLUE) {
      return new PagedResponseDto<DobEarningsEntity>([], skip, take, 0);
    }

    const earningsFindInputs: Prisma.DobEarningsLogFindManyArgs = {
      where: filter,
      orderBy: [{ updatedAt: 'desc' }],
      skip,
      take,
      select: {
        level: true,
        goldHoldEarnings: true,
        silverHoldEarnings: true,
        copperHoldEarnings: true,
        blueHoldEarnings: true,
        book: true,
        dob: {
          select: {
            txHash: true,
            dobOrder: {
              select: { price: true, amount: true },
            },
          },
        },
      },
    };

    try {
      if (allowCount) {
        const [earningss, count] = await this.prisma.$transaction([
          this.prisma.dobEarningsLog.findMany(earningsFindInputs),
          this.prisma.dobEarningsLog.count({ where: filter }),
        ]);
        // 重组数据
        const dataFormat = earningss.map((one) => {
          let ratio;
          if (one.level == DnaLevel.GOLD) {
            ratio = ratioConfig['gold'] ?? 0;
          } else if (one.level == DnaLevel.SILVER) {
            ratio = ratioConfig['silver'] ?? 0;
          } else if (one.level == DnaLevel.COPPER) {
            ratio = ratioConfig['copper'] ?? 0;
          }
          return new DobEarningsEntity({ ...one, ratio: ratio });
        });

        return new PagedResponseDto<DobEarningsEntity>(
          dataFormat,
          skip,
          take,
          count,
        );
      } else {
        const earningss =
          await this.prisma.dobEarningsLog.findMany(earningsFindInputs);

        const dataFormat = earningss.map((one) => {
          return new DobEarningsEntity({ ...one, ratio: ratioConfig });
        });

        return new PagedResponseDto<DobEarningsEntity>(
          dataFormat,
          skip,
          take,
          0,
        );
      }
    } catch (error) {
      this.logger.debug('Error Earnings Page', error);
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid query params.',
        });
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  // spore购买失败 回退
  async callBackDobOrder(dobOrderId: string) {
    //check dna order
    const dobOrder = await this.prisma.dobOrder.findUnique({
      where: {
        id: dobOrderId,
      },
      include: {
        cluster: true,
      },
    });
    const bookId = dobOrder.cluster.bookId;
    if (!dobOrder || !bookId) return false;
    // const bookData = await this.prisma.book.findUnique({
    //   where: { id: bookId },
    //   select: { id: true, phase: true, phaseRemain: true },
    // });
    // 更新price 剩余
    let priceUp: Prisma.PriceUpdateInput;
    if (dobOrder.level == DnaLevel.GOLD) {
      priceUp = {
        goldRemain: {
          increment: dobOrder.amount,
        },
      };
    } else if (dobOrder.level == DnaLevel.SILVER) {
      priceUp = {
        silverRemain: {
          increment: dobOrder.amount,
        },
      };
    } else if (dobOrder.level == DnaLevel.COPPER) {
      priceUp = {
        copperRemain: {
          increment: dobOrder.amount,
        },
      };
    } else if (dobOrder.level == DnaLevel.BLUE) {
      priceUp = {
        blueSales: {
          decrement: dobOrder.amount,
        },
      };
    }
    if (priceUp) {
      await this.prisma.price.update({
        where: { bookId: bookId },
        data: priceUp,
      });
    }
    // let bookUp: Prisma.BookUpdateInput;
    // //如果是当前阶段 则增加剩余 如果不是则回退
    // if (bookData.phase == dobOrder.level) {
    //   bookUp = {
    //     phaseRemain: {
    //       increment: dobOrder.amount,
    //     },
    //   };
    // } else {
    //   let level = dobOrder.level;
    //   let remain = dobOrder.amount;
    //   // 兼容越级回退
    //   if (
    //     (dobOrder.level == 'SILVER' && bookData.phase == 'GOLD') ||
    //     (dobOrder.level == 'COPPER' &&
    //       (bookData.phase == 'GOLD' || bookData.phase == 'SILVER'))
    //   ) {
    //     level = bookData.phase;
    //     remain = bookData.phaseRemain;
    //   }
    //   bookUp = {
    //     phase: level,
    //     phaseRemain: remain,
    //   };
    // }
    // await this.prisma.book.update({
    //   where: { id: bookId },
    //   data: bookUp,
    // });
  }

  async publish(id: string, data: CallbackPublishDobDto) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: { role: true },
    });
    if (!user) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'UserNotFind',
      });
    }
    if (user.role != Role.ADMIN) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'UserNotAuthor',
      });
    }
    const order_one = await this.prisma.dobOrder.findUnique({
      where: {
        id: data.dobOrderId,
      },
    });
    if (!order_one) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Order not find.',
      });
    }
    const platformAddr =
      'ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d';
    const spore: Prisma.DobCreateInput = {
      dobId: data.sporeId,
      ownerAddress: data.owner,
      isAdmin: data.owner == platformAddr ? true : false,
      cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60, //1 hour
      utxoData: 'null',
      dna: order_one.dna,
      level: order_one.level,
      metadata: order_one.dnaData,
      txHash: data.txHash,
      withdrawn: 0,
      cluster: {
        connect: {
          id: order_one.clusterId,
        },
      },
      user: {
        connect: {
          id: order_one.payUserId,
        },
      },
      dobOrder: {
        connect: {
          id: order_one.id,
        },
      },
    };
    //数据库更新事物
    return await this.prisma.$transaction(async (tx) => {
      const dob = await tx.dob.upsert({
        where: { dobId: spore.dobId },
        update: spore,
        create: spore,
      });
      // 更新spore 订单
      await tx.dobOrder.update({
        where: { id: order_one.id },
        data: {
          status: PaymentStatus.DONE,
          earningsType: EarningsType.PROCESSING,
          dob: { connect: { id: dob.id } },
        },
      });
      return dob;
    });
  }

  async findByTxHash(id: string, data: CallbackFindDobDto) {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: { role: true },
    });
    if (!user) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'UserNotFind',
      });
    }
    if (user.role != Role.ADMIN) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'UserNotAuthor',
      });
    }
    console.log(data, 'aaa');
    // 根据hash 找order
    const orders = await this.prisma.dobOrder.findMany({
      where: {
        OR: [
          { txhash: data.txHashs ? { in: data.txHashs } : undefined },
          {
            payment: data.paymentIDs
              ? { transactionId: { in: data.paymentIDs } }
              : undefined,
          },
        ],
      },
      include: { dob: true, payment: true },
      orderBy: { updatedAt: 'asc' },
    });
    if (!orders) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Order not find.',
      });
    }

    const result = await Promise.all(
      orders.map(async (order) => {
        const wallet = await this.prisma.wallet.findUnique({
          where: { userId: order.payUserId },
        });
        return {
          order: new DobOrderEntity(order),
          address: wallet?.address,
        };
      }),
    );
    console.log(result, 'aaa');
    return result;
  }

  private getpriceConfig(level: DnaLevel, config: PriceEntity) {
    const priceData = {
      price: 0,
      remain: 0,
      nextPhase: {},
      nextCount: 0,
    };

    if (level == 'GOLD') {
      priceData.price = config.goldPrice.toNumber();
      priceData.remain = config.goldRemain;
      if (config.silverRemain > 0) {
        priceData.nextPhase = DnaLevel.SILVER;
        priceData.nextCount = config.silverRemain;
      } else if (config.copperRemain > 0) {
        priceData.nextPhase = DnaLevel.COPPER;
        priceData.nextCount = config.copperRemain;
      } else {
        priceData.nextPhase = DnaLevel.BLUE;
      }
    } else if (level == 'SILVER') {
      priceData.price = config.silverPrice.toNumber();
      priceData.remain = config.silverRemain;
      if (config.copperRemain > 0) {
        priceData.nextPhase = DnaLevel.COPPER;
        priceData.nextCount = config.copperRemain;
      } else {
        priceData.nextPhase = DnaLevel.BLUE;
      }
    } else if (level == 'COPPER') {
      priceData.price = config.copperPrice.toNumber();
      priceData.remain = config.copperRemain;
      priceData.nextPhase = DnaLevel.BLUE;
    } else if (level == 'BLUE') {
      priceData.price = config.bluePrice.toNumber();
      priceData.nextPhase = DnaLevel.BLUE;
    }
    return priceData;
  }

  private async getNetWorkPrice(network: PayNetWork, price: number) {
    // 获取价格汇率
    const golbalConfig = await this.prisma.golbalConfig.findFirst();
    let value;
    switch (network) {
      case PayNetWork.ETH:
        const num = price / golbalConfig.ethPrice.toNumber();
        value = parseUnits(num.toFixed(18).toString(), 'ether').toString();
        break;
      case PayNetWork.BTC:
        value = Math.round(
          (price / golbalConfig.btcPrice.toNumber()) * 1e8,
        ).toString();
        break;
      case PayNetWork.CKB:
        value = Math.ceil(price / golbalConfig.ckbPrice.toNumber());
        break;
      // case PayNetWork.USD:
      default:
        value = price.toFixed(2);
        break;
    }
    return value;
  }
}
