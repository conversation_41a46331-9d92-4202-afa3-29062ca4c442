import { Modu<PERSON> } from '@nestjs/common';
import { DobsService } from './dobs.service';
import { DobsController } from './dobs.controller';
import { Secp256k1WalletService } from 'src/common/utils/secp256k1.service';
import { PrismaModule } from 'nestjs-prisma';
import { DobLogsService } from './dobLogs.service';
import { StringService } from 'src/common/utils/string.service';
import { CryptoService } from '../crypto/crypto.service';
import { DNAService } from 'src/common/utils/dna_encode.service';
import { PaymentService } from '../payment/payment.service';
import { StripeService } from '../stripe/stripe.service';

@Module({
  imports: [PrismaModule],
  controllers: [DobsController],
  providers: [
    Secp256k1WalletService,
    DobsService,
    DobLogsService,
    StringService,
    CryptoService,
    StripeService,
    DNAService,
    PaymentService,
  ],
  exports: [DobsService],
})
export class DobsModule {}
