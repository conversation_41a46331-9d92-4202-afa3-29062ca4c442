import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExcludeEndpoint,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { boolean } from 'joi';
import { ApiPaging } from 'src/common/decorators/api-paging.decorator';
import { PagingQuery } from 'src/common/decorators/paging-query.decorator';
import { QueryParamDto } from 'src/common/dtos/query-param.dto';
import { Public } from 'src/iam/auth/decorators/public.decorators';
import { DobEarningsEntity } from '../earnings/entities/dob-earnings.entity';
import { CheckoutPaymentDto } from '../print-orders/dto/checkout-payment.dto';
import { DobLogsQuery } from './decorator/dob-logs-query.decorator';
import { DobLogsService } from './dobLogs.service';
import { DobsService } from './dobs.service';
import {
  CallbackFindDobDto,
  CallbackPublishDobDto,
} from './dto/callback-spore-dob.dto';
import { CreateDnaDto, CreateSporeDobDto } from './dto/create-spore-dob.dto';
import { DnaResponseDto } from './dto/dna-response.dto';
import { QueryMyDobDto } from './dto/query-my-dob.dto';
import { QuerySporeDobLogsDto } from './dto/query-spore-dob-logs.dto';
import { TransferSporeDobDto } from './dto/transfer-spore-dob.dto';
import { DobBriefEntity } from './entities/dob-dec.entity';
import { DobEntity } from './entities/dob.entity';
import { DobLogsEntity } from './entities/dobLogs.entity';
import { DobOrderEntity } from './entities/dobOrder.entity';

@Controller('spore')
@ApiTags('spore')
export class DobsController {
  constructor(
    private readonly dobsService: DobsService,
    private readonly dobLogsService: DobLogsService,
  ) {}

  @Get('list/:clusterid')
  @Public()
  @ApiOperation({
    summary: '列车所有spore',
    description:
      '根据cluster获取链上所有spore<br/>公共接口:允许在不登录情况下获取',
  })
  async findAll(@Param('cid') cid: string) {
    return await this.dobsService.findAll(cid);
  }

  @Get('my/:dobid')
  @ApiBearerAuth()
  @ApiOkResponse({ type: DobEntity })
  @ApiOperation({
    summary: '我的dob-根据id获取指定dob',
  })
  async findOne(@Req() req, @Param('dobId') cid: string) {
    return new DobEntity(await this.dobsService.findOne(req.user.sub, cid));
  }

  @Post('dna')
  @ApiOperation({
    summary: '获取sporeDNA参数',
    description: '点击购买的时候调用',
  })
  @ApiBearerAuth()
  @ApiOkResponse({ type: DnaResponseDto })
  async getDnaForCluster(@Req() req, @Body() dnaInfo: CreateDnaDto) {
    if (!req.user.sub || req.user.sub !== dnaInfo.uid) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Invalid request user.',
      });
    }
    return await this.dobsService.getDnaForCluster(dnaInfo);
  }

  @Get('dna/canceled/:dobOrderId')
  @ApiOperation({
    summary: 'dna 回退接口',
  })
  @ApiBearerAuth()
  @ApiOkResponse({ type: boolean })
  async dnaCanceled(@Req() req, @Param('dobOrderId') oid: string) {
    return await this.dobsService.dnaCanceled(req.user.sub, oid);
  }

  @Post('subject/dna')
  @ApiOperation({
    summary: '专题页面购买-获取sporeDNA参数',
    description: '专题页面购买-点击购买的时候调用',
  })
  @ApiBearerAuth()
  @ApiOkResponse({ type: DnaResponseDto })
  async getSubjectDnaForCluster(@Req() req, @Body() dnaInfo: CreateDnaDto) {
    if (!req.user.sub || req.user.sub !== dnaInfo.uid) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Invalid request user.',
      });
    }
    return await this.dobsService.getDnaForCluster(dnaInfo);
  }

  // @Post('save_spore_wallet')
  // @ApiOperation({
  //   summary: 'ckb钱包mintspore回调',
  //   description: 'ckb钱包直接支付ckb调用spore-mint',
  // })
  // @ApiOkResponse({ type: DobEntity })
  // @Expose()
  // async saveSpore(@Req() req, @Body() sporeInfo: SaveSpore) {
  //   // 取当前登录的用户ID & 验证
  //   const uid = req.user.sub;
  //   if (!uid || !sporeInfo.uid || uid !== sporeInfo.uid) {
  //     throw new BadRequestException('Invalid request user.');
  //   }

  //   return new DobEntity(await this.dobsService.saveSpore(sporeInfo));
  // }

  @Post('create_spore_256k1')
  @ApiOperation({
    summary: '创建spore',
    description: '在eth|btc进行支付成功后，我们需要调用此接口进行spore链上购买',
  })
  @ApiOkResponse({ type: DobEntity })
  @ApiBearerAuth()
  async createSporeFor256K1(@Req() req, @Body() sporeInfo: CreateSporeDobDto) {
    // 取当前登录的用户ID & 验证
    return new DobEntity(
      await this.dobsService.createSporeForWallet(req.user.sub, sporeInfo),
    );
  }

  @Post('transfer_spore')
  @ApiOperation({
    summary: '转移Spore',
    description: '转移版权',
  })
  @ApiOkResponse({ type: DobEntity })
  async transferSpore(@Req() req, @Body() info: TransferSporeDobDto) {
    return new DobEntity(
      await this.dobsService.transferSpore(req.user.sub, info),
    );
  }

  @Get('my')
  @ApiPaging(DobEntity, QueryMyDobDto)
  @ApiOperation({
    summary: '列出我的所有spore',
    description: '获取我的所有资产记录',
  })
  @ApiBearerAuth()
  findDobsForCluster(
    @Req() req,
    @PagingQuery(QueryMyDobDto) params: QueryMyDobDto,
  ) {
    return this.dobsService.findPagination(req.user.sub, params);
  }

  @Get('earnings/:sporeId')
  @ApiPaging(DobEarningsEntity, QueryParamDto)
  @ApiOperation({
    summary: '根据SporeID获取收益记录',
    description: '资产详情的收益记录',
  })
  @ApiOkResponse({ type: DobEarningsEntity })
  @ApiBearerAuth()
  listSporeEarningsById(
    @Param('sporeId') sporeId: string,
    @PagingQuery(QueryParamDto) params: QueryParamDto,
  ) {
    return this.dobsService.listSporeEarningsById(sporeId, params);
  }

  @Get('log')
  @ApiPaging(DobLogsEntity, QuerySporeDobLogsDto)
  @ApiOperation({
    summary: '查询Spore的交易记录',
    description: 'spore的事务记录分页',
  })
  findDobLogsForCluster(
    @DobLogsQuery(QuerySporeDobLogsDto) params: QuerySporeDobLogsDto,
  ) {
    return this.dobLogsService.findLogsPagination(params);
  }

  @Get('brief/:sporeId')
  @ApiOperation({
    summary: '根据SporeID获取SporeInfo简介',
    description: '获取简要的一些信息',
  })
  @ApiOkResponse({ type: DobBriefEntity })
  @ApiBearerAuth()
  async findOnebrief(@Param('sporeId') sporeId: string) {
    return await this.dobsService.findOnebrief(sporeId);
  }

  @Get('my/buy_log')
  @ApiPaging(DobOrderEntity, QueryMyDobDto)
  @ApiOperation({
    summary: '获取我的购买记录',
  })
  @ApiExtraModels(DobOrderEntity)
  @ApiBearerAuth()
  listBookSalesById(
    @Req() req,
    @PagingQuery(QueryMyDobDto) params: QueryMyDobDto,
  ) {
    return this.dobsService.listMyBuyById(req.user.sub, params);
  }

  @Post('checkout')
  @ApiOperation({
    summary: '版权USD购买-结帐订单',
    description:
      '会返回付款意图客户端秘密，客户端应该重定向到条带页面与支付意图客户端秘密: https://checkout.stripe.com/c/pay/${client_secret}',
  })
  @ApiBearerAuth()
  checkout(@Req() req, @Body() checkoutPaymentDto: CheckoutPaymentDto) {
    return this.dobsService.checkoutOrderPayment(
      req.user.sub,
      checkoutPaymentDto,
    );
  }

  @Post('callback/dob/find')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'dob回调-找dobOrder',
  })
  @ApiExcludeEndpoint()
  async findByTxHash(@Req() req, @Body() data: CallbackFindDobDto) {
    return await this.dobsService.findByTxHash(req.user.sub, data);
  }

  @Post('callback/dob/publish')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'dob回调-创建dob',
  })
  @ApiExcludeEndpoint()
  publish(@Req() req, @Body() data: CallbackPublishDobDto) {
    return this.dobsService.publish(req.user.sub, data);
  }
}
