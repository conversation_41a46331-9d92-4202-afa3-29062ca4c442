import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Transform } from 'class-transformer';

export class DobBriefEntity {
  constructor({ ...data }: Partial<DobBriefEntity>) {
    Object.assign(this, data);
  }
  @ApiProperty({
    description: '等级[GOLD:金浆果,SILVER:银浆果,COPPER:铜浆果,BLUE:蓝莓]',
  })
  level: $Enums.DnaLevel;

  @ApiProperty({
    description: '收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  earnings: Decimal;

  @ApiProperty({
    description: '花费',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  price: Decimal;

  createdAt: Date;
}
