import { ApiProperty } from '@nestjs/swagger';
import { $Enums, Dob } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Transform } from 'class-transformer';
import { PriceEntity } from 'src/resources/books/entities/price.entity';
import { ClusterEntity } from 'src/resources/clusters/entities/cluster.entity';
import { UserEntity } from 'src/resources/users/entities/user.entity';

export class DobEntity implements Dob {
  constructor({ ...data }: Partial<DobEntity>) {
    Object.assign(this, data);
  }

  @ApiProperty()
  isAdmin: boolean;

  @ApiProperty()
  dobOrderId: string;

  @ApiProperty({
    description: '交易Hash',
  })
  txHash: string;

  @ApiProperty()
  @Transform(({ value }) => (value ? value.toString() : 0))
  withdrawn: Decimal;

  @ApiProperty({
    description: '等级[GOLD:金浆果,SILVER:银浆果,COPPER:铜浆果,BLUE:蓝莓]',
  })
  level: $Enums.DnaLevel;

  @ApiProperty()
  dna: string;

  @ApiProperty()
  cacheExpTime: number;

  @ApiProperty()
  id: string;

  @ApiProperty()
  dobId: string;

  @ApiProperty()
  ownerAddress: string;

  @ApiProperty()
  utxoData: string;

  @ApiProperty()
  metadata: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  user?: UserEntity;

  @ApiProperty()
  clusterId: string;

  @ApiProperty()
  cluster?: ClusterEntity;

  @ApiProperty()
  pirce?: PriceEntity;

  createdAt: Date;
  updatedAt: Date;
}
