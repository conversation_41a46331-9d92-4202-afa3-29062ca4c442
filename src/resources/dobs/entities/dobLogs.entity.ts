import { ApiProperty } from '@nestjs/swagger';
import { DobLogs } from '@prisma/client';

export class DobLogsEntity implements DobLogs {
  constructor({ ...data }: Partial<DobLogsEntity>) {
    Object.assign(this, data);
  }
  @ApiProperty()
  id: number;

  @ApiProperty()
  // 1 Create 2 Transfer
  operation: number;

  @ApiProperty()
  operationUid: string;

  @ApiProperty()
  dobId: string;

  @ApiProperty()
  clusterId: string;

  @ApiProperty()
  timestamp: string;
}
