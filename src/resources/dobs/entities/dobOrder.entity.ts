import { ApiProperty } from '@nestjs/swagger';
import { $Enums, DobOrder } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Exclude, Transform, Type } from 'class-transformer';
import { PaymentEntity } from 'src/resources/payment/entities/payment.entity';

export class DobOrderEntity implements DobOrder {
  constructor({ ...data }: Partial<DobOrderEntity>) {
    Object.assign(this, data);
  }

  @ApiProperty()
  earningsType: $Enums.EarningsType;

  @ApiProperty()
  id: string;

  @ApiProperty()
  dna: string;

  @ApiProperty({
    description: '等级[GOLD:金浆果,SILVER:银浆果,COPPER:铜浆果,BLUE:蓝莓]',
  })
  level: $Enums.DnaLevel;

  @ApiProperty({
    description: '支付hash',
  })
  txhash: string;

  @ApiProperty({
    description: '数量',
  })
  amount: number;

  @ApiProperty({
    description: '售价',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  price: Decimal;

  @ApiProperty({
    description: '链上价格',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  cryptoPrice: Decimal;

  @ApiProperty({
    description: '[BTC ETH]',
  })
  network: $Enums.PayNetWork;

  @Exclude()
  dnaData: string;

  @ApiProperty({
    description: '[PENDING:进行中,DONE:已完成,FAILED:失败,CANCELED:取消]',
  })
  status: $Enums.PaymentStatus;

  @ApiProperty()
  @Type(() => PaymentEntity)
  payment?: PaymentEntity;

  @ApiProperty()
  payUserId: string;

  @ApiProperty()
  clusterId: string;

  @ApiProperty()
  paymentId: string;

  createdAt: Date;
  updatedAt: Date;
}
