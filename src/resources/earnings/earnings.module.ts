import { Module } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { EarningsController } from './earnings.controller';
import { EarningsService } from './earnings.service';
import { StripeModule } from '../stripe/stripe.module';

@Module({
  imports: [PrismaModule, StripeModule],
  controllers: [EarningsController],
  providers: [EarningsService, StringService],
  exports: [EarningsService],
})
export class EarningsModule {}
