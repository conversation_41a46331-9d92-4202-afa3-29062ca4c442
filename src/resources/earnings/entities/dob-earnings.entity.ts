import { ApiProperty } from '@nestjs/swagger';
import { $Enums, DobEarningsLog } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Exclude, Transform } from 'class-transformer';
import { BookEntity } from 'src/resources/books/entities/book.entity';
import { DobEntity } from 'src/resources/dobs/entities/dob.entity';

export class DobEarningsEntity implements DobEarningsLog {
  constructor({ ...data }: Partial<DobEarningsEntity>) {
    Object.assign(this, data);
  }
  @ApiProperty()
  id: string;

  @ApiProperty({
    description: '等级[GOLD:金浆果,SILVER:银浆果,COPPER:铜浆果,BLUE:蓝莓]',
  })
  level: $Enums.DnaLevel;

  @ApiProperty({
    description: '价格',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  price: Decimal;

  @ApiProperty({
    description: '金持有收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  goldHoldEarnings: Decimal;

  @ApiProperty({
    description: '银持有收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  silverHoldEarnings: Decimal;

  @ApiProperty({
    description: '铜持有收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  copperHoldEarnings: Decimal;

  @ApiProperty({
    description: '蓝莓持有收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  blueHoldEarnings: Decimal;

  @ApiProperty({
    description: '作者收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  authorEarnings: Decimal;

  @Exclude()
  platformEarnings: Decimal;

  @ApiProperty()
  bookId: string;

  @ApiProperty()
  book?: BookEntity;

  @ApiProperty()
  dobId: string;

  @ApiProperty()
  dob?: DobEntity;

  @ApiProperty({
    description: '收益率',
  })
  ratio?: number;

  createdAt: Date;
  updatedAt: Date;
}
