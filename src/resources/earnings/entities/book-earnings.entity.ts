import { ApiProperty } from '@nestjs/swagger';
import { BookEarningsLog } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Transform } from 'class-transformer';

export class EarningsEntity implements BookEarningsLog {
  constructor({ ...data }: Partial<EarningsEntity>) {
    Object.assign(this, data);
  }

  @ApiProperty()
  id: string;

  @ApiProperty({
    description: '金收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  goldEarnings: Decimal;

  @ApiProperty({
    description: '银收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  silverEarnings: Decimal;

  @ApiProperty({
    description: '铜收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  copperEarnings: Decimal;

  @ApiProperty({
    description: '作者收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  authorEarnings: Decimal;

  @ApiProperty({
    description: '平台收益',
  })
  @Transform(({ value }) => (value ? value.toString() : 0))
  platformEarnings: Decimal;

  @ApiProperty()
  bookId: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
