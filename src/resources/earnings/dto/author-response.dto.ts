import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class AuthorEarningsResponseDto {
  @ApiProperty({
    description: '累计收益',
  })
  @Expose()
  totalEarnings: string;

  @ApiProperty({
    description: '可提现金额',
  })
  @Expose()
  unlockEarnings: string;

  @ApiProperty({
    description: '销售总额',
  })
  @Expose()
  sellTotal: number;

  @ApiProperty({
    description: '销售总量',
  })
  @Expose()
  sellAmount: number;

  constructor(
    totalEarnings: number,
    unlockEarnings: number,
    sellTotal: number,
    sellAmount: number,
  ) {
    this.totalEarnings =
      totalEarnings && totalEarnings >= 0 ? totalEarnings.toFixed(2) : '0';
    this.unlockEarnings =
      unlockEarnings && unlockEarnings >= 0 ? unlockEarnings.toFixed(2) : '0';
    this.sellTotal = sellTotal && sellTotal >= 0 ? sellTotal : 0;
    this.sellAmount = sellAmount && sellAmount > 0 ? sellAmount : 0;
  }
}
