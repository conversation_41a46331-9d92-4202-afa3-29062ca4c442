import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class HoldEarningsResponseDto {
  @ApiProperty()
  @Expose()
  totalEarnings: string;

  @ApiProperty()
  @Expose()
  unlockEarnings: string;

  constructor(totalEarnings: number, unlockEarnings: number) {
    this.totalEarnings =
      totalEarnings && totalEarnings >= 0 ? totalEarnings.toFixed(4) : '0';
    this.unlockEarnings =
      unlockEarnings && unlockEarnings >= 0 ? unlockEarnings.toFixed(4) : '0';
  }
}
