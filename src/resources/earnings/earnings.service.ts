import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { PagedResponseDto } from 'src/common/dtos/paged-response.dto';
import { DnaLevel, EarningsType, PaymentStatus, Prisma } from '@prisma/client';
import { QueryTimeParamDto } from 'src/common/dtos/query-time-param.dto';
import { AuthorEarningsResponseDto } from './dto/author-response.dto';
import { HoldEarningsResponseDto } from './dto/hold-response.dto';
import { DobEarningsEntity } from './entities/dob-earnings.entity';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EarningsService {
  private readonly logger = new Logger(EarningsService.name);
  private earnings_config;
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {
    this.earnings_config = configService.get<string>('EARNINGS_CONFIG');
  }

  async findAuthorTotalEarnings(uid: string) {
    // 作者总收益【遍历books 获取作者所有收益】
    const authorTotal = await this.prisma.bookEarningsLog.aggregate({
      where: {
        book: {
          userId: uid,
        },
      },
      _sum: {
        authorEarnings: true,
      },
    });
    const authorTotalNum = authorTotal._sum.authorEarnings
      ? authorTotal._sum.authorEarnings.toNumber()
      : 0;
    // 获取已经提现金额
    const withdrawn = await this.prisma.cluster.aggregate({
      where: { book: { userId: uid } },
      _sum: {
        withdrawn: true,
      },
    });
    const withdrawnNum = withdrawn._sum.withdrawn
      ? withdrawn._sum.withdrawn.toNumber()
      : 0;

    // 获取销售量 销售总额
    const dob = await this.prisma.dobOrder.aggregate({
      where: {
        cluster: { creator: uid },
        status: PaymentStatus.DONE,
        earningsType: EarningsType.FINISH,
      },
      _sum: {
        amount: true,
        price: true,
      },
    });
    const dobAmountNum = dob._sum.amount;
    const dobPriceNum = dob._sum.price ? dob._sum.price.toNumber() : 0;

    return new AuthorEarningsResponseDto(
      authorTotalNum,
      authorTotalNum - withdrawnNum,
      dobAmountNum,
      dobPriceNum,
    );
  }

  async findAuthorPagination(
    sub: string,
    params: QueryTimeParamDto,
  ): Promise<PagedResponseDto<DobEarningsEntity>> {
    return await this.findDobEarningsList(sub, params, 'author');
  }

  async findHoldTotalEarnings(uid: string) {
    // 获取持有所有 dob 已提现总额
    const dob = await this.prisma.dob.findMany({
      select: {
        withdrawn: true,
        level: true,
        cluster: {
          select: {
            bookId: true,
          },
        },
      },
      where: { userId: uid },
    });
    // 获取所有可提现总额
    const bookId = dob
      .map((dobone) => dobone.cluster.bookId)
      .filter((id, index, self) => self.indexOf(id) === index);
    // 根据全部卖出的情况计算收益--最新调整
    // TODO:不兼容所有书籍|且存在超发收益的情况|且现在方式无法计算蓝莓收益
    const priceData = await this.prisma.price.findMany({
      where: { bookId: { in: bookId } },
    });
    const setting = JSON.parse(this.earnings_config);
    const priceConfig = priceData.reduce((map, obj) => {
      map[obj.bookId] = obj;
      return map;
    }, {});
    let TotalNum = 0; //累计
    let lockNum = 0; //已提现
    for (const one of dob) {
      //已提现
      lockNum = one.withdrawn ? lockNum + one.withdrawn.toNumber() : lockNum;

      //累计可提现
      if (!one.cluster) continue;
      const bookid = one.cluster.bookId;
      const price = priceConfig[bookid] ?? [];
      if (price) {
        if (
          one.level == DnaLevel.GOLD &&
          setting['SILVER'] &&
          setting['COPPER']
        ) {
          // 单卡金的计算方式 :所有银收益+所有铜收益
          const silverEarnings =
            (price['silverCount'] *
              price['silverPrice'] *
              setting['SILVER']['gold']) /
            100;
          const copperEarnings =
            (price['copperCount'] *
              price['copperPrice'] *
              setting['COPPER']['gold']) /
            100;
          TotalNum += Number(
            Number((copperEarnings + silverEarnings) / price['goldCount']),
          );
        } else if (one.level == DnaLevel.SILVER && setting['COPPER']) {
          // 单卡银的计算方式 :所有铜收益
          const copperEarnings = Number(
            (price['copperCount'] *
              price['copperPrice'] *
              setting['COPPER']['silver']) /
              100 /
              price['silverCount'],
          );
          TotalNum += Number(copperEarnings);
        }
      }
    }

    return new HoldEarningsResponseDto(TotalNum, TotalNum - lockNum);
  }

  async findHoldPagination(
    sub: string,
    params: QueryTimeParamDto,
  ): Promise<PagedResponseDto<DobEarningsEntity>> {
    return await this.findDobEarningsList(sub, params, 'hold');
  }

  private async findDobEarningsList(
    sub: string,
    params: QueryTimeParamDto,
    type: string,
  ): Promise<PagedResponseDto<DobEarningsEntity>> {
    const { skip, take, allowCount, startTime } = params;
    let filter: Prisma.DobEarningsLogWhereInput;
    let earningsFindInputs: Prisma.DobEarningsLogFindManyArgs;

    if (type == 'author') {
      filter = {
        book: {
          userId: sub,
        },
        updatedAt: startTime
          ? {
              gte: startTime,
            }
          : undefined,
      };

      earningsFindInputs = {
        where: filter,
        orderBy: [{ updatedAt: 'desc' }],
        skip,
        take,
        include: {
          book: true,
        },
      };
    } else if (type == 'hold') {
      filter = {
        dob: {
          userId: sub,
        },
        updatedAt: startTime
          ? {
              gte: startTime,
            }
          : undefined,
      };

      earningsFindInputs = {
        where: filter,
        orderBy: [{ updatedAt: 'desc' }],
        skip,
        take,
        select: {
          level: true,
          price: true,
          goldHoldEarnings: true,
          silverHoldEarnings: true,
          copperHoldEarnings: true,
          blueHoldEarnings: true,
          book: true,
        },
      };
    } else {
      return null;
    }

    try {
      if (allowCount) {
        const [earningss, count] = await this.prisma.$transaction([
          this.prisma.dobEarningsLog.findMany(earningsFindInputs),
          this.prisma.dobEarningsLog.count({ where: filter }),
        ]);
        // 重组数据
        const dataFormat = earningss.map((one) => {
          return new DobEarningsEntity(one);
        });

        return new PagedResponseDto<DobEarningsEntity>(
          dataFormat,
          skip,
          take,
          count,
        );
      } else {
        const earningss =
          await this.prisma.dobEarningsLog.findMany(earningsFindInputs);

        const dataFormat = earningss.map((one) => {
          return new DobEarningsEntity(one);
        });

        return new PagedResponseDto<DobEarningsEntity>(
          dataFormat,
          skip,
          take,
          0,
        );
      }
    } catch (error) {
      this.logger.debug('Error Earnings Page', error);
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid query params.',
        });
      } else {
        throw new InternalServerErrorException();
      }
    }
  }
}
