import { Controller, Get, Req } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { EarningsService } from './earnings.service';
import { ApiPaging } from 'src/common/decorators/api-paging.decorator';
import { PagingQuery } from 'src/common/decorators/paging-query.decorator';
import { QueryTimeParamDto } from 'src/common/dtos/query-time-param.dto';
import { AuthorEarningsResponseDto } from './dto/author-response.dto';
import { HoldEarningsResponseDto } from './dto/hold-response.dto';
import { DobEarningsEntity } from './entities/dob-earnings.entity';

@ApiTags('earnings')
@Controller('earnings')
export class EarningsController {
  constructor(private readonly earningsService: EarningsService) {}

  @Get('bookTotal')
  @ApiOperation({
    summary: '列出作者收益累计数据',
  })
  @ApiOkResponse({ type: AuthorEarningsResponseDto })
  @ApiBearerAuth()
  findAuthorTotalEarnings(@Req() req) {
    return this.earningsService.findAuthorTotalEarnings(req.user.sub);
  }

  @Get('book')
  @ApiPaging(DobEarningsEntity, QueryTimeParamDto)
  @ApiOperation({
    summary: '列出作者收益日志',
  })
  @ApiBearerAuth()
  @ApiExtraModels(DobEarningsEntity)
  findAuthorEarnings(
    @Req() req,
    @PagingQuery(QueryTimeParamDto) params: QueryTimeParamDto,
  ) {
    return this.earningsService.findAuthorPagination(req.user.sub, params);
  }

  @Get('assetsTotal')
  @ApiOperation({
    summary: '列出资产收益累计数据',
  })
  @ApiOkResponse({ type: HoldEarningsResponseDto })
  @ApiBearerAuth()
  findHoldTotalEarnings(@Req() req) {
    return this.earningsService.findHoldTotalEarnings(req.user.sub);
  }

  @Get('assets')
  @ApiPaging(DobEarningsEntity, QueryTimeParamDto)
  @ApiOperation({
    summary: '列出资产收益日志',
  })
  @ApiBearerAuth()
  @ApiExtraModels(DobEarningsEntity)
  findHoldEarnings(
    @Req() req,
    @PagingQuery(QueryTimeParamDto) params: QueryTimeParamDto,
  ) {
    return this.earningsService.findHoldPagination(req.user.sub, params);
  }
}
