import { Controller, Get, Param } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Public } from 'src/iam/auth/decorators/public.decorators';
import { IssueService } from './issue.service';
import { PagingQuery } from 'src/common/decorators/paging-query.decorator';
import { ApiPaging } from 'src/common/decorators/api-paging.decorator';
import { IssueEntity } from './entities/issue.entity';
import { QueryIssueDto } from './dto/query-issue.dto';

@Controller('issues')
@ApiTags('issues')
export class IssueController {
  constructor(private readonly issueService: IssueService) {}

  @Get('faq')
  @ApiPaging(IssueEntity, QueryIssueDto)
  @ApiOperation({
    summary: '常见问题列表',
  })
  @Public()
  findFAQ(@PagingQuery(QueryIssueDto) params: QueryIssueDto) {
    return this.issueService.listIssueFAQ(params);
  }

  @Get('help')
  @ApiPaging(IssueEntity, QueryIssueDto)
  @ApiOperation({
    summary: '帮助中心列表',
  })
  @Public()
  findHelp(@PagingQuery(QueryIssueDto) params: QueryIssueDto) {
    return this.issueService.listIssueHelp(params);
  }

  @Get(':id')
  @ApiOperation({
    summary: '根据id获取单条信息',
  })
  @ApiOkResponse({ type: IssueEntity })
  @Public()
  async findOne(@Param('id') id: number) {
    return new IssueEntity(await this.issueService.findOne(id));
  }
}
