import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { IssueType, Prisma } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { PagedResponseDto } from 'src/common/dtos/paged-response.dto';
import { IssueEntity } from './entities/issue.entity';
import { QueryIssueDto } from './dto/query-issue.dto';

@Injectable()
export class IssueService {
  private logger = new Logger(IssueService.name);

  constructor(private prisma: PrismaService) {}

  async listIssueFAQ(
    params: QueryIssueDto,
  ): Promise<PagedResponseDto<IssueEntity>> {
    const { skip, take, allowCount, keyword, lang } = params;
    const filter: Prisma.IssueWhereInput = {
      type: IssueType.FAQ,
      ask: keyword
        ? {
            contains: keyword,
          }
        : undefined,
      language: lang ?? undefined,
    };
    const issueFindInputs: Prisma.IssueFindManyArgs = {
      select: { id: true, ask: true, answer: true, sort: true },
      where: filter,
      orderBy: [{ sort: 'asc' }],
      skip,
      take,
    };

    try {
      if (allowCount) {
        const [issues, count] = await this.prisma.$transaction([
          this.prisma.issue.findMany(issueFindInputs),
          this.prisma.issue.count({ where: filter }),
        ]);

        const dataFormat = issues.map((one) => {
          return new IssueEntity(one);
        });

        return new PagedResponseDto<IssueEntity>(dataFormat, skip, take, count);
      } else {
        const issues = await this.prisma.issue.findMany(issueFindInputs);

        const dataFormat = issues.map((one) => {
          return new IssueEntity(one);
        });

        return new PagedResponseDto<IssueEntity>(dataFormat, skip, take, 0);
      }
    } catch (error) {
      this.logger.debug('Error Cluster Page', error);
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid query params.',
        });
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  async listIssueHelp(
    params: QueryIssueDto,
  ): Promise<PagedResponseDto<IssueEntity>> {
    const { skip, take, allowCount, keyword, lang } = params;
    const filter: Prisma.IssueWhereInput = {
      type: IssueType.HELP,
      ask: keyword
        ? {
            contains: keyword,
          }
        : undefined,
      language: lang ?? undefined,
    };
    const issueFindInputs: Prisma.IssueFindManyArgs = {
      where: filter,
      orderBy: [{ sort: 'asc' }],
      skip,
      take,
    };

    try {
      if (allowCount) {
        const [issues, count] = await this.prisma.$transaction([
          this.prisma.issue.findMany(issueFindInputs),
          this.prisma.issue.count({ where: filter }),
        ]);

        const dataFormat = issues.map((one) => {
          return new IssueEntity(one);
        });

        return new PagedResponseDto<IssueEntity>(dataFormat, skip, take, count);
      } else {
        const issues = await this.prisma.issue.findMany(issueFindInputs);

        const dataFormat = issues.map((one) => {
          return new IssueEntity(one);
        });

        return new PagedResponseDto<IssueEntity>(dataFormat, skip, take, 0);
      }
    } catch (error) {
      this.logger.debug('Error Cluster Page', error);
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException({
          status: HttpStatus.BAD_REQUEST,
          message: 'Invalid query params.',
        });
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  async findOne(id: number) {
    const data = this.prisma.issue.findUnique({ where: { id } });
    if (!data) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Not Find.',
      });
    }
    return data;
  }
}
