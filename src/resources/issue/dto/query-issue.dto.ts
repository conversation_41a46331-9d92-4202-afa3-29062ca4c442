import { ApiProperty, PartialType } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { Expose } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryParamDto } from 'src/common/dtos/query-param.dto';

export class QueryIssueDto extends PartialType(QueryParamDto) {
  // search
  @ApiProperty({
    required: false,
    description: '语言 [CN:中文  EN: 英语  JA: 日语  FI: 芬兰语]',
    default: $Enums.Language.EN,
    enum: $Enums.Language,
  })
  @IsOptional()
  @IsEnum($Enums.Language)
  @Expose()
  lang?: $Enums.Language = $Enums.Language.EN;
}
