import { ApiProperty } from '@nestjs/swagger';
import { $Enums, Issue, IssueGroup } from '@prisma/client';
import { Exclude } from 'class-transformer';

export class IssueEntity implements Issue {
  constructor(partial: Partial<IssueEntity>) {
    Object.assign(this, partial);
  }
  @ApiProperty()
  language: $Enums.Language;

  @Exclude()
  type: $Enums.IssueType;

  @ApiProperty({
    description:
      '[FAQ:常见问题,PUBLISH_BOOK:发布作品相关,PUT_DOWN:上下架,PERSONAL_DATA:个人资料,ACCOUNT:账号,OTHER:其他]',
  })
  group: IssueGroup;

  @ApiProperty({
    description: '排序',
  })
  sort: number;

  @ApiProperty()
  id: number;

  @ApiProperty()
  ask: string;

  @ApiProperty()
  answer: string;

  @Exclude()
  createdAt: Date;

  @Exclude()
  updatedAt: Date;
}
