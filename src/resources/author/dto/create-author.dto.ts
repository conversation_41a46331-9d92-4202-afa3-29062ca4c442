import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { IsEmail, IsEnum, IsNotEmpty, IsString } from 'class-validator';

export class CreateAuthorDto {
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: '作者名字',
  })
  name: string;

  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: '作者姓名',
  })
  surname: string;

  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: '作者邮箱',
  })
  email: string;

  @ApiProperty({
    required: true,
    default: $Enums.AuthorIdentity.AUTHOR,
    enum: $Enums.AuthorIdentity,
    description: '身份标识',
  })
  @IsEnum($Enums.AuthorIdentity)
  identity: $Enums.AuthorIdentity;

  @ApiProperty({
    required: true,
    description: '作者简介',
  })
  @IsString()
  abstract: string;
}
