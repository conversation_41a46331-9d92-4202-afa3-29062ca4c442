import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { AuthorEntity } from './entities/author.entity';
import { AuthorService } from './author.service';
import { CreateAuthorDto } from './dto/create-author.dto';

@ApiTags('author')
@Controller('author')
export class AuthorController {
  constructor(private readonly authorService: AuthorService) {}

  @Post()
  @ApiOperation({
    summary: '入住申请',
    description: '申请为一个新作者',
  })
  @ApiBearerAuth()
  @ApiCreatedResponse({ type: AuthorEntity })
  async create(@Req() req, @Body() createAuthorDto: CreateAuthorDto) {
    const data = await this.authorService.create(req.user.sub, createAuthorDto);
    const days = await this.authorService.getAuditDays();
    return new AuthorEntity({ ...data, auditDays: days });
  }

  @Get('my')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '我的入住审核',
  })
  @ApiOkResponse({ type: AuthorEntity })
  async findMyPayment(@Req() req) {
    return new AuthorEntity(await this.authorService.findByUId(req.user.sub));
  }
}
