import { Module } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { AuthorController } from './author.controller';
import { AuthorService } from './author.service';
import { StripeModule } from '../stripe/stripe.module';

@Module({
  imports: [PrismaModule, StripeModule],
  controllers: [AuthorController],
  providers: [AuthorService, StringService],
  exports: [AuthorService],
})
export class AuthorModule {}
