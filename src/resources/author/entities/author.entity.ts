import { ApiProperty } from '@nestjs/swagger';
import { $Enums, Author } from '@prisma/client';
import { Type } from 'class-transformer';
import { UserEntity } from 'src/resources/users/entities/user.entity';

export class AuthorEntity implements Author {
  constructor({ ...data }: Partial<AuthorEntity>) {
    Object.assign(this, data);
  }

  @ApiProperty({
    description: '[PENDING:审核中,APPROVED:审核通过,FAILED:审核拒绝]',
  })
  status: $Enums.BookStatus;

  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  surname: string;

  @ApiProperty()
  email: string;

  @ApiProperty({
    description:
      '[AUTHOR:作者,AUTHOR_BROKER:作者经纪人,COPYRIGHT_OWNER:版权所有者,INSTITUTION:出版机构,INVITEE:被邀请者]',
  })
  identity: $Enums.AuthorIdentity;

  @ApiProperty()
  abstract: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  @Type(() => UserEntity)
  user?: UserEntity;

  @ApiProperty({
    description: '审核天数',
  })
  auditDays: string;

  createdAt: Date;
}
