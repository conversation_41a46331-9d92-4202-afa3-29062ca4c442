import {
  HttpException,
  HttpStatus,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import { CreateAuthorDto } from './dto/create-author.dto';
import { BookStatus } from '@prisma/client';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthorService {
  private audit_days;
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {
    this.audit_days = configService.get<string>('AUDIT_DAYS');
  }

  async create(uid: string, createAuthorDto: CreateAuthorDto) {
    const clonedPayload = {
      ...createAuthorDto,
    };
    if (clonedPayload.email) {
      const authorObject = await this.prisma.author.findFirst({
        where: {
          email: clonedPayload.email,
          status: { not: BookStatus.FAILED },
        },
      });

      if (authorObject)
        throw new UnprocessableEntityException({
          status: HttpStatus.BAD_REQUEST,
          errors: {
            email: 'emailAlreadyExists',
          },
        });
    }

    return this.prisma.author.create({
      data: {
        ...clonedPayload,
        status: BookStatus.PENDING,
        user: { connect: { id: uid } },
      },
    });
  }

  async findByUId(uid: string) {
    const data = await this.prisma.author.findFirst({
      where: { userId: uid },
      orderBy: { createdAt: 'desc' },
    });
    if (!data) {
      throw new HttpException(
        {
          status: HttpStatus.OK,
          message: 'My Author Not Find',
        },
        HttpStatus.OK,
      );
    }
    return data;
  }

  async getAuditDays() {
    return this.audit_days;
  }
}
