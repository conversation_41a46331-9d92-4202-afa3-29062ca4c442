import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { ApiPaging } from 'src/common/decorators/api-paging.decorator';
import { PagingQuery } from 'src/common/decorators/paging-query.decorator';
import { Public } from 'src/iam/auth/decorators/public.decorators';
import { BooksService } from '../books/books.service';
import { LuluService } from '../lulu/lulu.service';
import { CalcPrintJobCostDto } from './dto/calc-print-job-cost.dto';
import { CheckoutPaymentDto } from './dto/checkout-payment.dto';
import { CreateOrderDto } from './dto/create-order.dto';
import { GetShippingOptionsDto } from './dto/get-shipping-option-dto';
import { QueryMyPaymentDto } from './dto/query-payment.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { PrintOrderEntity } from './entities/order.entity';
import { OrdersService } from './orders.service';

@ApiTags('print-orders')
@Controller('orders')
export class OrdersController {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly luluService: LuluService,
    private readonly booksService: BooksService,
  ) {}

  @ApiOperation({
    summary: '创建打印订单',
    description: '如果用户已经有相同任务的订单，则应该替换。',
  })
  @ApiBearerAuth()
  @Post()
  @ApiCreatedResponse({ type: PrintOrderEntity })
  async create(@Req() req, @Body() createOrderDto: CreateOrderDto) {
    return new PrintOrderEntity(
      await this.ordersService.create(req.user.sub, createOrderDto),
    );
  }

  @Get('my/print_log')
  @ApiPaging(PrintOrderEntity, QueryMyPaymentDto)
  @ApiBearerAuth()
  @ApiOperation({
    summary: '查询我的打印订单',
    description: '查询，过滤，搜索我的打印顺序与分页。',
  })
  findMyOrders(
    @Req() req,
    @PagingQuery(QueryMyPaymentDto) params: QueryMyPaymentDto,
  ) {
    return this.ordersService.findPaginationByUser(req.user.sub, params);
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '按Id查找我的打印订单',
  })
  @ApiOkResponse({ type: PrintOrderEntity })
  async findOne(@Req() req, @Param('id') id: string) {
    return new PrintOrderEntity(
      await this.ordersService.findByUserAndId(req.user.sub, id),
    );
  }

  @ApiOperation({
    summary: '更新我的打印订单',
  })
  @ApiBearerAuth()
  @Patch()
  @ApiOkResponse({ type: PrintOrderEntity })
  async update(@Req() req, @Body() updateOrderDto: UpdateOrderDto) {
    return new PrintOrderEntity(
      await this.ordersService.create(req.user.sub, updateOrderDto),
    );
  }

  @ApiOperation({
    summary: '根据id删除打印订单',
  })
  @ApiBearerAuth()
  @Delete(':id')
  @ApiOkResponse({ type: PrintOrderEntity })
  async remove(@Req() req, @Param('id') id: string) {
    return new PrintOrderEntity(
      await this.ordersService.remove(req.user.sub, id),
    );
  }

  @ApiOperation({
    summary: '计算打印成本',
  })
  @Post('calc-print-costs')
  calcPrintJobCost(@Body() calcPrintJobCostDto: CalcPrintJobCostDto) {
    return this.ordersService.calcPrintJobCost(calcPrintJobCostDto);
  }

  @ApiOperation({
    summary: '结帐订单，并返回付款意图客户端秘密',
    description:
      '如果是visa支付:客户端应该重定向到条带页面与支付意图客户端秘密: https://checkout.stripe.com/c/pay/${client_secret}<br/>如果是钱包支付：则跟购买流程一直即可',
  })
  @Post('checkout')
  checkout(@Req() req, @Body() checkoutPaymentDto: CheckoutPaymentDto) {
    return this.ordersService.checkoutOrderPayment(
      req.user.sub,
      checkoutPaymentDto,
    );
  }

  @ApiOperation({
    summary: '是否可以创建打印书籍',
    description: '是否可以创建打印书籍 如果已经使用过 则报错',
  })
  @Public()
  @ApiBearerAuth()
  @Get('is_can_create/:dobId')
  async isCanCreate(@Param('dobId') dobId: string) {
    return await this.ordersService.isCanCreate(dobId);
  }

  @ApiOperation({
    summary: '使用LuLu API获取支持的shipping options',
  })
  @Post('shipping-options')
  async getShippingOptions(@Body() postData: GetShippingOptionsDto) {
    return await this.ordersService.getShippingOptions(postData);
  }
}
