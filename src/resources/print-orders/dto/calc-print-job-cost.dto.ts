import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  MaxLength,
} from 'class-validator';

export class CalcPrintJobCostDto {
  @ApiProperty()
  @IsUUID()
  bookId: string;

  @ApiProperty()
  @IsNumber()
  quantity: number = 1;

  @ApiProperty({
    default: $Enums.ShippingLevel.MAIL,
  })
  @IsEnum($Enums.ShippingLevel)
  shippingOptionLevel: $Enums.ShippingLevel;

  @ApiProperty()
  @IsString()
  city: string;

  @ApiProperty()
  @IsString()
  @MaxLength(2)
  countryCode: string;

  @ApiProperty()
  @IsString()
  @MaxLength(20)
  @Matches(/^\+?[\d\s\-.\/()]{8,20}$/)
  phone: string;

  @ApiProperty()
  @IsString()
  @MaxLength(64)
  postcode: string;

  @ApiProperty()
  @IsString()
  street1: string;

  @ApiProperty()
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isBusiness?: boolean = false;

  @ApiProperty()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @IsOptional()
  organization?: string;

  @ApiProperty()
  @IsOptional()
  stateCode?: string;

  @ApiProperty()
  @IsOptional()
  street2?: string;

  @ApiProperty({
    enum: $Enums.ShippingTitle,
    default: $Enums.ShippingTitle.DR,
  })
  @IsOptional()
  title?: $Enums.ShippingTitle;
}
