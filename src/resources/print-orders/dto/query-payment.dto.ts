import { ApiProperty, PartialType } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { Expose } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryParamDto } from 'src/common/dtos/query-param.dto';

export class QueryMyPaymentDto extends PartialType(QueryParamDto) {
  // search
  @ApiProperty({
    required: false,
    description: 'DobID 资产里面的dobID',
  })
  @Expose()
  @IsOptional()
  dobID?: string;

  @ApiProperty({
    required: false,
    name: 'level',
    description: '版权等级',
    enum: $Enums.DnaLevel,
  })
  @IsOptional()
  @IsEnum($Enums.DnaLevel)
  @Expose()
  level?: $Enums.DnaLevel;
}
