import { ApiProperty } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

export class CheckoutPaymentDto {
  @ApiProperty({
    enum: $Enums.PaymentProvider,
    default: $Enums.PaymentProvider.STRIPE,
  })
  @IsOptional()
  @IsEnum($Enums.PaymentProvider)
  provider: $Enums.PaymentProvider = $Enums.PaymentProvider.STRIPE;

  @ApiProperty({
    enum: $Enums.CURRENCY,
    default: $Enums.CURRENCY.USD,
  })
  @IsOptional()
  @IsEnum($Enums.CURRENCY)
  currency: $Enums.CURRENCY = $Enums.CURRENCY.USD;

  @ApiProperty({
    description:
      '如果是版权购买(spore)请求,就需要用到[获取dna(dna)接口]返回的orderID<br/>如果是打印订单(print-orders)请求,就需要用到创建[打印订单(orders)接口]返回的orderID',
  })
  @IsUUID()
  orderId: string;

  @ApiProperty({
    enum: $Enums.PayNetWork,
    description: '钱包支付网络|如果是钱包支付就需要该值',
  })
  @IsOptional()
  @IsEnum($Enums.PayNetWork)
  network: $Enums.PayNetWork;

  @ApiProperty({
    description: '钱包支付HASH|如果是钱包支付就需要该值',
  })
  @IsOptional()
  @IsString()
  txhash: string;
}
