import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  MaxLength,
} from 'class-validator';

export class GetShippingOptionsDto {
  @ApiProperty()
  @IsUUID()
  bookId: string;

  @ApiProperty()
  @IsOptional()
  city?: string;

  @ApiProperty()
  @IsString()
  @MaxLength(2)
  country: string;

  @ApiProperty()
  @IsOptional()
  @MaxLength(20)
  @Matches(/^\+?[\d\s\-.\/()]{8,20}$/)
  phone_number: string;

  @ApiProperty()
  @IsOptional()
  @MaxLength(64)
  postcode: string;

  @ApiProperty()
  @IsOptional()
  street1?: string;

  @ApiProperty({
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  is_business?: boolean = false;

  @ApiProperty({
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  is_postbox?: boolean = false;

  @ApiProperty()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @IsOptional()
  organization?: string;

  @ApiProperty()
  @IsOptional()
  @MaxLength(3)
  state?: string;

  @ApiProperty()
  @IsOptional()
  street2?: string;
}
