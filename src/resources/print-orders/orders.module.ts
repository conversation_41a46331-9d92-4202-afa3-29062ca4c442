import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { BooksModule } from '../books/books.module';
import { LuluModule } from '../lulu/lulu.module';
import { PaymentModule } from '../payment/payment.module';
import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';
import { CryptoService } from '../crypto/crypto.service';
import { DNAService } from 'src/common/utils/dna_encode.service';

@Module({
  imports: [
    PrismaModule,
    LuluModule,
    BooksModule,
    PaymentModule,
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),
  ],
  controllers: [OrdersController],
  providers: [OrdersService, StringService, CryptoService, DNAService],
})
export class PrintOrdersModule {}
