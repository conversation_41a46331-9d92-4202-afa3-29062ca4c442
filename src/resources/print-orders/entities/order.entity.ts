import { ApiProperty } from '@nestjs/swagger';
import { $Enums, PrintOrder } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Exclude } from 'class-transformer';
import { PaymentEntity } from 'src/resources/payment/entities/payment.entity';

export class PrintOrderEntity implements PrintOrder {
  constructor(partial: Partial<PrintOrderEntity>) {
    Object.assign(this, partial);
  }
  @ApiProperty()
  txhash: string;

  @ApiProperty()
  network: $Enums.PayNetWork;

  @ApiProperty()
  amount: Decimal;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @Exclude()
  paymentId: string;

  @ApiProperty()
  payment?: PaymentEntity;

  @ApiProperty({
    description: '数量',
  })
  quantity: number;

  @ApiProperty({
    description: '邮箱',
  })
  email: string;

  @ApiProperty()
  isBusiness: boolean;

  @ApiProperty({
    description: '机构',
  })
  organization: string;

  @ApiProperty({
    description: '街道',
  })
  street2: string;

  @ApiProperty()
  title: $Enums.ShippingTitle;

  @ApiProperty()
  shippingOptionLevel: $Enums.ShippingLevel;

  @ApiProperty()
  printJobId: string;

  @Exclude()
  bookId: string;

  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  dobId: string;

  @ApiProperty({
    description: '城市',
  })
  city: string;

  @ApiProperty({
    description: '国家编码',
  })
  countryCode: string;

  @ApiProperty({
    description: '联系人姓名',
  })
  name: string;

  @ApiProperty({
    description: '联系方式',
  })
  phone: string;

  @ApiProperty({
    description: '请求码',
  })
  postcode: string;

  @ApiProperty({
    description: '状态码',
  })
  stateCode: string;

  @ApiProperty({
    description: '街道1',
  })
  street1: string;

  @ApiProperty()
  shippingLevel: $Enums.ShippingLevel;

  @ApiProperty()
  status: $Enums.PrintOrderStatus;

  @ApiProperty()
  collection_address?: string;
}
