import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrintOrderStatus, Prisma } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { PagedResponseDto } from 'src/common/dtos/paged-response.dto';
import { StringService } from 'src/common/utils/string.service';
import { LuluService } from '../lulu/lulu.service';
import { CreatePaymentDto } from '../payment/dto/create-payment.dto';
import { PaymentService } from '../payment/payment.service';
import { CalcPrintJobCostDto } from './dto/calc-print-job-cost.dto';
import { CheckoutPaymentDto } from './dto/checkout-payment.dto';
import { CreateOrderDto } from './dto/create-order.dto';
import { GetShippingOptionsDto } from './dto/get-shipping-option-dto';
import { QueryMyPaymentDto } from './dto/query-payment.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { PrintOrderEntity } from './entities/order.entity';
import { CryptoService } from '../crypto/crypto.service';
import { ConfigService } from '@nestjs/config';
import { PaymentEntity } from '../payment/entities/payment.entity';

@Injectable()
export class OrdersService {
  private collection_address;
  constructor(
    private prisma: PrismaService,
    private luluService: LuluService,
    private paymentService: PaymentService,
    private stringService: StringService,
    private cryptoService: CryptoService,
    private configService: ConfigService,
  ) {
    this.collection_address = {
      eth: configService.get<string>('OFFICIAL_ADDRESS_ETH'),
      btc: configService.get<string>('OFFICIAL_ADDRESS_BTC'),
      ckb: configService.get<string>('OFFICIAL_ADDRESS_CKB'),
    };
  }

  async create(
    userId: string,
    createOrUpdateOrderDto: CreateOrderDto | UpdateOrderDto,
  ) {
    const { bookId, dobId, ...orderDto } = createOrUpdateOrderDto;
    // check if the dob is owned by current user
    const dob = await this.prisma.dob.findFirst({
      where: {
        dobId: dobId,
      },
      include: {
        printOrder: true,
      },
    });

    if (!dob) {
      throw new ForbiddenException(
        'Invalid dob id: the dob is not exist or not owned by current user.',
      );
    }

    // check if there's an exist completed print order for the dob
    if (
      dob.printOrder &&
      dob.printOrder.status in
        [PrintOrderStatus.COMPLETE, PrintOrderStatus.PAID]
    ) {
      throw new ForbiddenException(
        'Invalid request: Only one print order allowed for one dob.',
      );
    }
    // check if the dob is owned by current user
    // TODO if(dob.ownerAddress === userId) {}

    //  create or update order
    const order = await this.prisma.printOrder.upsert({
      where: {
        dobId: dob.id,
      },
      create: {
        ...orderDto,
        user: {
          connect: {
            id: userId,
          },
        },
        book: {
          connect: {
            id: bookId,
          },
        },
        dob: {
          connect: {
            dobId: dob.id,
          },
        },
      } as Prisma.PrintOrderCreateInput,
      update: {
        ...orderDto,
        user: {
          connect: {
            id: userId,
          },
        },
        dob: {
          connect: {
            dobId: dob.id,
          },
        },
      } as Prisma.PrintOrderUpdateInput,
    });
    return { ...order, collection_address: this.collection_address };
  }

  async findPaginationByUser(
    sub: string,
    params: QueryMyPaymentDto,
  ): Promise<PagedResponseDto<PrintOrderEntity>> {
    const {
      skip,
      take,
      allowCount,
      sort: order,
      keyword,
      dobID,
      level,
    } = params;
    const filter: Prisma.PrintOrderWhereInput = {
      userId: sub,
      book: {
        title: keyword ? { contains: keyword.trim() } : undefined,
      },
      dob: {
        dobId: dobID ?? undefined,
        level: level ?? undefined,
      },
    };
    const orderFindInputs: Prisma.PrintOrderFindManyArgs = {
      where: filter,
      orderBy: [this.stringService.toPrismaOrderByObject(order)],
      include: {
        payment: true,
        dob: true,
        book: true,
      },
      skip,
      take,
    };

    try {
      if (allowCount) {
        const [orders, count] = await this.prisma.$transaction([
          this.prisma.printOrder.findMany(orderFindInputs),
          this.prisma.printOrder.count({ where: filter }),
        ]);

        const dataFormat = orders.map((one) => {
          if (one['payment']) {
            one['payment'] = new PaymentEntity(one['payment']);
          }
          return new PrintOrderEntity(one);
        });

        return new PagedResponseDto<PrintOrderEntity>(
          dataFormat,
          skip,
          take,
          count,
        );
      } else {
        const orders = await this.prisma.printOrder.findMany(orderFindInputs);

        const dataFormat = orders.map((one) => {
          if (one['payment']) {
            one['payment'] = new PaymentEntity(one['payment']);
          }
          return new PrintOrderEntity(one);
        });

        return new PagedResponseDto<PrintOrderEntity>(
          dataFormat,
          skip,
          take,
          0,
        );
      }
    } catch (error) {
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new BadRequestException('Invalid query params.');
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  async findByUserAndId(sub: string, id: string) {
    const order = await this.prisma.printOrder.findUnique({
      where: {
        id,
        userId: sub,
      },
      include: {
        book: true,
        dob: true,
      },
    });
    return { ...order, collection_address: this.collection_address };
  }

  remove(userId: string, id: string) {
    return this.prisma.printOrder.delete({
      where: {
        userId,
        id,
        OR: [
          { status: PrintOrderStatus.CANCEL },
          { status: PrintOrderStatus.DRAFT },
        ],
      },
    });
  }

  async calcPrintJobCost(calcPrintJobCostDto: CalcPrintJobCostDto) {
    const { bookId, quantity, ...address } = calcPrintJobCostDto;
    const book = await this.prisma.book.findUnique({
      where: { id: bookId },
      include: {
        specification: true,
      },
    });

    return await this.luluService.calcPrintJobCost({
      line_items: [
        {
          page_count: book.specification.pageCount,
          pod_package_id: book.specification.podPackageId,
          quantity: quantity ?? 1,
        },
      ],
      shipping_address: {
        city: address.city,
        country_code: address.countryCode,
        name: address.name,
        phone_number: address.phone,
        postcode: address.postcode,
        state_code: address.stateCode,
        street1: address.street1,
      },
      shipping_option: address.shippingOptionLevel,
    });
  }

  async checkoutOrderPayment(
    userId: string,
    checkoutPaymentDto: CheckoutPaymentDto,
  ) {
    const order = await this.prisma.printOrder.findUnique({
      where: { id: checkoutPaymentDto.orderId, userId: userId },
      include: {
        book: {
          include: {
            specification: true,
          },
        },
      },
    });
    if (!order) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Print Order Not Find',
      });
    }
    const costDetails = await this.luluService.calcPrintJobCost({
      line_items: [
        {
          page_count: order.book.specification.pageCount,
          pod_package_id: order.book.specification.podPackageId,
          quantity: order.quantity ?? 1,
        },
      ],
      shipping_address: {
        city: order.city,
        country_code: order.countryCode,
        name: order.name,
        phone_number: order.phone,
        postcode: order.postcode,
        state_code: order.stateCode,
        street1: order.street1,
      },
      shipping_option: order.shippingOptionLevel,
    });
    const totalCost = costDetails.total_cost_incl_tax;
    // 钱包支付
    if (checkoutPaymentDto.network && checkoutPaymentDto.txhash) {
      // check transfer hash
      const txData = await this.cryptoService.findTransferByHash(
        checkoutPaymentDto.network,
        checkoutPaymentDto.txhash,
      );
      if (!txData.isSuccess) {
        //记录txhash 和 network dna|异步任务跑BTC交易结果
        await this.prisma.printOrder.update({
          where: { id: order.id },
          data: {
            status: PrintOrderStatus.PENDING,
            network: checkoutPaymentDto.network,
            txhash: checkoutPaymentDto.txhash,
            amount: totalCost,
          },
        });
        throw new HttpException(
          {
            status: HttpStatus.OK,
            message:
              'Order submitted - The system will automatically purchase the copyright after the order is confirmed',
          },
          HttpStatus.OK,
        );
      }
      // 验证成功就返回order
      return await this.prisma.printOrder.update({
        where: { id: order.id },
        data: {
          status: PrintOrderStatus.PAID,
          network: checkoutPaymentDto.network,
          txhash: checkoutPaymentDto.txhash,
          amount: totalCost,
        },
      });
    } else {
      //visa卡支付
      const paymentDto: CreatePaymentDto = {
        userId: userId,
        provider: checkoutPaymentDto.provider,
        currency: checkoutPaymentDto.currency,
        printOrderId: order.id,
        amount: totalCost,
      };
      return this.paymentService.createPayment(paymentDto);
    }
  }

  async isCanCreate(dobId: string) {
    // check if the dob is owned by current user
    const dob = await this.prisma.dob.findFirst({
      where: {
        dobId: dobId,
      },
      include: {
        printOrder: true,
      },
    });

    if (!dob) {
      return {
        isCreate: false,
        message:
          'Invalid dob id: the dob is not exist or not owned by current user.',
      };
    }

    // check if there's an exist completed print order for the dob
    if (dob.printOrder) {
      if (
        dob.printOrder.status in
        [PrintOrderStatus.COMPLETE, PrintOrderStatus.PAID]
      ) {
        return {
          isCreate: false,
          message: 'Invalid request: Only one print order allowed for one dob.',
        };
      } else {
        return {
          isCreate: false,
          message: 'Invalid request: There are unpaid orders for one dob.',
        };
      }
    }

    return { isCreate: true };
  }

  async getShippingOptions(postData: GetShippingOptionsDto) {
    const { bookId, ...shippingAddress } = postData;
    const book = await this.prisma.book.findUnique({
      where: { id: bookId },
      include: {
        specification: true,
      },
    });
    return this.luluService.retrieveShippingOptions({
      line_items: [
        {
          page_count: book.specification.pageCount,
          pod_package_id: book.specification.podPackageId,
          quantity: 1,
        },
      ],
      shipping_address: {
        ...shippingAddress,
      },
      currency: 'USD',
    });
  }
}
