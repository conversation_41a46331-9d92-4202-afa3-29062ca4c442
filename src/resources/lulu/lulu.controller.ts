import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  ValidateCoverUrlDto,
  ValidateSourceUrlDto,
} from './dto/lulu-validation.dto';
import {
  ValidateCoverEntity,
  ValidateInteriorEntity,
} from './entities/lulu-job.entity';
import { LuluService } from './lulu.service';
import { Public } from 'src/iam/auth/decorators/public.decorators';

@ApiTags('lulu')
@Controller('lulu')
export class LuluController {
  constructor(private readonly luluService: LuluService) {}

  @ApiOperation({
    summary: '用LuLu API验证源文件',
  })
  @Post('validate-source')
  async validateInteriorFile(
    @Body() validateSourceUrlDto: ValidateSourceUrlDto,
  ) {
    return new ValidateInteriorEntity(
      await this.luluService.validateInterior(
        validateSourceUrlDto.sourceUrl,
        'source',
      ),
    );
  }

  @ApiOperation({
    summary: '获取LULU提供的洲',
  })
  @Get('lulu-state/:country_code')
  @Public()
  async getState(@Param('country_code') country_code: string) {
    return await this.luluService.getState(country_code);
  }

  @ApiOperation({
    summary: '获取源文件验证状态',
  })
  @Get('validate-source/:id')
  async getInteriorFileValidateStatus(@Param('id') id: number) {
    return new ValidateInteriorEntity(
      await this.luluService.retrieveValidateCoverFileStatus(id),
    );
  }

  @ApiOperation({
    summary: '用LuLu API验证封面文件',
  })
  @Post('validate-cover')
  async validateCoverFile(@Body() validateCoverUrlDto: ValidateCoverUrlDto) {
    return new ValidateCoverEntity(
      await this.luluService.validateInterior(
        validateCoverUrlDto.coverUrl,
        'cover',
      ),
    );
  }

  @ApiOperation({
    summary: '使用LuLu API获取封面文件验证状态',
  })
  @Get('validate-cover/:id')
  async getCoverFileValidateStatus(@Param('id') id: number) {
    return new ValidateCoverEntity(
      await this.luluService.retrieveValidateCoverFileStatus(id),
    );
  }
}
