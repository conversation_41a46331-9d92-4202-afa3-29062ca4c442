import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Injectable,
  Logger,
  RequestTimeoutException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { catchError, firstValueFrom } from 'rxjs';
import { LuluPrintJobEntity } from './entities/lulu-job.entity';

@Injectable()
export class LuluService {
  private readonly logger = new Logger(LuluService.name);
  private LULU_BASE_URI = 'https://api.sandbox.lulu.com';
  private clientId;
  private clientSecret;

  constructor(
    private http: HttpService,
    private config: ConfigService,
  ) {
    this.LULU_BASE_URI = config.getOrThrow<string>('LULU_API_BASE_URI', {
      infer: true,
    });
    this.clientId = config.getOrThrow<string>('LULU_API_CLIENT_ID', {
      infer: true,
    });
    this.clientSecret = config.getOrThrow<string>('LULU_API_CLIENT_SECRET', {
      infer: true,
    });
  }

  async _getAccessToken() {
    const basicToken = Buffer.from(
      `${this.clientId}:${this.clientSecret}`,
    ).toString('base64');
    const { data } = await firstValueFrom(
      this.http
        .post(
          `${this.LULU_BASE_URI}/auth/realms/glasstree/protocol/openid-connect/token`,
          { grant_type: 'client_credentials' },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              Authorization: `Basic ${basicToken}`,
            },
          },
        )
        .pipe(
          catchError((error) => {
            this.logger.error(error);
            throw error;
          }),
        ),
    );

    return data.access_token;
  }

  _handleError(error, type = '') {
    if (error.response) {
      this.logger.debug(error.response.data);
      this.logger.debug(error.response.status);
      if (error.response.status === 400 || error.response.status === 404) {
        throw new BadRequestException(type + error.response.data);
      }
    } else if (error.request) {
      this.logger.debug(error.request);
      throw new RequestTimeoutException(
        type + 'Network error for the backend.',
      );
    } else {
      this.logger.debug(error.message);
      throw error;
    }
  }

  /**
   * Calculate print job costs
   * @returns
   */
  async calcPrintJobCost(postData: object) {
    const { data } = await firstValueFrom(
      this.http
        .post(
          `${this.LULU_BASE_URI}/print-job-cost-calculations`,
          postData,
          // {
          //   line_items: [
          //     {
          //       page_count: calcPrintJobCostDto.,
          //       pod_package_id: '0600X0900BWSTDPB060UW444MXX',
          //       quantity: 1,
          //     },
          //   ],
          //   shipping_address: {
          //     city: 'Lübeck',
          //     country_code: 'GB',
          //     name: 'Hans Dampf',
          //     phone_number: '************',
          //     postcode: 'PO1 3AX',
          //     state_code: '',
          //     street1: 'Holstenstr. 48',
          //   },
          //   shipping_option: 'MAIL',
          // },
          {
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              Authorization: `Bearer ${await this._getAccessToken()}`,
            },
          },
        )
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );

    this.logger.debug('Calculate Cost: ', data);
    return data;
  }

  async retrieveShippingOptions(postData: object) {
    const { data } = await firstValueFrom(
      this.http
        .post(
          `${this.LULU_BASE_URI}/shipping-options`,
          postData,
          // {
          //   line_items: [
          //     {
          //       page_count: 324,
          //       pod_package_id: '0600X0900BWSTDPB060UW444MXX',
          //       quantity: 1,
          //     },
          //   ],
          //   shipping_address: {
          //     city: 'Lübeck',
          //     country: 'GB',
          //     name: 'Hans Dampf',
          //     phone_number: '************',
          //     postcode: 'PO1 3AX',
          //     state_code: '',
          //     street1: 'Holstenstr. 48',
          //   },
          //   currency: 'USD',
          // },
          {
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              Authorization: `Bearer ${await this._getAccessToken()}`,
            },
          },
        )
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );

    this.logger.debug('Shipping Options: ', data);
    return data;
  }

  /**
   * Create lulu print job
   * @returns
   */
  async createPrintJob(postData: object): Promise<LuluPrintJobEntity> {
    const { data } = await firstValueFrom(
      this.http
        .post(
          `${this.LULU_BASE_URI}/print-jobs`,
          postData,
          // {
          //   contact_email: '<EMAIL>',
          //   external_id: 'demo-time',
          //   line_items: [
          //     {
          //       external_id: 'item-reference-1',
          //       printable_normalization: {
          //         cover: {
          //           source_url:
          //             'https://www.dropbox.com/s/7bv6mg2tj0h3l0r/lulu_trade_perfect_template.pdf?dl=1&raw=1',
          //         },
          //         interior: {
          //           source_url:
          //             'https://www.dropbox.com/s/r20orb8umqjzav9/lulu_trade_interior_template-32.pdf?dl=1&raw=1',
          //         },
          //         pod_package_id: '0600X0900BWSTDPB060UW444MXX',
          //       },
          //       quantity: 1,
          //       title: 'My Book',
          //     },
          //   ],
          //   production_delay: 120,
          //   shipping_address: {
          //     city: 'Lübeck',
          //     country_code: 'GB',
          //     name: 'Hans Dampf',
          //     phone_number: '************',
          //     postcode: 'PO1 3AX',
          //     state_code: '',
          //     street1: 'Holstenstr. 48',
          //   },
          //   shipping_level: 'MAIL',
          // },
          {
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              Authorization: `Bearer ${await this._getAccessToken()}`,
            },
          },
        )
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );
    return data;
  }

  /**
   *
   * @param jobId
   * @returns
   */
  async retrieveSinglePrintJob(jobId: string) {
    const { data } = await firstValueFrom(
      this.http
        .get(`${this.LULU_BASE_URI}/print-jobs/${jobId}`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${await this._getAccessToken()}`,
          },
        })
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );
    this.logger.debug('Job ID: ', jobId, 'Job details: ', data);
    return data;
  }

  /**
   *
   * @param jobId
   * @returns
   */
  async retrievePrintJobCost(jobId: string) {
    const { data } = await firstValueFrom(
      this.http
        .get(`${this.LULU_BASE_URI}/print-jobs/${jobId}/costs`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${await this._getAccessToken()}`,
          },
        })
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );
    this.logger.debug('Job ID: ', jobId, 'Job Costs: ', data);
    return data;
  }
  /**
   * retrieve print order status
   * @param order  order params
   * @returns
   */
  async retrievePrintJobStatus(jobId: string) {
    const { data } = await firstValueFrom(
      this.http
        .get(`${this.LULU_BASE_URI}/print-jobs/${jobId}/status`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${await this._getAccessToken()}`,
          },
        })
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );
    this.logger.debug('Job ID: ', jobId, 'Job Status: ', data);
    return data;
  }

  /**
   * Cancel print job
   * @param jobId
   * @returns
   */
  async cancelPrintJob(jobId: string) {
    const { data } = await firstValueFrom(
      this.http
        .post(
          `${this.LULU_BASE_URI}/print-jobs/${jobId}/status`,
          { status: 'CANCELED' },
          {
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              Authorization: `Bearer ${await this._getAccessToken()}`,
            },
          },
        )
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );
    this.logger.debug('Cancel Job: ', jobId, 'Result: ', data);
    return data;
  }

  /**
   * validate interior file
   * @param fileUrl
   * @param type [source cover]
   * @returns
   */
  async validateInterior(fileUrl: string, type: string) {
    const { data } = await firstValueFrom(
      this.http
        .post(
          `${this.LULU_BASE_URI}/validate-interior`,
          { source_url: fileUrl },
          {
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              Authorization: `Bearer ${await this._getAccessToken()}`,
            },
          },
        )
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw type + error;
          }),
        ),
    );
    this.logger.debug('Validate File Source: ' + type, data);
    const { errors, ...result } = data;
    if (!errors) {
      return result;
    } else {
      throw new BadRequestException(type, errors);
    }
  }

  async getState(country_code: string) {
    const { data } = await firstValueFrom(
      this.http
        .post(
          `https://api.lulu.com/graphql/`,
          {
            operationName: 'countryValidationRules',
            variables: {
              countryCode: country_code,
            },
            query:
              'query countryValidationRules($countryCode: String!) {\n  countryValidationRules(countryCode: $countryCode) {\n    countryCode\n    addressFormat\n    addressLatinFormat\n    requiredFields\n    countryAreaType\n    countryAreaChoices\n    postalCodeMatchers\n    __typename\n  }\n}\n',
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Connection: 'keep-alive',
              Host: 'api.lulu.com',
            },
            timeout: 10000,
          },
        )
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw new BadRequestException(error);
          }),
        ),
    );
    return data;
  }

  /**
   * validate interior file status
   * @param id
   * @returns
   */
  async retrieveValidateInteriorStatus(id: number) {
    const { data } = await firstValueFrom(
      this.http
        .get(`${this.LULU_BASE_URI}/validate-interior/${id}`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${await this._getAccessToken()}`,
          },
        })
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );
    this.logger.debug('Validate File Source: ', data);
    const { errors, ...result } = data;
    if (!errors) {
      return result;
    } else {
      throw new BadRequestException(errors);
    }
  }

  /**
   * calculate required cover dimensions basing on interior data.
   * @param pod_package_id
   * @param page_count
   * @returns
   */
  async calcCoverDimensions(pod_package_id: string, page_count: number) {
    const { data } = await firstValueFrom(
      this.http
        .post(
          `${this.LULU_BASE_URI}/cover-dimensions`,
          { pod_package_id, interior_page_count: page_count },
          {
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              Authorization: `Bearer ${await this._getAccessToken()}`,
            },
          },
        )
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );
    this.logger.debug('Calc Cover Dimensions: ', pod_package_id, data);
    return data;
  }

  /**
   * validate cover file
   * @param pod_package_id
   * @param source_url
   * @param page_count
   * @returns
   */
  async validateCoverFile(
    pod_package_id: string,
    source_url: string,
    page_count: number,
  ) {
    const { data } = await firstValueFrom(
      this.http
        .post(
          `${this.LULU_BASE_URI}/validate-cover`,
          { pod_package_id, interior_page_count: page_count, source_url },
          {
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              Authorization: `Bearer ${await this._getAccessToken()}`,
            },
          },
        )
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );
    this.logger.debug('Validate cover: ', pod_package_id, data);
    const { errors, ...result } = data;
    if (!errors) {
      return result;
    } else {
      throw new BadRequestException(errors);
    }
  }

  /**
   * validate cover file status
   * @param pod_package_id
   * @param source_url
   * @param page_count
   * @returns
   */
  async retrieveValidateCoverFileStatus(id: number) {
    const { data } = await firstValueFrom(
      this.http
        .get(`${this.LULU_BASE_URI}/validate-cover/${id}`, {
          headers: {
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${await this._getAccessToken()}`,
          },
        })
        .pipe(
          catchError((error) => {
            this._handleError(error);
            throw error;
          }),
        ),
    );
    this.logger.debug('Validate cover: ', data);
    const { errors, ...result } = data;
    if (!errors) {
      return result;
    } else {
      throw new BadRequestException(errors);
    }
  }
}
