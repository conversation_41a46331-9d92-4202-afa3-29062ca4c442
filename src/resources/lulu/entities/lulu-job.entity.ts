import { ApiProperty } from '@nestjs/swagger';

export enum LuluValidationStatus {
  VALIDATING,
  VALIDATED,
  ERROR,
}
export class LuluPrintJobEntity {
  contact_email: string;
  date_created: string;
  date_modified: string;
  id: number;
  external_id: string;
  production_delay: number;
  production_due_time?: number;
  shipping_level: string;
  shipping_option_level: string;
}

export class ValidateInteriorEntity {
  @ApiProperty()
  id: number;

  @ApiProperty()
  source_url: string;

  @ApiProperty()
  page_count: number;

  @ApiProperty({
    enum: LuluValidationStatus,
  })
  status: LuluValidationStatus;

  @ApiProperty()
  valid_pod_package_ids: string[];

  constructor({ ...data }: Partial<ValidateInteriorEntity>) {
    Object.assign(this, data);
  }
}

export class ValidateCoverEntity {
  @ApiProperty()
  id: number;

  @ApiProperty()
  source_url: string;

  @ApiProperty({
    enum: LuluValidationStatus,
  })
  status: LuluValidationStatus;

  constructor({ ...data }: Partial<ValidateCoverEntity>) {
    Object.assign(this, data);
  }
}
