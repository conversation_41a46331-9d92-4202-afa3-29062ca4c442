import { ApiProperty } from '@nestjs/swagger';
import { Role, User } from '@prisma/client';
import { Exclude } from 'class-transformer';

export class UserEntity implements User {
  constructor(partial: Partial<UserEntity>) {
    Object.assign(this, partial);
  }
  @ApiProperty({
    description: '头像',
  })
  avatar: string;

  @ApiProperty()
  isWallet: boolean;

  @ApiProperty()
  id: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  name: string;

  @ApiProperty()
  email: string;

  @Exclude()
  password: string;

  @ApiProperty()
  emailConfirmed: boolean;

  @ApiProperty()
  locked: boolean;

  @ApiProperty()
  code: string;

  @ApiProperty()
  inviteCode: string;

  @ApiProperty()
  role: Role;
}
