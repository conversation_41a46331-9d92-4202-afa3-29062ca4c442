import { ApiProperty } from '@nestjs/swagger';
import { Discord, Role, Twitter } from '@prisma/client';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsString,
  MinLength,
} from 'class-validator';

export class UserDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsEmail()
  @IsNotEmpty()
  @ApiProperty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password?: string;

  @IsBoolean()
  emailConfirmed: boolean;

  @IsBoolean()
  locked: boolean;

  role: Role;

  twitter?: Twitter;

  discord?: Discord;
}
