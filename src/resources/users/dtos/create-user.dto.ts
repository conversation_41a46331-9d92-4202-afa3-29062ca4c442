import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, MinLength } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: '用户名',
  })
  name: string;

  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: '邮箱',
  })
  email: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  @ApiProperty({
    required: false,
    description: '密码',
  })
  password?: string;

  @IsString()
  @ApiProperty({
    required: false,
    description: '邀请码',
  })
  inviteCode?: string;
}
