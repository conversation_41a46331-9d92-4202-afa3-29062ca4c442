import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
} from 'class-validator';

export class UpdateUserDto extends PartialType(CreateUserDto) {
  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: '头像url',
  })
  avatar?: string;
}

export class ChangeEmailDto {
  @IsString()
  @IsNotEmpty()
  @Length(6)
  @ApiProperty({
    required: true,
    description: '验证码',
  })
  token: string;

  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: '新邮箱',
  })
  newEmail: string;

  @IsString()
  @IsNotEmpty()
  @Length(6)
  @ApiProperty({
    required: true,
    description: '新邮箱验证码',
  })
  newToken: string;
}

export class BindEmailDto {
  @IsString()
  @IsNotEmpty()
  @Length(6)
  @ApiProperty({
    required: true,
    description: '验证码',
  })
  token: string;

  @IsEmail()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: '邮箱',
  })
  email: string;
}
