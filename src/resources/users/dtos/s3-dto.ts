import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class S3Dto {
  @IsString()
  @ApiProperty({
    required: true,
    description: '操作类型',
    default: 'putObject',
  })
  @IsEnum(['putObject', 'getObject'])
  operation: string = 'putObject';

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
    description: '操作key，可以上上传/下载的文件名',
  })
  key: string;

  @IsString()
  @ApiProperty({
    required: false,
    description: 'ACL权限：public-read, private, public-read-write',
    default: 'public-read',
  })
  @IsOptional()
  @IsEnum(['public-read', 'private', 'public-read-write'])
  acl: string = 'public-read';
}
