import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PrismaModule } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';
import { TokenService } from './token.service';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { CryptoService } from '../crypto/crypto.service';
import { AuthService } from 'src/iam/auth/auth.service';
import { JwtModule } from '@nestjs/jwt';
import { MailService } from 'src/mail/mail.service';
import { MailModule } from 'src/mail/mail.module';
import { MailerService } from 'src/mailer/mailer.service';
import { DNAService } from 'src/common/utils/dna_encode.service';

@Module({
  controllers: [UsersController],
  providers: [
    StringService,
    TokenService,
    UsersService,
    CryptoService,
    AuthService,
    MailerService,
    MailService,
    DNAService,
  ],
  imports: [PrismaModule, JwtModule, MailModule],
  exports: [UsersService, TokenService],
})
export class UsersModule {}
