import {
  BadRequestException,
  HttpStatus,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';
import { User } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import { PrismaService } from 'nestjs-prisma';
import { AuthService } from 'src/iam/auth/auth.service';
import { MagicLinkLoginDto } from 'src/iam/auth/dtos/magic-link.dto';
import { CryptoService } from '../crypto/crypto.service';
import { CryptoSignDto } from '../crypto/dto/crypto-sign.dto';
import { CreateUserDto } from './dtos/create-user.dto';
import {
  BindEmailDto,
  ChangeEmailDto,
  UpdateUserDto,
} from './dtos/update-user.dto';

export const roundsOfHashing = 10;

@Injectable()
export class UsersService {
  constructor(
    private prisma: PrismaService,
    private cryptoService: CryptoService,
    private authService: AuthService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const clonedPayload = {
      ...createUserDto,
    };
    if (clonedPayload.password) {
      const hashedPassword = await bcrypt.hash(
        clonedPayload.password,
        roundsOfHashing,
      );
      clonedPayload.password = hashedPassword;
    }
    if (clonedPayload.email) {
      const userObject = await this.prisma.user.findUnique({
        where: {
          email: clonedPayload.email,
        },
      });

      if (userObject)
        throw new UnprocessableEntityException({
          status: HttpStatus.BAD_REQUEST,
          errors: {
            email: 'emailAlreadyExists',
          },
        });
    }
    if (clonedPayload.inviteCode) {
      const userObject = await this.prisma.user.findUnique({
        where: {
          code: clonedPayload.inviteCode,
        },
      });
      if (userObject) {
        throw new UnprocessableEntityException({
          status: HttpStatus.BAD_REQUEST,
          errors: {
            inviteCode: 'invalidInviteCode',
          },
        });
      }
    }

    return this.prisma.user.create({ data: clonedPayload });
  }

  async createForWallet(createUserWalletDto: CryptoSignDto): Promise<User> {
    // verify Sign
    const signResult = await this.cryptoService.verifySign(createUserWalletDto);
    if (!signResult) {
      throw new BadRequestException('VerifySign False');
    }
    // 如果已经存在用户 则直接返回用户
    const walletOld = await this.prisma.wallet.findUnique({
      where: { address: createUserWalletDto.ckbAddress },
    });
    if (walletOld.userId) {
      return await this.prisma.$transaction(async (tx) => {
        const user = await tx.user.findUnique({
          where: { id: walletOld.userId },
        });
        await tx.wallet.update({
          where: { address: createUserWalletDto.ckbAddress },
          data: {
            isSign: true,
            internalAddress:
              createUserWalletDto.signType == 'CCC'
                ? createUserWalletDto.publicKey
                : undefined,
          },
        });
        return user;
      });
    }
    const hashedPassword = await bcrypt.hash(
      createUserWalletDto.ckbAddress,
      roundsOfHashing,
    );
    return await this.prisma.$transaction(async (tx) => {
      const min = Math.pow(10, 4) - 1;
      const max = Math.pow(10, 5) - 1;
      const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
      const user = await tx.user.upsert({
        where: {
          email: createUserWalletDto.ckbAddress,
        },
        update: {
          password: hashedPassword,
        },
        create: {
          name: 'silentberry' + randomNumber,
          email: createUserWalletDto.ckbAddress,
          password: hashedPassword,
          isWallet: true,
        },
      });
      await tx.wallet.update({
        where: { address: createUserWalletDto.ckbAddress },
        data: {
          userId: user.id,
          isSign: true,
          // ccc登录 存两个地址
          internalAddress:
            createUserWalletDto.signType == 'CCC'
              ? createUserWalletDto.publicKey
              : undefined,
        },
      });
      return user;
    });
  }

  async findByWallet(ckbAddress: string) {
    const uid = await this.prisma.wallet.findUnique({
      where: { address: ckbAddress },
      select: { userId: true },
    });
    if (uid.userId) {
      return await this.prisma.user.findUnique({
        where: { id: uid.userId },
      });
    }
    return null;
  }

  async findById(id: string) {
    const user = await this.prisma.user.findUnique({ where: { id } });
    if (user.isWallet) {
      const wallet = await this.prisma.wallet.findUnique({
        where: { userId: user.id },
      });
      user['wallet'] = wallet.address;
      user['internalAddress'] = wallet.internalAddress;
    }
    return user;
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    const clonedPayload = { ...updateUserDto };

    if (clonedPayload.password) {
      clonedPayload.password = await bcrypt.hash(
        clonedPayload.password,
        roundsOfHashing,
      );
    }

    if (clonedPayload.email) {
      const userObject = await this.prisma.user.findUnique({
        where: {
          email: clonedPayload.email,
        },
      });
      if (userObject && userObject.id !== id) {
        throw new UnprocessableEntityException({
          status: HttpStatus.BAD_REQUEST,
          errors: {
            email: 'emailAlreadyExists',
          },
        });
      }
    }
    await this.prisma.user.update({ where: { id }, data: clonedPayload });
    return this.findById(id);
  }

  async changeEmail(id: string, bindEmailDto: ChangeEmailDto) {
    const clonedPayload = { ...bindEmailDto };
    const userOne = await this.prisma.user.findUnique({ where: { id } });
    if (!userOne || !userOne.email) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'User Not Find',
      });
    }
    if (clonedPayload.newEmail == userOne.email) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Email can not be the same',
      });
    }
    //check old code
    const oldCheckData: MagicLinkLoginDto = {
      email: userOne.email,
      token: bindEmailDto.token,
    };
    const checkOld = await this.authService.magicLoginValidate(
      oldCheckData,
      false,
    );
    if (!checkOld) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Check token Error',
      });
    }
    if (clonedPayload.newEmail) {
      const userObject = await this.prisma.user.findUnique({
        where: {
          email: clonedPayload.newEmail,
        },
      });
      if (userObject) {
        throw new UnprocessableEntityException({
          status: HttpStatus.BAD_REQUEST,
          errors: {
            email: 'emailAlreadyExists',
          },
        });
      }
    }
    //check new email
    const newCheckData: MagicLinkLoginDto = {
      email: clonedPayload.newEmail,
      token: clonedPayload.newToken,
    };
    await this.authService.magicLoginValidate(newCheckData, false);

    await this.prisma.user.update({
      where: { id },
      data: {
        email: clonedPayload.newEmail,
      },
    });
    return this.findById(id);
  }

  async bindEmail(id: string, bindEmailDto: BindEmailDto) {
    const clonedPayload = { ...bindEmailDto };
    // 检查邮箱是否被人绑定
    const userOld = await this.prisma.user.findUnique({
      where: { email: clonedPayload.email },
    });
    if (userOld) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Email has bind to another user',
      });
    }

    const userOne = await this.prisma.user.findUnique({ where: { id } });
    // 钱包用户才有绑定邮箱
    if (!userOne) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'User Not Found',
      });
    }
    // 判定是否三方
    let tripartite;
    if (userOne.isWallet == false) {
      // 查询google登录
      tripartite = await this.prisma.google.findUnique({
        where: { userId: userOne.id },
      });
    } else {
      tripartite = await this.prisma.wallet.findUnique({
        where: { userId: userOne.id },
      });
    }
    if (!tripartite) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Non-three-party login users cannot be bind',
      });
    }
    //检查验证码
    const newCheckData: MagicLinkLoginDto = {
      email: clonedPayload.email,
      token: clonedPayload.token,
    };
    await this.authService.magicLoginValidate(newCheckData, false);

    await this.prisma.user.update({
      where: { id },
      data: {
        email: clonedPayload.email,
      },
    });
    return this.findById(id);
  }

  async relieveEmail(id: string, bindEmailDto: BindEmailDto) {
    const clonedPayload = { ...bindEmailDto };
    const userOne = await this.prisma.user.findUnique({ where: { id } });
    if (!userOne) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'User Not Found',
      });
    }
    if (!userOne.email) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'User has not Email',
      });
    }
    // 判定-三方登录
    let tripartiteName = '';
    if (userOne.isWallet == false) {
      // 查询google登录
      const tripartiteGoogle = await this.prisma.google.findUnique({
        where: { userId: userOne.id },
      });
      tripartiteName = tripartiteGoogle ? tripartiteGoogle.email : '';
    } else {
      const tripartiteWallet = await this.prisma.wallet.findUnique({
        where: { userId: userOne.id },
      });
      tripartiteName = tripartiteWallet ? tripartiteWallet.address : '';
    }
    if (!tripartiteName) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Non-three-party login users cannot be unbind',
      });
    }

    //检查验证码
    const newCheckData: MagicLinkLoginDto = {
      email: clonedPayload.email,
      token: clonedPayload.token,
    };
    await this.authService.magicLoginValidate(newCheckData, false);

    await this.prisma.user.update({
      where: { id },
      data: {
        email: tripartiteName,
      },
    });
    return this.findById(id);
  }

  remove(id: string) {
    return this.prisma.user.delete({ where: { id } });
  }

  async bindWallet(id: string, bindWallet: CryptoSignDto) {
    const clonedPayload = { ...bindWallet };
    const signResult = await this.cryptoService.verifySign(bindWallet);
    if (!signResult) {
      throw new BadRequestException('VerifySign False');
    }
    // 检查钱包是否被人绑定过
    const userOld = await this.prisma.wallet.findUnique({
      where: { address: clonedPayload.ckbAddress },
    });
    if (userOld && userOld.userId) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'Wallet has bind to another user',
      });
    }

    const userOne = await this.prisma.user.findUnique({
      where: { id },
    });
    // 钱包用户才有绑定邮箱
    if (!userOne) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'User Not Found',
      });
    }
    // 判定用户是否绑定了钱包
    if (userOne.isWallet) {
      throw new BadRequestException({
        status: HttpStatus.BAD_REQUEST,
        message: 'User already has a connected wallet',
      });
    }
    // 绑定钱包
    await this.prisma.$transaction(async (tx) => {
      await tx.user.update({
        where: { id },
        data: {
          isWallet: true,
        },
      });
      await tx.wallet.update({
        where: {
          address: clonedPayload.ckbAddress,
        },
        data: {
          userId: id,
          internalAddress: clonedPayload.publicKey ?? undefined,
        },
      });
    });
    return this.findById(id);
  }
}
