import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Token, TokenType, User } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { StringService } from 'src/common/utils/string.service';

@Injectable()
export class TokenService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
    private readonly stringService: StringService,
  ) {}

  async getToken(user: User, token: string): Promise<Token> {
    return this.prisma.token.findUnique({
      where: {
        token_userId: {
          token,
          userId: user.id,
        },
      },
    });
  }

  async createRefreshTokenForUser(user: User): Promise<string> {
    const refreshToken = this.stringService.random(40);
    const date = new Date();
    date.setDate(
      date.getDate() + this.configService.get<number>('REFRESH_TOKEN_TTL'),
    );

    await this.prisma.token.create({
      data: {
        token: refreshToken,
        expiryTime: date,
        type: TokenType.REFRESH_TOKEN,
        userId: user.id,
      },
    });

    return refreshToken;
  }
}
