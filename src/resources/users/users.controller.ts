import {
  GetObjectCommand,
  ObjectCannedACL,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Req,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { Role } from '@prisma/client';
import { Public } from 'src/iam/auth/decorators/public.decorators';
import { Roles } from 'src/iam/auth/decorators/roles.decorators';
import { CryptoSignDto } from '../crypto/dto/crypto-sign.dto';
import { CreateUserDto } from './dtos/create-user.dto';
import { S3Dto } from './dtos/s3-dto';
import {
  BindEmailDto,
  ChangeEmailDto,
  UpdateUserDto,
} from './dtos/update-user.dto';
import { UserEntity } from './entities/user.entity';
import { UsersService } from './users.service';
// import { CryptoSignDto } from '../crypto/dto/crypto-sign.dto';

@Controller('users')
@ApiTags('users')
export class UsersController {
  private s3BucketName: string;
  private s3Region: string;
  private s3AccessKeyId: string;
  private s3SecretAccessKey: string;
  private s3Client: S3Client;
  constructor(
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
  ) {
    this.s3BucketName = this.configService.get<string>('S3_BUCKET_NAME');
    this.s3Region = this.configService.get<string>('S3_REGION');
    this.s3AccessKeyId = this.configService.get<string>('S3_ACCESS_KEY_ID');
    this.s3SecretAccessKey = this.configService.get<string>(
      'S3_SECRET_ACCESS_KEY',
    );

    this.s3Client = new S3Client({
      region: this.s3Region,
      bucketEndpoint: true,
      forcePathStyle: true,
      credentials: {
        accessKeyId: this.s3AccessKeyId,
        secretAccessKey: this.s3SecretAccessKey,
      },
    });
  }

  @Post()
  @ApiOperation({
    summary: '根据邮箱创建用户',
    description: '创建一个新用户,如果你邀请用户,请带来inviteCode',
  })
  @ApiCreatedResponse({ type: UserEntity })
  async create(@Body() createUserDto: CreateUserDto) {
    return new UserEntity(await this.usersService.create(createUserDto));
  }

  @Get('me')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '获取当前用户配置文件',
    description: '获取当前登录用户配置文件',
  })
  @ApiOkResponse({ type: UserEntity })
  async profile(@Req() req) {
    return new UserEntity(await this.usersService.findById(req.user.sub));
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '获取User',
    description: '获取用户根据ID',
  })
  @ApiOkResponse({ type: UserEntity })
  async findOne(@Req() req, @Param('id') id: string) {
    if (!req.user.sub || req.user.sub !== id) {
      throw new BadRequestException('Invalid uid');
    }
    return new UserEntity(await this.usersService.findById(id));
  }

  @Patch(':id')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '更新User',
    description: '根据UserID更新User',
  })
  @ApiOkResponse({ type: UserEntity })
  async update(
    @Req() req,
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    if (!req.user.sub || req.user.sub !== id) {
      throw new BadRequestException('Invalid uid');
    }
    return new UserEntity(await this.usersService.update(id, updateUserDto));
  }

  @Delete(':id')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '删除user',
    description: '根据UserID删除user',
  })
  @ApiOkResponse({ type: UserEntity })
  @Roles(Role.ADMIN)
  async remove(@Req() req, @Param('id') id: string) {
    if (!req.user.sub || req.user.sub !== id) {
      throw new BadRequestException('Invalid uid');
    }
    return new UserEntity(await this.usersService.remove(id));
  }

  @Patch('change_email/:id')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '换绑邮箱',
    description: '根据UserID改变user邮箱',
  })
  @ApiOkResponse({ type: UserEntity })
  async changeEmail(
    @Req() req,
    @Param('id') id: string,
    @Body() bindEmail: ChangeEmailDto,
  ) {
    if (!req.user.sub || req.user.sub !== id) {
      throw new BadRequestException('Invalid uid');
    }
    return new UserEntity(await this.usersService.changeEmail(id, bindEmail));
  }

  @Patch('bind_email/:id')
  @Public()
  @ApiBearerAuth()
  @ApiOperation({
    summary: '绑定邮箱',
    description: '根据UserID绑定新邮箱 <br/> 只限三方登录',
  })
  @ApiOkResponse({ type: UserEntity })
  async bindEmail(
    @Req() req,
    @Param('id') id: string,
    @Body() bindEmail: BindEmailDto,
  ) {
    if (!req.user.sub || req.user.sub !== id) {
      throw new BadRequestException('Invalid uid');
    }
    return new UserEntity(await this.usersService.bindEmail(id, bindEmail));
  }

  @Patch('relieve_email/:id')
  @ApiBearerAuth()
  @Public()
  @ApiOperation({
    summary: '解绑邮箱',
    description: '根据UserID解绑邮箱 <br/> 只限三方登录',
  })
  @ApiOkResponse({ type: UserEntity })
  async relieveEmail(
    @Req() req,
    @Param('id') id: string,
    @Body() bindEmail: BindEmailDto,
  ) {
    if (!req.user.sub || req.user.sub !== id) {
      throw new BadRequestException('Invalid uid');
    }
    return new UserEntity(await this.usersService.relieveEmail(id, bindEmail));
  }

  @Patch('bind_wallet/:id')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '绑定钱包',
    description: '根据UserID绑定钱包 <br/> 只限google及邮箱用户',
  })
  @ApiOkResponse({ type: UserEntity })
  async bindWallet(
    @Req() req,
    @Param('id') id: string,
    @Body() bindWallet: CryptoSignDto,
  ) {
    if (req.user.sub !== id) {
      throw new BadRequestException('Invalid uid');
    }
    return new UserEntity(await this.usersService.bindWallet(id, bindWallet));
  }

  @Post('s3/presigned-url')
  @ApiBearerAuth()
  @ApiOperation({
    summary: '获取S3预签名URL',
    description: '生成一个S3预签名URL用于上传或下载',
  })
  async getPresignedUrl(@Body() s3Dto: S3Dto) {
    const command =
      s3Dto.operation === 'putObject'
        ? new PutObjectCommand({
            Bucket: this.s3BucketName,
            Key: s3Dto.key,
            ACL: s3Dto.acl as ObjectCannedACL,
          })
        : new GetObjectCommand({
            Bucket: this.s3BucketName,
            Key: s3Dto.key,
          });

    const url = await getSignedUrl(this.s3Client, command, {
      expiresIn: 2 * 60 * 60,
    });

    return { url };
  }
}
