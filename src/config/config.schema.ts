import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),
  HOST: Joi.string().default('127.0.0.1'),
  PORT: Joi.number().default(3000),
  BASE_URL: Joi.string().default('http://localhost:3000'),
  DATABASE_URL: Joi.string().required(),
  API_VERSION: Joi.string().default('0.0.1').description('API Version'),
  PROJECT_TITLE: Joi.string().default('API'),
  PROJECT_DESCRIPTION: Joi.string().default(''),
  JWT_SECRET: Joi.string(),
  JWT_EXPIRES_IN: Joi.string().default('2h'),
  REFRESH_TOKEN_TTL: Joi.number().default(3600),
  GOOGLE_CLIENT_ID: Joi.string(),
  GOOGLE_CLIENT_SECRET: Joi.string(),
  GOOGLE_REDIRECT_URL: Joi.string(),
  TWITTER_CLIENT_SECRET: Joi.string(),
  TWITTER_REDIRECT_URL: Joi.string(),
  IS_TASK_ENABLED: Joi.boolean().default(false),
  LEVEL_ICON_CONFIG: Joi.string(),
  S3_BUCKET_NAME: Joi.string().optional(),
  S3_REGION: Joi.string(),
  S3_ACCESS_KEY_ID: Joi.string(),
  S3_SECRET_ACCESS_KEY: Joi.string(),
  S3_CUSTOM_ENDPOINT: Joi.string().optional(),
  // Email configuration
  MAIL_HOST: Joi.string(),
  MAIL_PORT: Joi.number(),
  MAIL_IGNORETLS: Joi.boolean(),
  MAIL_SECURE: Joi.boolean(),
  MAIL_REQUIRETLS: Joi.boolean(),
  MAIL_USER: Joi.string(),
  MAIL_PASSWORD: Joi.string(),
  MAIL_DEFAULT_NAME: Joi.string(),
  MAIL_DEFAULT_EMAIL: Joi.string(),
});
