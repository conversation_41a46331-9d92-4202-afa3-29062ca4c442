export const Configuration = () => ({
  HOST: process.env.HOST,
  PORT: parseInt(process.env.PORT, 10),
  BASE_URL: process.env.BASE_URL,
  ENVIRONMENT: process.env.NODE_ENV,
  API_VERSION: process.env.API_VERSION,
  PROJECT_TITLE: process.env.PROJECT_TITLE,
  PROJECT_DESCRIPTION: process.env.PROJECT_DESCRIPTION,
  JWT_SECRET: process.env.JWT_SECRET,
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN,
  REFRESH_TOKEN_TTL: process.env.REFRESH_TOKEN_TTL,
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
  GOOGLE_REDIRECT_URL: process.env.GOOGLE_REDIRECT_URL,
  TWITTER_CLIENT_SECRET: process.env.TWITTER_CLIENT_SECRET,
  TWITTER_REDIRECT_URL: process.env.TWITTER_REDIRECT_URL,
  SECP256K1_PRIVATE_KEY: process.env.SECP256K1_PRIVATE_KEY,
  BLOCK_CHAIN_NETWORK: process.env.BLOCK_CHAIN_NETWORK,
  DOB_SDK_URL: process.env.DOB_SDK_URL,
  OFFICIAL_ADDRESS_ETH: process.env.OFFICIAL_ADDRESS_ETH,
  OFFICIAL_ADDRESS_BTC: process.env.OFFICIAL_ADDRESS_BTC,
  OFFICIAL_ADDRESS_CKB: process.env.OFFICIAL_ADDRESS_CKB,
  ETH_RPC: process.env.ETH_RPC,
  ETH_RPC_TEST_NET: process.env.ETH_RPC_TEST_NET,
  BTC_RPC: process.env.BTC_RPC,
  BTC_RPC_TEST_NET: process.env.BTC_RPC_TEST_NET,
  BTC_FEE_RPC: process.env.BTC_FEE_RPC,
  AWS_REGION: process.env.AWS_REGION,
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
  AWS_BUCKET: process.env.AWS_BUCKET,
  EARNINGS_CONFIG: process.env.EARNINGS_CONFIG,
  LEVEL_ICON_CONFIG: process.env.LEVEL_ICON_CONFIG,
  AUDIT_DAYS: process.env.AUDIT_DAYS,
  S3_BUCKET_NAME: process.env.S3_BUCKET_NAME,
  S3_REGION: process.env.S3_REGION,
  S3_ACCESS_KEY_ID: process.env.S3_ACCESS_KEY_ID,
  S3_SECRET_ACCESS_KEY: process.env.S3_SECRET_ACCESS_KEY,
  IS_TASK_ENABLED: process.env.IS_TASK_ENABLED,
});
