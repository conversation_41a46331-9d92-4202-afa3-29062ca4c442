import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { CryptoService } from 'src/resources/crypto/crypto.service';

@Injectable()
export class getBinanceApiTaskService {
  private readonly logger = new Logger(getBinanceApiTaskService.name);
  private is_task_enabled: boolean;
  constructor(
    private cryptoService: CryptoService,
    private configService: ConfigService,
  ) {
    this.is_task_enabled = configService.get<boolean>('IS_TASK_ENABLED');
  }

  // binance-api-price
  @Cron(CronExpression.EVERY_10_SECONDS, { name: 'binance-api-price' })
  async handleCron() {
    if (this.is_task_enabled === false) {
      this.logger.log('getBinanceApiTask is disabled');
      return;
    }
    try {
      console.log('getBinanceApi');
      await this.cryptoService.getBinanceApi();
    } catch (error) {
      this.logger.debug('binance api price:', error);
    }
  }
}
