import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrintOrderStatus } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';
import { LuluService } from 'src/resources/lulu/lulu.service';

@Injectable()
export class CreatePrintJobTaskService {
  private logger = new Logger(CreatePrintJobTaskService.name);
  private is_task_enabled;
  constructor(
    private prisma: PrismaService,
    private luluService: LuluService,
    private configService: ConfigService,
  ) {
    this.is_task_enabled = configService.get<boolean>('IS_TASK_ENABLED');
  }

  /**
   * Create print job for paid orders with lulu api every 5 minutes
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleCron() {
    if (!this.is_task_enabled) {
      this.logger.log('CreatePrintJobTask is disabled');
      return;
    }
    const paidPrintOrders = await this.prisma.printOrder.findMany({
      where: {
        status: PrintOrderStatus.PAID,
      },
      include: {
        book: {
          include: {
            specification: true,
          },
        },
      },
      take: 10,
    });
    for (const order of paidPrintOrders) {
      try {
        const _res = await this.luluService.createPrintJob({
          contact_email: order.email,
          external_id: order.id,
          line_items: [
            {
              printable_normalization: {
                cover: {
                  source_url: order.book.specification.coverUrl,
                },
                interior: {
                  source_url: order.book.specification.sourceUrl,
                },
                pod_package_id: order.book.specification.podPackageId,
              },
              quantity: order.quantity,
              title: order.book.title,
            },
          ],
          production_delay: 1200,
          shipping_address: {
            city: order.city,
            country_code: order.countryCode,
            name: order.name,
            phone_number: order.phone,
            postcode: order.postcode,
            state_code: order.stateCode,
            street1: order.street1,
            street2: order.street2,
          },
          shipping_level: order.shippingLevel,
        });
        if (
          _res &&
          _res['line_items'] &&
          _res['line_items'][0]['status']['name'] == 'CREATED'
        ) {
          this.logger.log('Created Print Order: ', order.id);
          this.prisma.printOrder.update({
            where: { id: order.id },
            data: {
              status: PrintOrderStatus.COMPLETE,
            },
          });
        }
      } catch (e) {
        this.logger.log('Created an Print Order Error: ', e);
        this.prisma.printOrder.update({
          where: { id: order.id },
          data: {
            status: PrintOrderStatus.CANCEL,
          },
        });
      }
    }
  }
}
