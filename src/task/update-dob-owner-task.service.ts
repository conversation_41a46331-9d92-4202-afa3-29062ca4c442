import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'nestjs-prisma';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { DnaLevel, Prisma } from '@prisma/client';

//遍历所有collections>获取所有nft信息>对比dobs>入库|更新dobs
@Injectable()
export class UpdateDobOwnerTaskTaskService {
  private readonly logger = new Logger(UpdateDobOwnerTaskTaskService.name);
  private is_task_enabled: boolean;
  private isLocked = false;
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {
    this.is_task_enabled = configService.get<boolean>('IS_TASK_ENABLED');
  }
  //order for hash
  @Cron(CronExpression.EVERY_5_MINUTES, { name: 'update-dob-owner' })
  async handleCron() {
    if (!this.is_task_enabled || this.isLocked) {
      this.logger.log('CheckDobOrderTask is disabled');
      return;
    }
    this.isLocked = true;
    try {
      const host = 'https://mainnet-api.explorer.nervos.org';
      // 平台地址
      const platformAddr =
        'ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d';
      const mintsAddr =
        'ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqg89znuqg5adz8tu5azgw7zrhusnza3wrs6kmfrz';
      // clusterID
      const clusterID = 'f18601a6-fc2b-4308-b917-5ef9a0d9c888';
      // 错误次数
      let errorCount = 0;
      // 首页url
      let url =
        '/api/v2/nft/collections/0x52e5418248a2e3125f411670edb8633acc9a4e0d680941270c42d6fb89ef8002/items?page=1&page_size=20';

      // 循环page
      while (true) {
        // 如果网络错误太多次--就进行下一次 本次放弃同步
        if (errorCount >= 50) {
          console.log('网络请求失败太多次--退出');
          break;
        }
        try {
          const response = await axios.get(host + url);
          if (response.status == 200) {
            const res = response.data;
            const data = res.data;
            const pagination = res.pagination;
            if (!res || !data || !pagination) {
              // 数据值不对 也算错误
              console.log('数据值不对-错误次数+1');
              errorCount++;
            }
            //取下一页值
            if (pagination['next'] != null && pagination['next_url']) {
              url = pagination['next_url'];
              console.log('next_url', host + url);
            } else {
              url = '';
            }
            for (const item of data) {
              const dobID = item['type_script']['args'];
              const ckbAddress = item['owner'];
              // 如果是平台地址不做处理
              if (ckbAddress == mintsAddr) continue;
              if (!ckbAddress || !dobID) continue;

              const wallet = await this.prisma.wallet.findUnique({
                where: { address: ckbAddress },
              });
              let uid: string;
              const min = Math.pow(10, 4) - 1;
              const max = Math.pow(10, 5) - 1;
              const randomNumber =
                Math.floor(Math.random() * (max - min + 1)) + min;
              const isAdmin = ckbAddress == platformAddr ? true : false;
              // 全部放在一个事务中来
              await this.prisma.$transaction(async (tx) => {
                // 根据地址找用户
                if (wallet) {
                  if (!wallet.userId) {
                    //新增用户表
                    const user = await tx.user.upsert({
                      where: {
                        email: ckbAddress,
                      },
                      update: {
                        isWallet: true,
                      },
                      create: {
                        name: 'silentberry' + randomNumber,
                        email: ckbAddress,
                        isWallet: true,
                      },
                    });
                    await tx.wallet.update({
                      where: { address: ckbAddress },
                      data: { userId: user.id },
                    });
                    uid = user.id;
                  } else {
                    uid = wallet.userId;
                  }
                } else {
                  //新增用户和钱包
                  const user = await tx.user.upsert({
                    where: {
                      email: ckbAddress,
                    },
                    update: {
                      isWallet: true,
                    },
                    create: {
                      name: 'silentberry' + randomNumber,
                      email: ckbAddress,
                      isWallet: true,
                    },
                  });
                  await tx.wallet.create({
                    data: {
                      address: ckbAddress,
                      userId: user.id,
                    },
                  });
                  uid = user.id;
                }
                // 再根据spore-id找dobs
                const dobData = await tx.dob.findUnique({
                  where: { dobId: dobID },
                });
                if (!dobData) {
                  // 没有的情况下 写入dobs
                  const spore: Prisma.DobCreateInput = {
                    dobId: dobID,
                    ownerAddress: ckbAddress,
                    isAdmin: false,
                    cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60, //1 hour
                    utxoData: 'for-update',
                    dna: 'for-update',
                    level: DnaLevel.COPPER,
                    metadata: 'for-update',
                    txHash: 'for-update',
                    withdrawn: 0,
                    cluster: {
                      connect: {
                        id: clusterID,
                      },
                    },
                    user: {
                      connect: {
                        id: uid,
                      },
                    },
                  };
                  await tx.dob.create({
                    data: spore,
                  });
                } else {
                  // 存在dob的情况 就是更新所属
                  await tx.dob.update({
                    where: { id: dobData.id },
                    data: {
                      ownerAddress: ckbAddress,
                      userId: isAdmin ? undefined : uid,
                      isAdmin: isAdmin,
                    },
                  });
                }
              });
            }
            console.log(
              `同步结束-页码：${pagination['page']} 条数：${data.length}`,
            );
          } else {
            console.log('网络请求错误-错误次数+1', response.status);
            errorCount++;
          }

          // 如果没有下一页 --本次同步结束
          if (!url) {
            console.log('本次执行成功-全量同步结束');
            break;
          }
        } catch (error) {
          console.log('网络请求错误-错误次数+1', error);
          errorCount++;
        }
      }
    } catch (error) {
      this.logger.debug('order-hash Cron Error:', error);
    } finally {
      this.isLocked = false;
    }
  }
}
