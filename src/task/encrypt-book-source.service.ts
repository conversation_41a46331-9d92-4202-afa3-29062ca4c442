import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as AsposePdf from 'asposepdfnodejs';
import axios from 'axios';
import * as fs from 'fs';
import { PrismaService } from 'nestjs-prisma';
import * as path from 'path';
import { AwsS3Service } from 'src/common/utils/awsS3.service';
import { StringService } from 'src/common/utils/string.service';
@Injectable()
export class EncryptBookSourceTaskService {
  private logger = new Logger(EncryptBookSourceTaskService.name);
  //   private asposePdfModule: any;
  private is_task_enabled;
  private static isInitialized = false; // 添加静态标志

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private awsS3Service: AwsS3Service,
    private stringService: StringService,
  ) {
    // 防止重复初始化
    if (EncryptBookSourceTaskService.isInitialized) {
      this.logger.warn('Service instance already initialized, skipping...');
      return;
    }
    EncryptBookSourceTaskService.isInitialized = true;

    this.is_task_enabled = configService.get<boolean>('IS_TASK_ENABLED');
    this.logger.log('Service instance initialized');
  }

  async executeTask() {
    this.logger.log('Manual task execution started');
    return await this.encryptBookSource();
  }

  /**
   * Create print job for paid orders with lulu api every 5 minutes
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleCron() {
    if (!this.is_task_enabled) {
      this.logger.log('EncryptBookSourceTask is disabled');
      return;
    }
    this.logger.log('Cron EncryptBookSourceTask is started');
    await this.encryptBookSource();
  }

  async encryptBookSource() {
    this.logger.log('Starting encryptBookSource process');
    try {
      // 1. 获取所有需要处理的clusters
      const clusters = await this.prisma.cluster.findMany({
        where: {
          book: {
            NOT: {
              specification: null,
            },
          },
        },
        select: {
          id: true,
          book: {
            select: {
              specification: {
                select: {
                  sourceUrl: true,
                },
              },
            },
          },
        },
      });

      this.logger.log(`Found ${clusters.length} clusters`);

      // 2. 串行处理每个cluster
      for (const cluster of clusters) {
        // 3. 获取需要处理的dobs
        const dobs = await this.prisma.dob.findMany({
          where: {
            clusterId: cluster.id,
            encryptedBookSource: null,
          },
          take: 5,
        });

        this.logger.log(`Found ${dobs.length} dobs`);
        if (dobs.length === 0) {
          this.logger.log(`No dobs found for cluster ${cluster.id}`);
          continue;
        }

        // 4. 下载源文件
        const sourceUrl = cluster.book.specification.sourceUrl;
        const tempFilePath = await this.downloadSourcePdf(sourceUrl);

        // 5. 串行处理每个dob
        for (const dob of dobs) {
          try {
            this.logger.log(`Processing DOB book source: ${dob.dobId}`);

            // 6. 生成密码
            const adminPassword = this.stringService.random(20);
            const userPassword = this.stringService.random(16);

            // 7. 加密PDF
            const tempDir = '/tmp/book-sources';
            const encryptedPdf = await this.encryptPdf(
              tempFilePath,
              adminPassword,
              userPassword,
              `${tempDir}/${dob.dobId}.pdf`,
            );

            // 8. 上传加密后的PDF
            const key = await this.uploadEncryptedPdf(
              `${dob.dobId}.pdf`,
              encryptedPdf,
            );

            // 9. 创建数据库记录
            if (key) {
              await this.prisma.encryptedBookSource.create({
                data: {
                  dobId: dob.dobId,
                  sourceUrl: key,
                  adminPassword: adminPassword,
                  userPassword: userPassword,
                },
              });
              this.logger.log(`Successfully processed DOB: ${dob.dobId}`);
            }
          } catch (error) {
            this.logger.error(
              `Error processing DOB ${dob.dobId}: ${error.message}`,
            );
            // 继续处理下一个dob
            continue;
          }
        }

        // 10. 清理临时文件
        try {
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
            this.logger.log(`Cleaned up temp file: ${tempFilePath}`);
          }
        } catch (error) {
          this.logger.error(`Error cleaning up temp file: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error in handleCron: ${error.message}`);
    }
  }

  async downloadSourcePdf(sourceUrl: string) {
    const tempDir = '/tmp/book-sources';

    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const fileName = `${Buffer.from(sourceUrl).toString('base64')}.pdf`;
    const tempFilePath = path.join(tempDir, fileName);

    if (fs.existsSync(tempFilePath)) {
      this.logger.log(`File already exists: ${tempFilePath}`);
      return tempFilePath;
    }

    const response = await axios.get(sourceUrl, {
      responseType: 'arraybuffer',
    });

    fs.writeFileSync(tempFilePath, response.data);
    this.logger.log(`File downloaded: ${tempFilePath}`);

    return tempFilePath;
  }

  async uploadEncryptedPdf(dobId: string, encryptedPdf: string) {
    this.logger.log(`Uploading encrypted PDF: ${encryptedPdf}`);
    try {
      const key = `book-source/${dobId}`;
      // 使用 createReadStream 替代 readFileSync，避免将整个文件加载到内存
      const fileStream = fs.createReadStream(encryptedPdf);
      const result = await this.awsS3Service.putObject(null, key, fileStream);

      if (result) {
        this.logger.log(`Encrypted PDF uploaded: ${key}`);
        // 确保在删除文件前，文件流已经关闭
        await fs.promises.unlink(encryptedPdf);
        this.logger.log(`Deleted encrypted PDF: ${encryptedPdf}`);
        return key;
      }
      return null;
    } catch (error) {
      this.logger.error(`Error in uploadEncryptedPdf: ${error.message}`);
      throw error;
    }
  }

  async encryptPdf(
    pdf_file: string,
    adminPassword: string,
    userPassword: string,
    output_file_path: string,
  ) {
    this.logger.log(`Encrypting PDF: ${pdf_file}`);
    const AsposePdfModule = await AsposePdf();
    /*使用密码 "user" 和 "owner" 加密 PDF 文件，并保存为 "ResultEncrypt.pdf"*/
    const json = await AsposePdfModule.AsposePdfEncrypt(
      pdf_file,
      userPassword,
      adminPassword,
      AsposePdfModule.Permissions.PrintDocument |
        AsposePdfModule.Permissions.ModifyContent |
        AsposePdfModule.Permissions.ModifyAnnotations |
        AsposePdfModule.Permissions.FillInForm |
        AsposePdfModule.Permissions.CopyContent |
        AsposePdfModule.Permissions.ExtractContent,
      AsposePdfModule.CryptoAlgorithm.RC4x40,
      output_file_path,
    );
    console.log(
      'AsposePdfEncrypt => %O',
      json.errorCode == 0 ? json.fileNameResult : json.errorText,
    );
    this.logger.log(`Encrypted PDF: ${output_file_path}`);
    return output_file_path;
  }
}
