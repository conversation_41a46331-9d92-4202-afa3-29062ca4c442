import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'nestjs-prisma';
import { CryptoService } from '../resources/crypto/crypto.service';
// import { helpers } from '@ckb-lumos/lumos';
// import { addressToScript } from '@nervosnetwork/ckb-sdk-utils';
import { ConfigService } from '@nestjs/config';
import { ccc } from '@ckb-ccc/core';
import { createSpore } from '@ckb-ccc/spore';
import {
  EarningsType,
  PaymentStatus,
  PrintOrderStatus,
  Prisma,
} from '@prisma/client';
// import {
//   // bytifyRawString,
//   // createSpore,
//   // predefinedSporeConfigs,
//   // createMultipleSpores,
// } from '@spore-sdk/core';
import { Secp256k1WalletService } from 'src/common/utils/secp256k1.service';
import { DobsService } from 'src/resources/dobs/dobs.service';

//BTC支付 异步扫描支付订单 目前eth 同步获取交易hash 不需要异步扫描
@Injectable()
export class CheckDobOrderTaskService {
  private readonly logger = new Logger(CheckDobOrderTaskService.name);
  private config_network;
  private collection_address;
  private config_secp256k1_key;
  private chain_network;
  private is_task_enabled: boolean;
  private isLocked = false;
  constructor(
    private prisma: PrismaService,
    private cryptoService: CryptoService,
    private configService: ConfigService,
    private dobsService: DobsService,
    private secp256k1Wallet: Secp256k1WalletService,
  ) {
    this.chain_network = configService.get<string>('BLOCK_CHAIN_NETWORK');
    this.is_task_enabled = configService.get<boolean>('IS_TASK_ENABLED');
    this.config_network =
      configService.get<string>('BLOCK_CHAIN_NETWORK') == 'Mainnet'
        ? new ccc.ClientPublicMainnet()
        : new ccc.ClientPublicTestnet();

    this.config_secp256k1_key = configService.get<string>(
      'SECP256K1_PRIVATE_KEY',
    );
    this.collection_address = {
      eth: configService.get<string>('OFFICIAL_ADDRESS_ETH'),
      btc: configService.get<string>('OFFICIAL_ADDRESS_BTC'),
      ckb: configService.get<string>('OFFICIAL_ADDRESS_CKB'),
    };
  }
  //order for hash
  @Cron(CronExpression.EVERY_MINUTE, { name: 'check-order-hash' })
  async handleHashOrderCron() {
    if (!this.is_task_enabled || this.isLocked) {
      this.logger.log('CheckDobOrderTask is disabled');
      return;
    }
    this.isLocked = true;
    try {
      const order_data = await this.prisma.dobOrder.findMany({
        where: {
          status: PaymentStatus.PAID,
          // createdAt: { gte: new Date('2025-01-03T13:30:00+08:00') },
          level: 'COPPER',
        },
        include: { cluster: true },
        take: 10,
      });
      const previousHour = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);
      console.log('check-order-hash', order_data.length);

      for (const order_one of order_data) {
        //判定是否过期
        if (order_one.createdAt < previousHour) {
          // 订单超时-自动失败
          await this.prisma.dobOrder.updateMany({
            where: {
              id: order_one.id,
            },
            data: {
              status: PaymentStatus.FAILED,
            },
          });
          await this.dobsService.callBackDobOrder(order_one.id);
        } else {
          if (order_one.txhash && order_one.payUserId && order_one.network) {
            console.log(order_one.id, 'orderID');
            // check transfer hash
            const txData = await this.cryptoService.findTransferByHash(
              order_one.network,
              order_one.txhash,
            );
            // 如果是rpc异常
            if (txData.error) {
              // rpc异常 - 失败
              await this.prisma.dobOrder.update({
                where: {
                  id: order_one.id,
                },
                data: {
                  // 使用CANCELED表示异常 取消验证
                  status: PaymentStatus.CANCELED,
                },
              });
              await this.dobsService.callBackDobOrder(order_one.id);
            }
            // 验证成功后
            console.log(txData, 'txData');
            let cryptoPrice: string;
            if (order_one.network == 'ETH') {
              // eth 18位太长了 数据库存不下 会失败
              txData.value =
                txData.value.length > 15
                  ? txData.value.slice(0, 15)
                  : txData.value;
              cryptoPrice =
                order_one.cryptoPrice.toString().length > 15
                  ? order_one.cryptoPrice.toString().slice(0, 15)
                  : order_one.cryptoPrice.toString();
            } else {
              cryptoPrice = order_one.cryptoPrice.toString();
            }
            if (txData.isSuccess && txData.value == cryptoPrice) {
              const payUser = await this.prisma.wallet.findUnique({
                where: { userId: order_one.payUserId },
              });

              let userLock: any;
              if (payUser && payUser.address) {
                userLock = (
                  await ccc.Address.fromString(
                    payUser.address,
                    this.config_network,
                  )
                ).script;
              }
              const wallet = new ccc.SignerCkbPrivateKey(
                this.config_network,
                this.config_secp256k1_key,
              );
              const walletObj = await wallet.getRecommendedAddressObj();
              let toLock: any, isAdmin: boolean, ownerAddress: string;
              if (!userLock) {
                toLock = walletObj.script;
                isAdmin = true;
                ownerAddress = walletObj.toString();
              } else {
                toLock = userLock;
                isAdmin = false;
                ownerAddress = payUser.address;
              }
              const { tx, id: sporeId } = await createSpore({
                signer: wallet,
                to: toLock,
                clusterMode: 'lockProxy',

                data: {
                  contentType: 'dob/1',
                  content: ccc.bytesFrom(
                    JSON.stringify({ dna: order_one.dna }),
                    'utf8',
                  ),
                  clusterId: order_one.cluster.clusterId,
                },
              });
              await tx.completeFeeBy(wallet);
              const signedTx = await wallet.signTransaction(tx);
              const txHash = signedTx.hash();
              console.log(signedTx.stringify(), txHash, sporeId, 'hashData');
              for (let i = 1; i <= order_one.amount; i++) {
                // 创建spore
                const spore: Prisma.DobCreateInput = {
                  dobId: sporeId,
                  ownerAddress: ownerAddress,
                  isAdmin: isAdmin,
                  cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60, //1 hour
                  utxoData: 'null',
                  dna: order_one.dna,
                  level: order_one.level,
                  metadata: order_one.dnaData,
                  txHash: txHash,
                  withdrawn: 0,
                  cluster: {
                    connect: {
                      id: order_one.clusterId,
                    },
                  },
                  user: {
                    connect: {
                      id: order_one.payUserId,
                    },
                  },
                  dobOrder: {
                    connect: {
                      id: order_one.id,
                    },
                  },
                };

                await this.prisma.$transaction(async (tx) => {
                  const dob = await tx.dob.create({ data: spore });
                  // 更新spore 订单
                  await tx.dobOrder.update({
                    where: { id: order_one.id },
                    data: {
                      status: PaymentStatus.DONE,
                      earningsType: EarningsType.PROCESSING,
                      dob: { connect: { id: dob.id } },
                    },
                  });
                  return dob;
                });
              }
              // 更新book 销量
              await this.prisma.book.update({
                where: { id: order_one.cluster.bookId },
                data: {
                  sales: {
                    increment: order_one.amount,
                  },
                },
              });
              console.log('database success');
              //最后发起交易
              await wallet.sendTransaction(tx);
              console.log('sendTransaction(tx) success');
            } else if (
              txData.isSuccess &&
              txData.value != order_one.price.toString()
            ) {
              // 金额不对 失败
              await this.prisma.dobOrder.update({
                where: {
                  id: order_one.id,
                },
                data: {
                  status: PaymentStatus.FAILED,
                },
              });
              await this.dobsService.callBackDobOrder(order_one.id);
            } else if (!txData.isSuccess && txData.value && txData.from) {
              // 支付状态不对 失败
              await this.prisma.dobOrder.update({
                where: {
                  id: order_one.id,
                },
                data: {
                  status: PaymentStatus.FAILED,
                },
              });
              await this.dobsService.callBackDobOrder(order_one.id);
            }
          }
        }
      }
    } catch (error) {
      this.logger.debug('order-hash Cron Error:', error);
    } finally {
      this.isLocked = false;
    }
  }

  // Print order check(10 min)
  @Cron(CronExpression.EVERY_5_MINUTES, { name: 'print-order-hash' })
  async handlePrintOrderHashCron() {
    if (!this.is_task_enabled) {
      this.logger.log('PrintOrderHashTask is disabled');
      return;
    }
    try {
      const order_data = await this.prisma.printOrder.findMany({
        where: {
          status: PrintOrderStatus.PENDING,
          txhash: { not: null },
          network: { not: null },
        },
      });
      const previousHour = new Date(new Date().getTime() - 60 * 60 * 1000);
      if (order_data.length > 0) {
        order_data.forEach(async (order_one) => {
          if (order_one.createdAt < previousHour) {
            // 订单超时-自动失败
            await this.prisma.printOrder.updateMany({
              where: { id: order_one.id },
              data: {
                status: PrintOrderStatus.CANCEL,
              },
            });
          } else {
            // 检查hash
            const checkRes = await this.cryptoService.findTransferByHash(
              order_one.network,
              order_one.txhash,
            );
            if (checkRes) {
              await this.prisma.printOrder.updateMany({
                where: { id: order_one.id },
                data: {
                  status: PrintOrderStatus.PAID,
                },
              });
            }
          }
        });
      }
    } catch (error) {
      this.logger.debug('spore-for-id-hour Cron Error:', error);
    }
  }

  async sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
