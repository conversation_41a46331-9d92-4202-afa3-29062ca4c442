import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'nestjs-prisma';
import { CryptoService } from '../resources/crypto/crypto.service';
import { ConfigService } from '@nestjs/config';
import { ccc } from '@ckb-ccc/core';
import { DnaLevel, EarningsType, PaymentStatus, Prisma } from '@prisma/client';
import { Secp256k1WalletService } from 'src/common/utils/secp256k1.service';
import { DobsService } from 'src/resources/dobs/dobs.service';
import axios from 'axios';

interface Transaction {
  id: string;
  type: string;
  attributes: {
    block_number: number;
    transaction_hash: string;
    income: string;
    created_at: string;
    display_inputs_count: number;
    display_inputs: Array<{ address_hash: string; capacity: string }>;
    display_outputs_count: number;
    display_outputs: Array<{
      address_hash: string;
      capacity: string;
      status: string;
      cell_type: string;
    }>;
  };
}

interface TransactionResponse {
  data: Transaction[];
  meta: {
    total: number;
    page_size: number;
    total_pages: number;
  };
}
const BASE_API_URL =
  'https://mainnet-api.explorer.nervos.org/api/v1/address_transactions/';
const PAGE_SIZE = 100;
const ADDRESS =
  'ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqtedlkpqza0g4ejye9gntxxg2jddnlax7g7wa85h';

//监听收款地址>核对订单>取到数量>从平台转移spore到支付地址
@Injectable()
export class TransferDobTaskTaskService {
  private readonly logger = new Logger(TransferDobTaskTaskService.name);
  private config_network;
  private collection_address;
  private config_secp256k1_key;
  private chain_network;
  private is_task_enabled: boolean;
  private isLocked = false;
  constructor(
    private prisma: PrismaService,
    private cryptoService: CryptoService,
    private configService: ConfigService,
    private dobsService: DobsService,
    private secp256k1Wallet: Secp256k1WalletService,
  ) {
    this.chain_network = configService.get<string>('BLOCK_CHAIN_NETWORK');
    this.is_task_enabled = configService.get<boolean>('IS_TASK_ENABLED');
    this.config_network =
      configService.get<string>('BLOCK_CHAIN_NETWORK') == 'Mainnet'
        ? new ccc.ClientPublicMainnet()
        : new ccc.ClientPublicTestnet();

    this.config_secp256k1_key = configService.get<string>(
      'SECP256K1_PRIVATE_KEY',
    );
    this.collection_address = {
      eth: configService.get<string>('OFFICIAL_ADDRESS_ETH'),
      btc: configService.get<string>('OFFICIAL_ADDRESS_BTC'),
      ckb: configService.get<string>('OFFICIAL_ADDRESS_CKB'),
    };
  }
  //order for hash
  @Cron(CronExpression.EVERY_MINUTE, { name: 'check-order-hash' })
  async handleHashOrderCron() {
    if (!this.is_task_enabled || this.isLocked) {
      this.logger.log('CheckDobOrderTask is disabled');
      return;
    }
    this.isLocked = true;
    try {
      let errorCount = 0;
      let page = 1;
      // 取这个值是为了增量监听transfer
      const oldTxHashTag = await this.prisma.golbalConfig.findFirst();
      const min = Math.pow(10, 4) - 1;
      const max = Math.pow(10, 5) - 1;
      const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
      const tipBlockNumber = await this.fetchBlockNumber();
      // 循环page
      while (true) {
        // 如果网络错误太多次--就进行下一次 本次放弃同步
        if (errorCount >= 50) {
          console.log('网络请求失败太多次--退出');
          break;
        }
        try {
          const response = await axios.get(
            `${BASE_API_URL}${ADDRESS}?page=${page}&page_size=${PAGE_SIZE}&sort=time.desc`,
            {
              headers: {
                Accept: 'application/vnd.api+json',
                'Content-Type': 'application/vnd.api+json',
              },
            },
          );
          if (response.status != 200) {
            console.log('网络请求错误-错误次数+1', response.status);
            errorCount++;
          }
          const responseData: TransactionResponse = response.data;
          if (responseData.data.length === 0) {
            console.log('当前页没有发现转账记录');
            page++;
          }

          // 循环处理
          for (const transaction of responseData.data) {
            if (transaction.type === 'ckb_transactions') {
              // const timestamp = transaction.attributes.created_at;
              const blockNumber = transaction.attributes.block_number;
              const transactionHash = transaction.attributes.transaction_hash;
              const income = transaction.attributes.income;
              const payerAddress =
                transaction.attributes.display_inputs[0].address_hash;
              let checkStatus = false;
              //增量标识
              if (blockNumber <= Number(oldTxHashTag.txHashTag)) {
                console.log(
                  '已经到了记录的区块Number',
                  blockNumber,
                  oldTxHashTag.txHashTag,
                );
                break;
              }
              if (Number(income) < 0) {
                //   console.debug("income is negative", income, transactionHash);
                return;
              }

              if (Number(blockNumber) + 24 > Number(tipBlockNumber)) {
                console.log(
                  'Waiting for block confirmation, block number:',
                  blockNumber,
                  'top block number:',
                  tipBlockNumber,
                );
                return;
              }

              transaction.attributes.display_inputs.forEach((input) => {
                if (input.address_hash !== payerAddress) {
                  console.warn(
                    'multiple input address',
                    payerAddress,
                    input.address_hash,
                  );
                  return;
                }
              });

              transaction.attributes.display_outputs.forEach((output) => {
                if (
                  output.address_hash === ADDRESS &&
                  output.cell_type === 'normal'
                ) {
                  const amount = output.capacity;
                  if (income === amount) {
                    checkStatus = true;
                    return;
                  } else {
                    console.warn(
                      'amount not match',
                      amount,
                      income,
                      transactionHash,
                    );
                  }
                }
              });
              // 成功验证后
              if (checkStatus) {
                // 记录处理的区块
                await this.prisma.golbalConfig.update({
                  where: { id: oldTxHashTag.id },
                  data: { txHashTag: blockNumber.toString() },
                });
                // 根据TxHash找寻数据库 order
                const order = await this.prisma.dobOrder.findUnique({
                  where: { txhash: transactionHash },
                });
                // 没有发现订单
                if (!order) continue;
                // 校验钱包地址
                const userWallet = await this.prisma.wallet.findUnique({
                  where: { userId: order.payUserId },
                });
                // 判定用户是否存在
                let uid;
                if (!userWallet) {
                  const user = await this.prisma.user.upsert({
                    where: {
                      email: payerAddress,
                    },
                    update: {
                      isWallet: true,
                    },
                    create: {
                      name: 'silentberry' + randomNumber,
                      email: payerAddress,
                      isWallet: true,
                    },
                  });
                  await this.prisma.wallet.create({
                    data: {
                      address: payerAddress,
                      userId: user.id,
                    },
                  });
                  uid = user.id;
                } else {
                  // 如果存在 且需要地址对应上
                  if (userWallet.address != payerAddress) {
                    console.log(
                      'txHash 的来源地址跟dob-order对应不上',
                      order.id,
                      userWallet.address,
                      payerAddress,
                    );
                    continue;
                  }
                  uid = userWallet.userId;
                }

                // TODO:需要补充 运行转移spore 的代码
                const platformDobId = await this.prisma.platformDobs.findFirst({
                  where: { status: 'UNUSED' },
                });
                const transferHash = '0xx';
                const sporeID = platformDobId.SporeId;

                // 写入数据库-dobs
                const spore: Prisma.DobCreateInput = {
                  dobId: sporeID,
                  ownerAddress: payerAddress,
                  isAdmin: false,
                  cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60, //1 hour
                  utxoData: 'null',
                  dna: order ? order.dna : 'for-tansfer-dna',
                  level: DnaLevel.COPPER,
                  metadata: order ? order.dnaData : 'for-tansfer.dnaData',
                  txHash: transferHash,
                  withdrawn: 0,
                  cluster: {
                    connect: {
                      id: order
                        ? order.clusterId
                        : 'f18601a6-fc2b-4308-b917-5ef9a0d9c888',
                    },
                  },
                  user: {
                    connect: {
                      id: uid,
                    },
                  },
                  dobOrder: {
                    connect: {
                      id: order ? order.id : undefined,
                    },
                  },
                };
                await this.prisma.$transaction(async (tx) => {
                  const dob = await tx.dob.create({ data: spore });
                  // 更新spore 订单
                  if (order) {
                    await tx.dobOrder.update({
                      where: { id: order.id },
                      data: {
                        status: PaymentStatus.DONE,
                        earningsType: EarningsType.PROCESSING,
                        dob: { connect: { id: dob.id } },
                      },
                    });
                  }

                  //更新dobs使用
                  await tx.platformDobs.update({
                    where: { id: platformDobId.id },
                    data: { status: 'USED' },
                  });
                  return dob;
                });
              }
            }
          }

          if (page < responseData.meta.total_pages) {
            console.log(`数据处理结束-页码:${page}`);
            page++;
          } else {
            console.log(`本次数据处理结束`);
            break;
          }
        } catch (error) {
          console.log('网络请求错误-错误次数+1', error);
          errorCount++;
        }
      }
    } catch (error) {
      this.logger.debug('order-hash Cron Error:', error);
    } finally {
      this.isLocked = false;
    }
  }

  async fetchBlockNumber() {
    const response = await axios.get(
      `${BASE_API_URL}statistics/tip_block_number`,
      {
        headers: {
          Accept: 'application/vnd.api+json',
          'Content-Type': 'application/vnd.api+json',
        },
      },
    );
    if (
      response.status === 200 &&
      response.data.data.attributes.tip_block_number
    ) {
      return response.data.data.attributes.tip_block_number;
    }
    return 0;
  }
}
