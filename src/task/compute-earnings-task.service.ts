import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DnaLevel, EarningsType, PaymentStatus, Prisma } from '@prisma/client';
import { PrismaService } from 'nestjs-prisma';

interface RatioConfig {
  author: number;
  platform: number;
  gold: number;
  silver: number;
  copper: number;
}

@Injectable()
export class ComputeEarningsTaskService {
  private readonly logger = new Logger(ComputeEarningsTaskService.name);
  private earnings_config;
  private is_task_enabled: boolean;
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {
    this.earnings_config = configService.get<string>('EARNINGS_CONFIG');
    this.is_task_enabled = configService.get<boolean>('IS_TASK_ENABLED');
  }

  // compute earnings
  @Cron(CronExpression.EVERY_10_MINUTES, { name: 'compute-earnings' })
  async handleCron() {
    if (!this.is_task_enabled) {
      this.logger.log('ComputeEarningsTask is disabled');
      return;
    }
    try {
      // 根据100条订单获取还未结算的奖励
      const PendingList = await this.prisma.dobOrder.findMany({
        include: {
          cluster: true,
          dob: true,
        },
        where: {
          earningsType: EarningsType.PROCESSING,
          status: PaymentStatus.DONE,
        },
        take: 100,
      });
      if (PendingList.length > 0) {
        const BookIdList = PendingList.map((order) => {
          return order.cluster.bookId;
        });
        //找价格配置
        const countConfig = await this.prisma.price.findMany({
          select: {
            bookId: true,
            goldCount: true,
            goldPrice: true,
            silverCount: true,
            silverPrice: true,
            copperCount: true,
            copperPrice: true,
            bluePrice: true,
          },
          where: { bookId: { in: BookIdList } },
        });
        //没有价格配置 不计算
        if (countConfig.length == 0) {
          const idList = PendingList.map((order) => {
            return order.id;
          });
          await this.prisma.dobOrder.updateMany({
            where: {
              id: {
                in: idList,
              },
            },
            data: { earningsType: EarningsType.FINISH },
          });
          this.logger.debug('Earnings Error:Books Price Not Find', idList);
          return true;
        }
        const countMap = countConfig.reduce((map, obj) => {
          return { ...map, [obj.bookId]: obj };
        }, {});
        const setting = JSON.parse(this.earnings_config);
        // TODO: 一次性更新或者插入多条
        for (const order of PendingList) {
          if (!order.cluster || !order.cluster.bookId || order.dob.length == 0)
            continue;
          const bookId = order.cluster.bookId;
          const dobList = order.dob;
          // const dobList = order.dob;
          if (countMap[bookId]) {
            // 默认取订单里面的金额-优先price
            let totalPrice = order.price.toNumber();
            let ratioConfig: RatioConfig;
            if (order.level === DnaLevel.GOLD) {
              ratioConfig = setting['GOLD'];
              // totalPrice = countMap[bookId]['goldPrice'] ?? totalPrice;
            } else if (order.level === DnaLevel.SILVER) {
              ratioConfig = setting['SILVER'];
              // totalPrice = countMap[bookId]['silverPrice'] ?? totalPrice;
            } else if (order.level === DnaLevel.COPPER) {
              ratioConfig = setting['COPPER'];
              // totalPrice = countMap[bookId]['copperPrice'] ?? totalPrice;
            } else if (order.level === DnaLevel.BLUE) {
              ratioConfig = setting['BLUE'];
              // totalPrice = countMap[bookId]['bluePrice'] ?? totalPrice;
            }

            const earningsData = this.getEarningsResult(
              ratioConfig,
              totalPrice,
              countMap[bookId],
            );
            // 如果没有书籍收益-则新增
            const BookEarningsCreateData: Prisma.BookEarningsLogCreateInput = {
              ...earningsData,
              book: {
                connect: { id: bookId },
              },
            };
            // 如果有书籍收益-则更新
            const BookEarningsUpdateData: Prisma.BookEarningsLogUpdateInput = {
              goldEarnings:
                earningsData.goldEarnings > 0
                  ? {
                      increment: earningsData.goldEarnings,
                    }
                  : undefined,
              silverEarnings:
                earningsData.silverEarnings > 0
                  ? {
                      increment: earningsData.silverEarnings,
                    }
                  : undefined,
              copperEarnings:
                earningsData.copperEarnings > 0
                  ? {
                      increment: earningsData.copperEarnings,
                    }
                  : undefined,
              authorEarnings:
                earningsData.authorEarnings > 0
                  ? {
                      increment: earningsData.authorEarnings,
                    }
                  : undefined,
              platformEarnings:
                earningsData.platformEarnings > 0
                  ? {
                      increment: earningsData.platformEarnings,
                    }
                  : undefined,
            };

            await this.prisma.$transaction(async (tx) => {
              // 写入书籍总收益记录
              await tx.bookEarningsLog.upsert({
                where: { bookId: bookId },
                create: BookEarningsCreateData,
                update: BookEarningsUpdateData,
              });

              // 写入dob持有收益记录|兼容批量购买的情况
              const dobListlen = dobList.length;
              // 因为一个订单可以对应多个dob 所以除去dob的数量
              for (const dobOne of dobList) {
                const DobEarningsCreateData: Prisma.DobEarningsLogCreateInput =
                  {
                    price: order.price.div(dobListlen),
                    level: order.level,
                    goldHoldEarnings: earningsData.goldEarnings / dobListlen,
                    silverHoldEarnings:
                      earningsData.silverEarnings / dobListlen,
                    copperHoldEarnings:
                      earningsData.copperEarnings / dobListlen,
                    blueHoldEarnings: 0,
                    authorEarnings: earningsData.authorEarnings / dobListlen,
                    platformEarnings:
                      earningsData.platformEarnings / dobListlen,
                    book: {
                      connect: {
                        id: bookId,
                      },
                    },
                    dob: {
                      connect: {
                        id: dobOne.id,
                      },
                    },
                  };
                await tx.dobEarningsLog.upsert({
                  where: { dobId: dobOne.id },
                  update: { level: dobOne.level }, //兼容重复计算的情况（重复mint的情况下就会出现这种问题）
                  create: DobEarningsCreateData,
                });
              }

              // 更新订单
              await tx.dobOrder.update({
                where: { id: order.id },
                data: { earningsType: EarningsType.FINISH },
              });
            });
          } else {
            // 没有价格信息 脏数据 更新订单
            this.prisma.dobOrder.update({
              where: { id: order.id },
              data: { earningsType: EarningsType.FINISH },
            });
          }
        }
      }
    } catch (error) {
      this.logger.debug('compute-earnings Cron Error:', error);
    }
  }

  private getEarningsResult(
    ratioConfig: RatioConfig,
    totalPrice: number,
    countMap: object,
  ) {
    //计算奖励
    const result = {
      goldEarnings: 0,
      silverEarnings: 0,
      copperEarnings: 0,
      authorEarnings: 0,
      platformEarnings: 0,
    };
    if (ratioConfig.author > 0) {
      result.authorEarnings = (totalPrice * ratioConfig.author) / 100;
    }
    if (ratioConfig.platform > 0) {
      result.platformEarnings = (totalPrice * ratioConfig.platform) / 100;
    }
    if (ratioConfig.gold > 0 && countMap['goldCount'] > 0) {
      result.goldEarnings =
        (totalPrice * ratioConfig.gold) / countMap['goldCount'] / 100;
    }
    if (ratioConfig.silver > 0 && countMap['silverCount'] > 0) {
      result.silverEarnings =
        (totalPrice * ratioConfig.silver) / countMap['silverCount'] / 100;
    }
    if (ratioConfig.copper > 0 && countMap['copperCount'] > 0) {
      result.copperEarnings =
        (totalPrice * ratioConfig.copper) / countMap['copperCount'] / 100;
    }
    return result;
  }
}
