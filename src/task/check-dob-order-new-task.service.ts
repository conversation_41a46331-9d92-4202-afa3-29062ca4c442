import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'nestjs-prisma';
import { CryptoService } from '../resources/crypto/crypto.service';
import { ConfigService } from '@nestjs/config';
import { ccc } from '@ckb-ccc/core';
import { transferSpore } from '@ckb-ccc/spore';
import { EarningsType, PaymentStatus, Prisma } from '@prisma/client';
import { DobsService } from 'src/resources/dobs/dobs.service';

//BTC支付 异步扫描支付订单 目前eth 同步获取交易hash 不需要异步扫描
@Injectable()
export class CheckDobOrderTaskNewService {
  private readonly logger = new Logger(CheckDobOrderTaskNewService.name);
  private config_network;
  private config_secp256k1_key;
  private is_task_enabled: boolean;
  private isLocked = false;
  constructor(
    private prisma: PrismaService,
    private cryptoService: CryptoService,
    private configService: ConfigService,
    private dobsService: DobsService,
  ) {
    this.is_task_enabled = configService.get<boolean>('IS_TASK_ENABLED');
    this.config_network =
      configService.get<string>('BLOCK_CHAIN_NETWORK') == 'Mainnet'
        ? new ccc.ClientPublicMainnet()
        : new ccc.ClientPublicTestnet();

    this.config_secp256k1_key = configService.get<string>(
      'SECP256K1_PRIVATE_KEY',
    );
  }
  //order for hash
  @Cron(CronExpression.EVERY_MINUTE, { name: 'check-order-hash' })
  async handleHashOrderNewCron() {
    if (!this.is_task_enabled || this.isLocked) {
      this.logger.log('CheckDobOrderTaskNew is disabled');
      return;
    }
    this.isLocked = true;
    try {
      const order_data = await this.prisma.dobOrder.findMany({
        where: {
          status: PaymentStatus.PAID,
          level: 'COPPER',
        },
        include: { cluster: true },
        take: 10,
      });
      const previousHour = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);
      console.log('check-order-hash-new', order_data.length);

      for (const order_one of order_data) {
        //判定是否过期
        if (order_one.createdAt < previousHour) {
          // 订单超时-自动失败
          await this.prisma.dobOrder.updateMany({
            where: {
              id: order_one.id,
            },
            data: {
              status: PaymentStatus.FAILED,
            },
          });
          await this.dobsService.callBackDobOrder(order_one.id);
        } else {
          if (order_one.txhash && order_one.payUserId && order_one.network) {
            console.log(order_one.id, 'orderID');
            // check transfer hash
            const txData = await this.cryptoService.findTransferByHash(
              order_one.network,
              order_one.txhash,
            );
            // 如果是rpc异常
            if (txData.error) {
              // rpc异常 - 失败
              await this.prisma.dobOrder.update({
                where: {
                  id: order_one.id,
                },
                data: {
                  // 使用CANCELED表示异常 取消验证
                  status: PaymentStatus.CANCELED,
                },
              });
              await this.dobsService.callBackDobOrder(order_one.id);
            }
            // 验证成功后
            let cryptoPrice: string;
            if (order_one.network == 'ETH') {
              // eth 18位太长了 数据库存不下 会失败
              txData.value =
                txData.value.length > 15
                  ? txData.value.slice(0, 15)
                  : txData.value;
              cryptoPrice =
                order_one.cryptoPrice.toString().length > 15
                  ? order_one.cryptoPrice.toString().slice(0, 15)
                  : order_one.cryptoPrice.toString();
            } else {
              cryptoPrice = order_one.cryptoPrice.toString();
            }
            if (txData.isSuccess && txData.value == cryptoPrice) {
              const payUser = await this.prisma.wallet.findUnique({
                where: { userId: order_one.payUserId },
              });
              //转移dobs的逻辑
              let userWallet: any;
              if (payUser && payUser.address) {
                userWallet = await ccc.Address.fromString(
                  payUser.address,
                  this.config_network,
                );
              }
              const signer = new ccc.SignerCkbPrivateKey(
                this.config_network,
                this.config_secp256k1_key,
              );
              let isAdmin: boolean, ownerAddress: string;
              if (!userWallet) {
                isAdmin = true;
                ownerAddress = signer.getRecommendedAddressObj.toString();
              } else {
                isAdmin = false;
                ownerAddress = payUser.address;
              }
              //一个记录里面amount为多个的时候
              for (let i = 1; i <= order_one.amount; i++) {
                // 获取dobs-id |一个一个获取防止并发问题
                const platformDobId = await this.prisma.platformDobs.findFirst({
                  where: { status: 'UNUSED' },
                });
                const sporeId = platformDobId.SporeId ?? '';
                // 没有sporeid的时候
                if (!sporeId) continue;
                const { tx } = await transferSpore({
                  signer,
                  id: platformDobId.SporeId,
                  to: userWallet.script,
                });
                await tx.completeFeeBy(signer);
                const signedTx = await signer.signTransaction(tx);
                const txHash = signedTx.hash();

                const spore: Prisma.DobCreateInput = {
                  dobId: sporeId,
                  ownerAddress: ownerAddress,
                  isAdmin: isAdmin,
                  cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60, //1 hour
                  utxoData: 'null',
                  dna: order_one.dna,
                  level: order_one.level,
                  metadata: order_one.dnaData,
                  txHash: txHash,
                  withdrawn: 0,
                  cluster: {
                    connect: {
                      id: order_one.clusterId,
                    },
                  },
                  user: {
                    connect: {
                      id: order_one.payUserId,
                    },
                  },
                  dobOrder: {
                    connect: {
                      id: order_one.id,
                    },
                  },
                };
                //数据库更新事物
                await this.prisma.$transaction(async (tx) => {
                  const dob = await tx.dob.create({ data: spore });
                  // 更新spore 订单
                  await tx.dobOrder.update({
                    where: { id: order_one.id },
                    data: {
                      status: PaymentStatus.DONE,
                      earningsType: EarningsType.PROCESSING,
                      dob: { connect: { id: dob.id } },
                    },
                  });
                  //更新dobs使用
                  await tx.platformDobs.update({
                    where: { id: platformDobId.id },
                    data: { status: 'USED' },
                  });
                  return dob;
                });
                //最后发起交易|兼容一笔里面多个dob
                await signer.client.sendTransaction(tx);
                console.log('sendTransaction(tx) success');
              }
              // 更新book 销量
              await this.prisma.book.update({
                where: { id: order_one.cluster.bookId },
                data: {
                  sales: {
                    increment: order_one.amount,
                  },
                },
              });
              console.log('database success');
            } else if (
              txData.isSuccess &&
              txData.value != order_one.price.toString()
            ) {
              // 金额不对 失败
              await this.prisma.dobOrder.update({
                where: {
                  id: order_one.id,
                },
                data: {
                  status: PaymentStatus.FAILED,
                },
              });
              await this.dobsService.callBackDobOrder(order_one.id);
            } else if (!txData.isSuccess && txData.value && txData.from) {
              // 支付状态不对 失败
              await this.prisma.dobOrder.update({
                where: {
                  id: order_one.id,
                },
                data: {
                  status: PaymentStatus.FAILED,
                },
              });
              await this.dobsService.callBackDobOrder(order_one.id);
            }
          }
        }
      }
    } catch (error) {
      this.logger.debug('order-hash Cron Error:', error);
    } finally {
      this.isLocked = false;
    }
  }
}
