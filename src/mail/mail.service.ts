import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';

import { MailerService } from 'src/mailer/mailer.service';

export interface MailData<T = never> {
  to: string;
  data: T;
}

@Injectable()
export class MailService {
  constructor(
    private readonly mailerService: MailerService,
    private readonly config: ConfigService,
  ) {}

  async magicLink(
    mailData: MailData<{
      code: string;
      expired: string;
      browser: string;
      os: string;
    }>,
  ) {
    const url = new URL(
      this.config.getOrThrow('BASE_URL', { infer: true }) + '/auth/login',
    );
    const formattedDate = this.formatDate(new Date());
    await this.mailerService.sendMail({
      to: mailData.to,
      subject: '[Silentberry] Sign In Verification',
      text: '',
      templatePath: path.join(__dirname, 'templates', 'magic-link.hbs'),
      context: {
        title: '[<PERSON><PERSON>] Sign In Verification',
        url: url.toString(),
        actionTitle: '[<PERSON>berry] Sign In Verification',
        app_name: '<PERSON><PERSON>',
        text1: `Hi ${mailData.to}`,
        text2: `Your authentication code is: ${mailData.data.code}`,
        text3: `The code expires in ${mailData.data.expired}`,
        text4: `This code was requested on ${formattedDate} using ${mailData.data.browser}, ${mailData.data.os}.`,
      },
    });
  }

  private formatDate(date) {
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
}
