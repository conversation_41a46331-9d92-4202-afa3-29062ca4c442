import {
  ClassSerializerInterceptor,
  Logger,
  ValidationPipe,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory, Reflector } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  const app = await NestFactory.create(AppModule, { rawBody: true });
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      stopAtFirstError: true,
    }),
  );
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
  app.setGlobalPrefix('api');
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    optionsSuccessStatus: 204,
    credentials: true,
  });

  const configService = app.get(ConfigService);

  const env = configService.get<string>('ENVIRONMENT');
  if (env !== 'production') {
    const config = new DocumentBuilder()
      .setTitle(configService.get<string>('PROJECT_TITLE'))
      .setDescription(configService.get<string>('PROJECT_DESCRIPTION'))
      .setVersion(configService.get<string>('API_VERSION'))
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('docs', app, document, {
      jsonDocumentUrl: 'docs/swagger.json',
    });
  }

  await app.listen(
    configService.get<number>('PORT'),
    configService.get<string>('HOST'),
  );
  logger.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap();
