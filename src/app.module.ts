import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { ScheduleModule } from '@nestjs/schedule';
import { PrismaModule } from 'nestjs-prisma';
import { validationSchema } from './config/config.schema';
import { Configuration } from './config/configuration';
import { JwtMiddleware } from './iam/auth/middlewares/jwt.middleware';
import { IamModule } from './iam/iam.module';
import { MailModule } from './mail/mail.module';
import { MailerModule } from './mailer/mailer.module';
import { AuthorModule } from './resources/author/author.module';
import { BooksModule } from './resources/books/books.module';
import { ClustersModule } from './resources/clusters/clusters.module';
import { CryptoModule } from './resources/crypto/crypto.module';
import { DobsModule } from './resources/dobs/dobs.module';
import { EarningsModule } from './resources/earnings/earnings.module';
import { IssueModule } from './resources/issue/issue.module';
import { LuluModule } from './resources/lulu/lulu.module';
import { PaymentModule } from './resources/payment/payment.module';
import { PrintOrdersModule } from './resources/print-orders/orders.module';
import { StripeModule } from './resources/stripe/stripe.module';
import { UsersModule } from './resources/users/users.module';
import { WithdrawModule } from './resources/withdraw/withdraw.module';
import { ComputeEarningsTaskService } from './task/compute-earnings-task.service';
import { CreatePrintJobTaskService } from './task/create-print-job.service';
// import { CheckDobOrderTaskService } from './task/check-dob-order-task.service';
import { Secp256k1WalletService } from './common/utils/secp256k1.service';
import { getBinanceApiTaskService } from './task/get-binance-api-task.service';
import { UpdateDobOwnerTaskTaskService } from './task/update-dob-owner-task.service';
// import { CheckDobOrderTaskNewService } from './task/check-dob-order-new-task.service';
import { AwsS3Service } from './common/utils/awsS3.service';
import { EncryptBookSourceTaskService } from './task/encrypt-book-source.service';
import { StringService } from './common/utils/string.service';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema,
      load: [Configuration],
    }),
    PrismaModule,
    IamModule,
    UsersModule,
    BooksModule,
    MailerModule,
    ClustersModule,
    DobsModule,
    MailModule,
    CryptoModule,
    ScheduleModule.forRoot(),
    PrintOrdersModule,
    PaymentModule,
    StripeModule,
    LuluModule,
    IssueModule,
    WithdrawModule,
    AuthorModule,
    EarningsModule,
  ],
  providers: [
    JwtService,
    AwsS3Service,
    StringService,
    Secp256k1WalletService,
    CreatePrintJobTaskService,
    ComputeEarningsTaskService,
    // CheckDobOrderTaskService,
    // CheckDobOrderTaskNewService,
    UpdateDobOwnerTaskTaskService, // 全量同步地址
    getBinanceApiTaskService, // 获取价格接口
    EncryptBookSourceTaskService,
  ],
})
export class AppModule implements NestModule {
  public configure(consumer: MiddlewareConsumer): void {
    consumer.apply(JwtMiddleware).forRoutes('*');
  }
}
