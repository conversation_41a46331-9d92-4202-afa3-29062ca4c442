// wallet.ts
import { BI, config, hd, helpers } from '@ckb-lumos/lumos';
const { mnemonic, ExtendedPrivateKey, AddressType } = hd;
config.initializeConfig(config.predefined.LINA);

export const generateFirstHDPrivateKey = () => {
  const myMnemonic = mnemonic.generateMnemonic();
  const seed = mnemonic.mnemonicToSeedSync(myMnemonic);
  //console.log("my mnemonic ", seed.toString('hex'));

  const extendedPrivKey = ExtendedPrivateKey.fromSeed(seed);
  return extendedPrivKey.privateKeyInfo(AddressType.Receiving, 0).privateKey;
};

const getAddressByPrivateKey = (privateKey: string) => {
  const args = hd.key.privateKeyToBlake160(privateKey);
  const template = config.predefined.LINA.SCRIPTS['SECP256K1_BLAKE160']!;
  const lockScript = {
    codeHash: template.CODE_HASH,
    hashType: template.HASH_TYPE,
    args: args,
  };

  return helpers.encodeToAddress(lockScript);
};

import { Indexer } from '@ckb-lumos/lumos';

const CKB_RPC_URL = 'https://mainnet.ckbapp.dev/rpc';
const CKB_INDEXER_URL = 'https://mainnet.ckbapp.dev/indexer';

// const rpc = new RPC(CKB_RPC_URL);
const indexer = new Indexer(CKB_INDEXER_URL, CKB_RPC_URL);

export async function getCapacities(address: string): Promise<BI> {
  const collector = indexer.collector({
    lock: helpers.parseAddress(address),
  });

  let capacities = BI.from(0);
  for await (const cell of collector.collect()) {
    capacities = capacities.add(cell.cellOutput.capacity);
  }

  return capacities;
}

// const privateKey = generateFirstHDPrivateKey();
// const address = getAddressByPrivateKey(privateKey);
// console.log('privateKey: ', privateKey);
// console.log('address: ', address);

// getCapacities(address).then((capacities) =>
//   console.log(`balance: ${capacities.div(10 ** 8).toString()} CKB`),
// );
getCapacities(
  'ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsq0rlwja8u203clt0tzpvt97tu0zx2aed0cvcfy0q',
).then((capacities) =>
  console.log(`balance: ${capacities.div(10 ** 8).toString()} CKB`),
);
