FROM node:20 AS base

RUN npm install --global corepack@latest
RUN corepack enable pnpm && corepack install -g pnpm


FROM base AS build
WORKDIR /app
COPY . .
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store pnpm fetch --frozen-lockfile
RUN --mount=type=cache,id=pnpm,target=/root/.local/share/pnpm/store pnpm install --frozen-lockfile
RUN pnpm build
USER node

FROM base

WORKDIR /app
COPY --from=build --chown=node:node /app/node_modules /app/node_modules
COPY --from=build --chown=node:node /app/dist /app/dist
COPY --from=build --chown=node:node /app/prisma /app/prisma
# COPY --from=build --chown=node:node /app/src/task/aspose.lic /app/src/task/aspose.lic
COPY --from=build --chown=node:node /app/aspose.lic /app/aspose.lic
COPY --from=build --chown=node:node /app/package.json /app/package.json
ENV NODE_ENV production
EXPOSE 3000
USER node
CMD ["node", "./dist/src/main.js"]
