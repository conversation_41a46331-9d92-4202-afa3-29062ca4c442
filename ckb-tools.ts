import { Address, AddressType } from '@lay2/pw-core';
import {
  AddressPrefix,
  privateKeyToAddress,
  privateKeyToPublicKey,
} from '@nervosnetwork/ckb-sdk-utils';
import * as Crypto from 'crypto';

function generateRandomPrivateKey() {
  const promise = new Promise((resolve, _reject) => {
    Crypto.randomBytes(32, (error: Error | null, buffer: Uint8Array) => {
      const hexBytes = Buffer.from(buffer).toString('hex');
      resolve('0x' + hexBytes);
    });
  });

  return promise;
}

generateRandomPrivateKey().then((privateKey) => {
  const publicKey = privateKeyToPublicKey(privateKey as string);
  const getAddress = () => {
    const _address = new Address(
      privateKeyToAddress(privateKey as string, {
        prefix: AddressPrefix.Testnet,
      }),
      AddressType.ckb,
    );
    return _address.toCKBAddress();
  };

  console.log({ privateKey });
  console.log({ publicKey });
  console.log({ address: getAddress() });
});
