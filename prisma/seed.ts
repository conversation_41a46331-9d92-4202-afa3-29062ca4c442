import { PrismaClient } from '@prisma/client';
// import { seed_check_order_hash } from './seeds/check_dob_order_hash';
import { seed_reissue_copper_temp } from './seeds/reissue_copper_temp';
// import { seed_whitelist } from './seeds/whitelist';
// import { seed_user } from './seeds/user';
// import { seed_books } from './seeds/book';
// import { seed_reissue_gold_temp } from './seeds/reissue_gold_temp';
// import { seed_reissue_copper_temp } from './seeds/reissue_copper_temp';
// import { seed_reissue_silver_temp } from './seeds/reissue_silver_temp';
// import { seed_reissue_cluster_temp } from './seeds/reissue_cluster_temp';
// import { seed_platform_spore } from './seeds/platform_spore';
// import { seed_withdrawal } from './seeds/withdrawal';
// import { seed_issue } from './seeds/issue';
// import { seed_earnings } from './seeds/earnings';

const prisma = new PrismaClient();

async function main() {
  // await seed_check_order_hash();
  //platform spore
  // await seed_platform_spore();
  // ！！！前提是需要讲历史dob信息全部清理！！！！
  // const book_id = '09acf0e0-33e6-4653-bea0-820d9b7e177b';
  // console.log('处理cluster');
  // const new_cluster_id = await seed_reissue_cluster_temp(book_id);
  // const new_cluster_id = 'f18601a6-fc2b-4308-b917-5ef9a0d9c888';
  // console.log('cluster完成--new_cluster_id');
  // 补发dob-空投
  // console.log('开始处理gold');
  // await seed_reissue_gold_temp(new_cluster_id, book_id);
  // console.log('gold结束');
  // console.log('开始处理silver');
  // await seed_reissue_silver_temp(new_cluster_id, book_id);
  // console.log('silver结束');
  // console.log('开始处理copper');
  // await seed_reissue_copper_temp(new_cluster_id, book_id);
  // console.log('copper结束');
  // create two dummy users 必须优先执行用户
  // await seed_user();
  // await seed_issue();
  // await seed_books();
  // await seed_earnings();
  // await seed_withdrawal();
  // await seed_isbnFree();
  // await seed_whitelist();
}

// execute the main function
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    // close Prisma Client at the end
    await prisma.$disconnect();
  });
