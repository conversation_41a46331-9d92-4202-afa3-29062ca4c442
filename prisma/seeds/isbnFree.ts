import { IsbnFreeType, PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

function generateISBN13() {
  let isbn = '978';
  for (let i = 0; i < 9; i++) {
    isbn += Math.floor(Math.random() * 10);
  }
  let checksum = 0;
  for (let i = 0; i < 12; i++) {
    checksum += parseInt(isbn[i]) * (i % 2 === 0 ? 1 : 3);
  }
  checksum = (10 - (checksum % 10)) % 10;
  isbn += checksum;
  return isbn;
}

export async function seed_isbnFree() {
  const count = await prisma.isbnFree.count();
  if (count > 10000) return true;
  let i = 1;
  while (i <= 5000) {
    //生成isdn
    const isdn = generateISBN13();
    await prisma.isbnFree.upsert({
      where: { isbnNum: isdn },
      create: {
        isbnNum: isdn,
        status: IsbnFreeType.UNUSED,
      },
      update: {
        isbnNum: isdn,
      },
    });
    i++;
  }
}
