import {
  EarningsType,
  PaymentStatus,
  Prisma,
  PrismaClient,
} from '@prisma/client';

const prisma = new PrismaClient();
// gold 订单信息
const glodOrderids = [
  'a84359ea-23fa-41ca-a10c-c27a1b4a3da3',
  'fcf35f75-d159-4cc0-a3d6-cafe3732a504',
  'c53849c2-b13e-4206-9f74-f7fe476e540f',
  'fd3295b3-281f-40e4-839d-68c22841f908',
  'c1916676-1bd6-4728-afb3-01b3c27ed490',
  'd0721874-867e-4d33-9a87-f59edfb6640b',
  'b721a509-f20b-4c2b-bd7b-ff6cabb9c9d8',
  '78ac4b1b-db75-46d2-b603-48df239ab9fb',
  '739e25e2-9ac4-4c0a-9cae-6054d75d50a0',
  '7324df57-ce2e-423e-89a9-b7ccb00728e5',
  '4121a557-22e5-4893-a663-aed320853cbf',
  '6733070c-8210-47df-8622-0ff4cb91fdaf',
];
// gold 支付用户信息
const goldUserAddresss = [
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqq9pec8p7ye77jjcr5ahasqexuzpfc8gq8czd4zde',
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqqx4cvunkqtsdnh2ly3sm7sgwrz9haa6v7qnxkznl',
  'ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgy8vzt07fp4vgfuyzye9ptpnacyw3tghdhqq48epy3',
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqqxn4cgpklhxfpqr66nly0d0pj9ljq5mc0qw5c9xc',
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqqx93mv70836yh0cp8p2gekyar25x5h3yxgkjwrcl',
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqqx0jg36wvs29vyuw7qamepaxwnafdd5x5ul4y6pw',
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqqxkstr2zyz0vg55c85jk67ud4evjmy9yuvnt33g9',
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqqykts2plxw0dhqcnr2xtwwearxwyvv3mxuuuty8t',
  'ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgj3jgn4mr5g0lzqxrrjyennz24urshlvxpqqf546n9',
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqq84aaq6v5zscakyrmn3ep7m2pxlfask02q58d4v9',
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqq83503v79qrpczf2e6wdgf6jj8am56udwsgxzykc',
  'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqq808z7cunx9am3ts86yn2wrmp5947py42uqx5w0n',
];
// 空投信息
const glodDOBdatas = [
  '1,0x2700326b716758271d960a0e0915f8faef426f3fe02c3b97557475322af47804,0x3680208065b0718a1d38ffa7b27b0f3b812f17a69ded3c0a09a541da7c93a437',
  '2,0xc1f69bb91ba60c1ae89da1b457af6708be02dc7b50997a3d1f1c1d42c73f7269,0x17e301b2d5b51633952231e423eaf812a5e4629afe5ec0b27da9d4443ae2af2e',
  '3,0x011b5aeea28f39d053c7f75de7333ce2182738dd673dcae35b80c739e3d48d97,0x7f17a7df0064b854ee2a9e90b10952f29f62493e8c3b77069f1b347c6b6a5582',
  '4,0xf0c1c3fd9f3dd81c58b3a0df0aae001a7257f5933507c3349efc92b539243e8f,0x8406dfac33dc2a25afa157f839fa857d2c4df6cecc84267fe26172ed257238e6',
  '5,0xb247bd6b7737f1859e98f2e11b2e097b4de589167a6d4345d1c65cf8e0777d51,0xff0a5755c23c4247825ad78ef87ff33421b833133e8b5775b8344f4e42268daf',
  '6,0x133485902d2624b3d8bab57fe800a7539515cf337e866e93fd749636df487845,0x4e49eac4f23d80bd785e1bd235c84c2ed0034ad524f03fbcbc04977c80d8e2e9',
  '7,0x1b46bae8ad2513745606c894c63d456bd9ebc79e8b52b07f15f2cbd1de745922,0xe80b5b2130c8da4321e69419a30d8716e687ee2f29a2750ac852a135ade5e3e2',
  '8,0xce240b3881da2d2f549b7ecfa788ead55aae0c438524b30d220216cf2ac13e3b,0x5afceb6301f27fe7ca1ddb2cb8207a277ecf4c3a190166ae40cf90d225645f6a',
  '9,0x9be256ae2305fd89c62a903f0d8f6dd9ae02dc41edf0b6ce2f6c59a3b2909280,0xf27f262dcce7d8b7ad84a5ea0118686ba841dc1ea0c9c52270e6d66f74109946',
  '10,0xf852afa6f3c6127a73e3c66e8f86788ddeafdfeb2d6200ab193a2053e89aa98b,0x5388af647515d15e09ea0eef7d09e259ec06de9e9c163a6505f89ccb950e4edf',
  '11,0xcd28cfac719a1075121adc6bba91ed6a4fc6b4d2375f9c0976d9dbff7030af1b,0x98b483029cd794d9f92bb63349f51c6575613cc500bccf9ce11c567281ae97af',
  '12,0xc96a6bda8e575760cad8ab6b2db8ded20a359ef55c6a3b7c619bf5e04b599fd9,0x08412e0fd051244533d9d7eb33515046e90e8e87e0caf1dbf736b2801347d62c',
];
// ！！！ 需要清理dob历史数据
export async function seed_reissue_gold_temp(
  new_cluster_id: string,
  book_id: string,
) {
  //更新交易
  if (glodOrderids.length == glodDOBdatas.length) {
    for (const [index, goldOrder] of glodOrderids.entries()) {
      // 获取空投新消息
      const dobStr = glodDOBdatas[index];
      if (!dobStr) continue;
      // 0:id 1:txHash 2:sporeID
      const dobData = dobStr.split(',');
      // 根据订单id 获取交易记录
      const order_one = await prisma.dobOrder.findUnique({
        where: { id: goldOrder },
        include: { payUser: true },
      });
      // 没有找到订单id
      if (!order_one) {
        console.log('orderID 未找到', goldOrder);
        continue;
      }
      const ownerAddress =
        goldUserAddresss[index] != 'NULL' ? goldUserAddresss[index] : '';
      //创建dob记录
      const spore: Prisma.DobCreateInput = {
        dobId: dobData[2], //目前只有1个
        ownerAddress:
          ownerAddress ??
          'ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d', //持有者地址
        isAdmin: ownerAddress ? false : true, //如果没有持有者变成管理员
        cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60, //1 hour
        utxoData: 'null',
        dna: order_one.dna,
        level: order_one.level,
        metadata: order_one.dnaData,
        txHash: dobData[1],
        withdrawn: 0,
        cluster: {
          connect: {
            id: new_cluster_id, //绑定新的cluster-id
          },
        },
        user: {
          connect: {
            id: order_one.payUserId,
          },
        },
        dobOrder: {
          connect: {
            id: order_one.id,
          },
        },
      };

      await prisma.$transaction(async (tx) => {
        const dob = await tx.dob.create({ data: spore });
        // 更新spore 订单
        await tx.dobOrder.update({
          where: { id: order_one.id },
          data: {
            status: PaymentStatus.DONE,
            earningsType: EarningsType.PROCESSING,
            dob: { connect: { id: dob.id } },
            clusterId: new_cluster_id,
          },
        });
        return dob;
      });
    }
    //更新book销量
    await prisma.book.update({
      where: { id: book_id },
      data: {
        sales: glodOrderids.length,
      },
    });
  } else {
    console.log('gold 数据不对');
  }
}
