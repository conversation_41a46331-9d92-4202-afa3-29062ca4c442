import { PrismaClient, Role } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();
const roundsOfHashing = 10;

export async function seed_user() {
  const passwordSabin = await bcrypt.hash('password-sabin', roundsOfHashing);
  const passwordAlex = await bcrypt.hash('password-alex', roundsOfHashing);

  const passwordCode = await bcrypt.hash(
    'password-callback-code',
    roundsOfHashing,
  );

  const user1 = await prisma.user.upsert({
    where: {
      email: '<EMAIL>',
    },
    update: {
      password: passwordSabin,
    },
    create: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON>',
      password: passwordSabin,
    },
  });
  const user2 = await prisma.user.upsert({
    where: {
      email: '<EMAIL>',
    },
    update: {
      password: passwordAlex,
      role: Role.EDITOR,
    },
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: passwordAlex,
      role: Role.EDITOR,
    },
  });

  await prisma.user.upsert({
    where: {
      email: '<EMAIL>',
    },
    update: {
      password: passwordCode,
      role: Role.ADMIN,
    },
    create: {
      email: '<EMAIL>',
      name: 'Do not update',
      password: passwordCode,
      role: Role.ADMIN,
    },
  });

  console.log({ user1, user2 });
}
