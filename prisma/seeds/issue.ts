import { IssueGroup, IssueType, PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

function generateISBN13() {
  let isbn = '978';
  for (let i = 0; i < 9; i++) {
    isbn += Math.floor(Math.random() * 10);
  }
  let checksum = 0;
  for (let i = 0; i < 12; i++) {
    checksum += parseInt(isbn[i]) * (i % 2 === 0 ? 1 : 3);
  }
  checksum = (10 - (checksum % 10)) % 10;
  isbn += checksum;
  return isbn;
}

function getRandomEnumValue(): IssueGroup {
  const keys = Object.keys(IssueGroup);
  const randomIndex = Math.floor(Math.random() * keys.length);
  return IssueGroup[keys[randomIndex] as keyof typeof IssueGroup];
}

export async function seed_issue() {
  const faqcount = await prisma.issue.count({
    where: { type: IssueType.FAQ },
  });

  const helpcount = await prisma.issue.count({
    where: { type: IssueType.HELP },
  });
  if (faqcount <= 10) {
    let i = 1;
    while (i <= 10) {
      //生成isdn
      const isdn = generateISBN13();
      await prisma.issue.create({
        data: {
          type: IssueType.FAQ,
          group: getRandomEnumValue(),
          ask: 'faq-title--' + isdn,
          answer: 'faq-data ---' + isdn + randomUUID(),
        },
      });
      i++;
    }
  }

  if (helpcount <= 10) {
    let j = 1;
    while (j <= 10) {
      //生成isdn
      const isdn = generateISBN13();
      await prisma.issue.create({
        data: {
          type: IssueType.HELP,
          group: getRandomEnumValue(),
          ask: 'help-title--' + isdn,
          answer: 'help-data ---' + isdn + randomUUID(),
        },
      });
      j++;
    }
  }
}
