import {
  DnaLevel,
  EarningsType,
  PaymentStatus,
  Prisma,
  PrismaClient,
} from '@prisma/client';

const prisma = new PrismaClient();
// copper 订单信息
const copperOrderids = [
  'd924b461-a6d5-4f01-b2cf-93a9d06f3241',
  '439ebe4f-8d8f-4bef-8180-4a4c9d15fee5',
  '5bce1799-513d-416d-a231-f42c1ce10d57',
  '05cb22eb-981e-429f-9a22-087abca78e2a',
  'a92358cc-9a81-4a0e-9d81-4a76b74cb63c',
  '8eea7f66-fc07-43c3-ae50-407202bf517b',
  '86f099df-74db-46c9-8c69-1a89ce591e58',
  '5829addd-4ac2-44d0-8ad2-20b67b071962',
  '5829addd-4ac2-44d0-8ad2-20b67b071962',
  '40e5ec7f-6d71-4432-a31b-183e4ed2e483',
  '40e5ec7f-6d71-4432-a31b-183e4ed2e483',
  '4d496e3e-69de-4489-bb4e-23e2be6779d1',
  '31964e64-98ce-4aa8-a54c-d38e1bb321e9',
  '4d5187bf-c398-44a9-8354-0fd295ed00e7',
  '31964e64-98ce-4aa8-a54c-d38e1bb321e9',
  '5ee189b0-c68a-41e9-8a6e-111e74de82a4',
  '1265f6ab-c0f9-42d6-bb38-ac079e6ffebf',
  '3683a180-9fa6-4ff8-8ffe-19e0d6aa157c',
  '92b0151e-8e54-4287-a606-93e5156c5549',
  '05327c6e-338b-473e-8e72-d90ac7bd5f65',
  'd0b88bed-8336-4b26-a806-02243cb3c020',
  '27c01490-33e9-4ec7-be23-f8fcf6b4a2ab',
];
// // copper 支付用户信息
// const copperUserAddresss = [
//   'ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgjzt3nvp8ct8l7wgcnyhelxudl0tjgj3ekqqpfspyh',
//   'NULL',
//   'NULL',
//   'NULL',
//   'NULL',
//   'NULL',
//   'NULL',
//   'NULL',
//   'NULL',
//   'NULL',
//   'NULL',
//   'NULL',
//   'NULL',
//   'ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgjmw9x6940ngpa4ueh5n3x52lrmmp5zhmtqqa4jap6',
//   'NULL',
//   'NULL',
//   'NULL',
//   'ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgy50c50w8m3pc9te768zytsr5zuvp85szcqqhjqzyd',
//   'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqqxnca4h3g85sue9q3k5dqnjzdcn3h0gxky9r96a2',
//   'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqq9qwmx2z664qzudzze5xpfujkethveg4s5z34cpw',
//   'ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgyyzrju652tkh79sh4vrukjjz26dh8vvdzqq7wa6rv',
//   'ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqq9qwmx2z664qzudzze5xpfujkethveg4s5z34cpw',
// ];
// 空投信息
const copperDOBdatas = [
  '0xbbee24075d9dbeb2a58e3e676b43153fbaaa801cd5356cad83afb1aa62c61aed ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgjzt3nvp8ct8l7wgcnyhelxudl0tjgj3ekqqpfspyh 0xd651fecc26ca3f4665a99c695e25b147ce70ad631c8f8e66b65bd0c7900208b7',
  '0x29ed3f411929b261d619bfa80c3ba9392cd72d850d39062bfd4f7a5512f94f62 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0xc6e96e40a0a56c5eda889e337dd1f12383f705f7ad82f4b31a57a2a1554f250e',
  '0x96846e71c3ee0bfe5169bd6cb37e873c112331fd2f83c94ceefd9c3484a88494 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0xe6774c95c66876eb1740e404e0e55f12bf6d3ac332e585887995df1cd9d50601',
  '0x9dc073745b4c61a5bdd0e5384b783a844444236275f8fa30e40f13cfd06f4a19 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x785b5f6adfb8da858a223bb81dc5b5952a52c9de65e5438c1dd76c23295acc78',
  '0x4c9351a781a859c76f2e40c2516225d72872596b1c20d44cd0e20dee3d9b178c ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x04b56a53e61c3c549c8b2a20fd5683f2915b742e43a873ae56ddb48bbd5db569',
  '0x1a945edb508ee53660d1971b3d8b14b99bba75bd00acac67c74c772d00fff385 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x04ff5a73219e0fec32ed84277c9d0c61fb733b99f82ce21fda4c1a44db0b77f1',
  '0xced0fdbf479bac4a1436dd92b440c00c9a4297f0b3171488b812bc84c19b4706 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0xd657ad49a7224c2bd4e026a0b9b75160f5b7004dff114777ed17d074d20c9d6e',
  '0x9e1477b281eb6ccda8f50f9410799e9eeca025d9b17ef712771f2b6e697e22ad ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x9464bc66d9090c7fcdbf63a3fd3fef4e7c5abdf8cc5ccf0eef0cea229fbbb55d',
  '0x9844f84dfbc0eba7979311841dd2e9b7bc14a2fad7b6d00ad33faa7960b200e7 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x69630ba03945ee50623fcde2963f2cbcd96a9896018e96dc6bc7c71054cb878b',
  '0x7d39c7599695824f1847ce65fbe467b92f6c255e3202f09af17eff56e8481916 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x092b02e533ee260032571a69f208e760ee0ec015ec8745bd5320afdba68c6a26',
  '0x0fb36327cec4b3d141517ed30602c2f1f8b1124abfdada45b4d822f5a88bdd18 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x58c4cc885336ecebd1c8abf6d085f529bace24ebfe3ab914038aa03b90c8321c',
  '0xf1decb6c075b964e0defc1a57b0bd188680bd9e9a5a743eb8c9e1e37baaa8058 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x5e76086bd1d2f63a6daec8a59c2710a4266c8d83014ec4b28ca14c2432a8bbc4',
  '0x7bacd9e38744bbba05ec2e76b2bcacfc311ef6e4309f48a2ebe3bd9cba52ec3b ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x362492369bc1eb92c0ed1e1e4ddb3efb8e02400df183393edc28b9d83e7ca1cd',
  '0x119ba5288258f8efb34f7512193f257f3f3f845865c409fc1f10d01a4bad20fe ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgjmw9x6940ngpa4ueh5n3x52lrmmp5zhmtqqa4jap6 0x37616288b327f853b0e2ea44ec04d1273061c57bf758d8935e1c48d94224cdc3',
  '0x9ae9b1e8840523d13b44afc7840f7d34eff72524f603484a241fcc591cfa7260 ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x1d2ddfdff2d8dcca0e38f510c8d382d0c601dc3c33f72b83e7186bbae30b4896',
  '0x35b2926a9e8c332cdc3e2597a8de219bcc3b0cc7d33d356411e34c30d8b4b08e ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0xe27143eae66534e460d9953f6a46f957be9de562dcce323f18d49125c880881e',
  '0x9079d05439831ab3ba5ccff10e59ab0654ee38a270a54d16e821b027f5b532ed ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d 0x41020f4e5cfef808d99852e5124d60ac260d29811c50195469dc780ae81ec44b',
  '0x2e24c031914ddcb7843ac456b089c977f16e22b160aca9b883eba12b817199ec ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgy50c50w8m3pc9te768zytsr5zuvp85szcqqhjqzyd 0xffc4444e12a4f3c7d7171125d441c0f26081e935e88be408a8eeba3884692006',
  '0x4c85590c9cfa2a456acd584ba284cc9c2a0b5812ee666df2d28318fb8b868803 ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqqxnca4h3g85sue9q3k5dqnjzdcn3h0gxky9r96a2 0xfd7df65e578067a162b4ee6b172e6e6ebd2e0a1d96b6d2c925a873911c555960',
  '0xda05c2281160991e47f04347acc48077e4792b38ac2a6f19e04601910e3dc8c0 ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqq9qwmx2z664qzudzze5xpfujkethveg4s5z34cpw 0x1814b10ee327fcf5514aff3f82fad217c665ad00976fe4253eeb7009e6e13690',
  '0x117b853bf210e3e730aa87a1a5288d867d11b6c386734685247949d3d711f313 ckb1qzdcr9un5ezx8tkh03s46m9jymh22jruelq8svzr5krj2nx69dhjvqgyyzrju652tkh79sh4vrukjjz26dh8vvdzqq7wa6rv 0x0f1acdbd8b1c99340e58a4c80dd7251b443a8f2b825716b8dd96a4825835f21a',
  '0x4d49a612996ca4f7473516519c1a23d40912b0227c46b4126f33562d4cdbcf4d ckb1qrgqep8saj8agswr30pls73hra28ry8jlnlc3ejzh3dl2ju7xxpjxqgqq9qwmx2z664qzudzze5xpfujkethveg4s5z34cpw 0xbb08229db291dbe017f614f331c18a2b4dbdab5ea1bea3d34e2dabde951236d3',
];

export async function seed_reissue_copper_temp(
  new_cluster_id: string,
  book_id: string,
) {
  //更新交易
  if (copperOrderids.length == copperDOBdatas.length) {
    for (const [index, copperOrder] of copperOrderids.entries()) {
      // 获取空投新消息
      const dobStr = copperDOBdatas[index];
      if (!dobStr) continue;
      // 0:id 1:txHash 2:sporeID
      const dobData = dobStr.split(' ');
      // 根据订单id 获取交易记录
      let order_one = await prisma.dobOrder.findUnique({
        where: { id: copperOrder },
        include: { payUser: true },
      });
      const ownerAddress = dobData[1];
      if (!order_one) {
        console.log('orderID 未找到 创建doborder', copperOrder);
        const payuser = await prisma.wallet.findFirst({
          where: { address: ownerAddress },
        });
        //创建order
        const dna: Prisma.DobOrderCreateInput = {
          dna: 'temp_dna',
          level: DnaLevel.COPPER,
          dnaData: JSON.stringify('temp_dna'),
          status: PaymentStatus.PENDING,
          price: 15,
          cryptoPrice: 1247,
          amount: 1,
          earningsType: EarningsType.INIT,
          cluster: {
            connect: {
              clusterId: new_cluster_id,
            },
          },
          payUser: {
            connect: {
              id: payuser?.userId ?? '',
            },
          },
        };
        const order = await prisma.dobOrder.create({ data: dna });
        order_one = await prisma.dobOrder.findUnique({
          where: {
            id: order.id,
          },
          include: { payUser: true },
        });
      }
      if (!order_one) {
        console.log('dob order 空');
        continue;
      }
      //创建dob记录
      const spore: Prisma.DobCreateInput = {
        dobId: dobData[2], //目前只有1个
        ownerAddress:
          ownerAddress ??
          'ckb1qzda0cr08m85hc8jlnfp3zer7xulejywt49kt2rr0vthywaa50xwsqd0guxpvrq96xgvw6q850mcnz5y9s9mduq6xqy4d', //持有者地址
        isAdmin: ownerAddress ? false : true, //如果没有持有者变成管理员
        cacheExpTime: Math.floor(Date.now() / 1000) + 60 * 60, //1 hour
        utxoData: 'null',
        dna: order_one.dna,
        level: order_one.level,
        metadata: order_one.dnaData,
        txHash: dobData[0],
        withdrawn: 0,
        cluster: {
          connect: {
            id: new_cluster_id, //绑定新的cluster-id
          },
        },
        user: {
          connect: {
            id: order_one.payUserId,
          },
        },
        dobOrder: {
          connect: {
            id: order_one.id,
          },
        },
      };

      await prisma.$transaction(async (tx) => {
        const dob = await tx.dob.create({ data: spore });
        // 更新spore 订单
        await tx.dobOrder.update({
          where: { id: order_one.id },
          data: {
            status: PaymentStatus.DONE,
            earningsType: EarningsType.PROCESSING,
            dob: { connect: { id: dob.id } },
            clusterId: new_cluster_id,
          },
        });
        return dob;
      });
    }
    //更新book销量
    await prisma.book.update({
      where: { id: book_id },
      data: {
        sales: copperOrderids.length,
      },
    });
  } else {
    console.log('copper 数据不对');
  }
}
