import {
  PaymentStatus,
  PrismaClient,
  WithdrawalSource,
  WithdrawalType,
} from '@prisma/client';

const prisma = new PrismaClient();

export async function seed_withdrawal() {
  const user1 = await prisma.user.findUnique({
    where: {
      email: '<EMAIL>',
    },
  });
  if (!user1) return true;
  await prisma.withdrawalOrder.create({
    data: {
      collectionAddress: 'vsanum-1230213465',
      withdrawalPrice: 6.6,
      withdrawalType: WithdrawalType.VISA,
      status: PaymentStatus.CANCELED,
      withdrawalSource: WithdrawalSource.AUTHOR,
      user: {
        connect: {
          id: user1.id!,
        },
      },
    },
  });
  await prisma.withdrawalOrder.create({
    data: {
      collectionAddress: '0xxmetamask',
      withdrawalPrice: 7.6,
      withdrawalType: WithdrawalType.METAMASK,
      status: PaymentStatus.DONE,
      withdrawalSource: WithdrawalSource.DOB,
      user: {
        connect: {
          id: user1.id!,
        },
      },
    },
  });
  await prisma.withdrawalOrder.create({
    data: {
      collectionAddress: '0xxeth',
      withdrawalPrice: 7.6,
      withdrawalType: WithdrawalType.ETH,
      status: PaymentStatus.FAILED,
      withdrawalSource: WithdrawalSource.DOB,
      user: {
        connect: {
          id: user1.id!,
        },
      },
    },
  });
  await prisma.withdrawalOrder.create({
    data: {
      collectionAddress: '0xx78989',
      withdrawalPrice: 7.6,
      withdrawalType: WithdrawalType.BTC,
      status: PaymentStatus.CANCELED,
      withdrawalSource: WithdrawalSource.AUTHOR,
      user: {
        connect: {
          id: user1.id!,
        },
      },
    },
  });
}
