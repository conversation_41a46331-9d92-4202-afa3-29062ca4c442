import { BookStatus, BookType, DnaLevel, PrismaClient } from '@prisma/client';
// import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

export async function seed_books() {
  const user1 = await prisma.user.findUnique({
    where: {
      email: '<EMAIL>',
    },
  });
  if (!user1) return true;

  let books = await prisma.book.create({
    data: {
      title: 'book test',
      language: 'Acoli',
      category: 'books',
      type: BookType.PRINT_BOOK,
      status: BookStatus.PUBLISHED,
      subtitle: 'subtitle',
      edition: 'edition',
      editionStatement: 'editionStatement',
      description: 'description',
      contributorNotes: 'contributorNotes',
      keywords: 'keywords',
      bisacMainCategory: 'bisacMainCategory',
      audience: 'audience',
      explicitContentType: 'explicitContentType',
      user: {
        connect: {
          id: user1.id!,
        },
      },
      phase: DnaLevel.GOLD,
    },
  });

  // cluster test Data
  const clusterId =
    '0xb09a7b74f08afe5246b415f134fcda946207d761ef92f315ca563cc9dd22c315';
  const cluster = await prisma.cluster.upsert({
    where: {
      clusterId: clusterId,
    },
    update: {
      timestamp: new Date().toString(),
    },
    //{
    //     "code_hash": "0x7366a61534fa7c7e6225ecc0d828ea3b5366adec2b58206f2ee84995fe030075",
    //     "args": "0xb09a7b74f08afe5246b415f134fcda946207d761ef92f315ca563cc9dd22c315",
    //     "hash_type": "data1"
    // }
    create: {
      name: 'test cluster',
      description: 'test cluster description',
      decoder: JSON.stringify({
        type: 'code_hash',
        hash: '0x1c84212ebd817e9de09d2a79f85cc421b684eda63409cfa75688f98716e77b5f',
        tx_hash:
          '0xc877aca405da6a3038054cb5da20f2db0ed46bb643007d4e0b1d3fe7da155bf0',
        out_index: 0,
      }),
      pattern: JSON.stringify([
        {
          traitName: 'Cover',
          dobType: 'Number',
          dnaOffset: 0,
          dnaLength: 6,
          patternType: 'rawNumber',
        },
        {
          traitName: 'Level',
          dobType: 'String',
          dnaOffset: 6,
          dnaLength: 1,
          patternType: 'options',
          traitArgs: ['GOLD', 'SILVER', 'COPPER', 'BLUE'],
        },
        {
          traitName: 'Icon',
          dobType: 'String',
          dnaOffset: 6,
          dnaLength: 1,
          patternType: 'options',
          traitArgs: ['GOLD', 'SILVER', 'COPPER', 'BLUE'],
        },
      ]),
      clusterId: clusterId,
      contractHash: '0x2ad22',
      typeHash:
        '0x52e5418248a2e3125f411670edb8633acc9a4e0d680941270c42d6fb89ef8002',
      creator: user1.id!,
      hash: '0x87759e47b5f1200d63d2ce29622fd1c9e00304cac62c03aee1b56e9aaa1c37bf',
      ownerAddress: '0x2ad22',
      timestamp: new Date().toString(),
      book: {
        connect: {
          id: books.id,
        },
      },
      ckbCellCost: 15000,
      mintFee: 3000,
      sporeCount: 0,
      perMintAmount: 0,
      withdrawn: 0,
    },
  });
  books = await prisma.book.update({
    where: { id: books.id },
    data: {
      cluster: {
        connect: {
          id: cluster.id,
        },
      },
      status: BookStatus.PUBLISHED,
    },
  });
  console.log(books.id, 'books');
}
