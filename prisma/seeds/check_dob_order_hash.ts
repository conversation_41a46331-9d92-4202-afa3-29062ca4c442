import { Payment<PERSON>tatus, PayNetWork, PrismaClient } from '@prisma/client';
import axios from 'axios';
import { ethers } from 'ethers';
import * as fs from 'node:fs/promises';
import { ConfigService } from '@nestjs/config';

const prisma = new PrismaClient();
const configService = new ConfigService();
const config_btc_rpc = configService.get<string>('BTC_RPC') ?? '';
const config_btc_addr = configService.get<string>('OFFICIAL_ADDRESS_BTC') ?? '';

const config_eth_rpc = configService.get<string>('ETH_RPC') ?? '';
const config_eth_addr = configService.get<string>('OFFICIAL_ADDRESS_ETH') ?? '';

const config_ckb_rpc = configService.get<string>('CKB_RPC') ?? '';
const config_ckb_addr = configService.get<string>('OFFICIAL_ADDRESS_CKB') ?? '';

function getRpcForConfig(rpcJson: string) {
  const rpcList: Array<string> = rpcJson ? JSON.parse(rpcJson) : [];
  if (rpcList.length == 0) return '';
  const randomIndex = Math.floor(Math.random() * rpcList.length);
  return rpcList[randomIndex];
}

async function findTransferByHash(network: PayNetWork, hash: string) {
  const OnChainData = {
    from: '',
    to: '',
    value: '0',
    isSuccess: false,
    error: false,
  };

  try {
    switch (network) {
      case PayNetWork.BTC:
        if (!config_btc_rpc) return OnChainData;
        const btc_url = getRpcForConfig(config_btc_rpc)
          .toString()
          .replace('{hash}', hash);
        const _res_btc = await axios.get(btc_url, { timeout: 10000 });
        console.log(btc_url, _res_btc, 'btcccc');
        if (_res_btc.status == 200) {
          const btc_data = _res_btc.data;
          let from_addr;
          let to_addr;
          let value;
          if (btc_data['inputs']) {
            const from_data = btc_data['inputs'][0];
            from_addr = from_data['addresses']
              ? from_data['addresses'][0].toString()
              : from_data['prev_out']['addr'];

            const to_data = btc_data['out']
              ? btc_data['out'][0]
              : btc_data['outputs'][0];
            to_addr = to_data['addresses']
              ? to_data['addresses'][0].toString()
              : to_data['addr'].toString();

            console.log(from_data, to_data, 'aa');
            value = Math.round(to_data['value']);
          } else {
            const from_data = btc_data['vin'][0];

            from_addr = from_data['prevout']
              ? from_data['prevout']['scriptpubkey_address'].toString()
              : '';

            const to_data = btc_data['vout'][0];
            to_addr = to_data['scriptpubkey_address'].toString();

            value = Math.round(to_data['value']);
            console.log(from_data, to_data, 'aa');
          }
          //check addr
          if (to_addr == config_btc_addr) {
            OnChainData.from = from_addr;
            OnChainData.to = to_addr;
            OnChainData.value = value.toString();
            OnChainData.isSuccess = true;
          }
        } else {
          // 如果不是200 说明是服务得问题 放到下一次
          OnChainData.isSuccess = false;
        }
        break;
      case PayNetWork.ETH:
        if (!config_eth_rpc) return OnChainData;
        const eth_url = getRpcForConfig(config_eth_rpc);
        const provider = new ethers.JsonRpcProvider(eth_url);
        const hashData = await provider.getTransaction(hash);
        console.log(hashData, 'ddd');
        if (hashData && hashData?.blockNumber && hashData?.blockNumber > 0) {
          //check addr
          if (
            hashData?.from &&
            hashData?.to?.toString().toLowerCase() ==
              config_eth_addr?.toLowerCase()
          ) {
            OnChainData.from = hashData?.from;
            OnChainData.to = hashData?.to?.toLowerCase();
            OnChainData.isSuccess = true;
            OnChainData.value = hashData?.value?.toString();
          }
        } else {
          OnChainData.isSuccess = false;
        }
        break;
      case PayNetWork.CKB:
        if (!config_ckb_rpc) return OnChainData;
        const ckb_url = getRpcForConfig(config_ckb_rpc)
          .toString()
          .replace('{hash}', hash);
        const headers = {
          Accept: 'application/vnd.api+json',
          'Content-Type': 'application/vnd.api+json',
        };
        OnChainData.isSuccess = false;
        const _res_ckb = await axios.get(ckb_url, {
          timeout: 10000,
          headers: headers,
        });
        if (_res_ckb && _res_ckb.status == 200) {
          const res_data = _res_ckb.data;
          if (res_data) {
            let from_addr = '';
            let to_addr = '';
            let value = 0;
            const ckb_data = res_data['data'];
            console.log(ckb_data, 'ddd');
            if (ckb_data && ckb_data['attributes']) {
              const attributes = ckb_data['attributes'];
              // 状态是成功才行
              if (attributes['tx_status'] == 'committed') {
                OnChainData.isSuccess = true;
              }
              if (
                attributes['display_outputs'] &&
                attributes['display_outputs'].length > 0
              ) {
                const display_outputs = attributes['display_outputs'];
                console.log(display_outputs, 'output');
                from_addr = display_outputs[1]['address_hash'];
                to_addr = display_outputs[0]['address_hash'];
                value = Math.ceil(display_outputs[0]['capacity'] / 1e8);
              }
              //check addr
              console.log(from_addr, to_addr, value, 'aaaa');
              if (to_addr == config_ckb_addr) {
                OnChainData.from = from_addr;
                OnChainData.to = to_addr;
                OnChainData.value = value.toString();
              }
            }
          }
        }
        break;
    }
  } catch (error) {
    OnChainData.error = true;
  }

  return OnChainData;
}

export async function seed_check_order_hash() {
  const order_data = await prisma.dobOrder.findMany({
    where: {
      status: { in: [PaymentStatus.PAID, PaymentStatus.PENDING] },
      clusterId: 'f18601a6-fc2b-4308-b917-5ef9a0d9c888',
      level: 'COPPER',
    },
    include: { cluster: true, payment: true },
    take: 10,
  });
  if (!order_data) {
    console.log('没有数据');
    return;
  }
  const previousHour = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);
  console.log('check-order-hash-new', order_data.length);

  for (const order_one of order_data) {
    //判定是否过期
    if (order_one.createdAt < previousHour) {
      // 订单超时-自动失败
      await prisma.dobOrder.updateMany({
        where: {
          id: order_one.id,
        },
        data: {
          status: PaymentStatus.FAILED,
        },
      });
    } else {
      if (
        order_one.status == 'PENDING' &&
        order_one.network == 'USD' &&
        order_one.paymentId &&
        order_one.payment &&
        order_one.payment.status == 'DONE'
      ) {
        const data = {
          network: order_one.network,
          order_id: order_one.id,
          payType: order_one.status,
          uid: order_one.payUserId,
          level: order_one.level,
        };
        fs.appendFile('/tmp/output.txt', JSON.stringify(data), 'utf8');
        console.log(data, 'order-success-data');
      } else if (order_one.txhash && order_one.payUserId && order_one.network) {
        // check transfer hash
        const txData = await findTransferByHash(
          order_one.network,
          order_one.txhash,
        );
        // 如果是rpc异常
        if (txData.error) {
          // rpc异常 - 失败
          await prisma.dobOrder.update({
            where: {
              id: order_one.id,
            },
            data: {
              // 使用CANCELED表示异常 取消验证
              status: PaymentStatus.CANCELED,
            },
          });
        }
        // 验证成功后
        let cryptoPrice: string;
        if (order_one.network == 'ETH') {
          // eth 18位太长了 数据库存不下 会失败
          txData.value =
            txData.value.length > 15 ? txData.value.slice(0, 15) : txData.value;
          cryptoPrice =
            order_one.cryptoPrice.toString().length > 15
              ? order_one.cryptoPrice.toString().slice(0, 15)
              : order_one.cryptoPrice.toString();
        } else {
          cryptoPrice = order_one.cryptoPrice.toString();
        }
        if (txData.isSuccess && txData.value == cryptoPrice) {
          const data = {
            network: order_one.network,
            order_id: order_one.id,
            payType: order_one.status,
            uid: order_one.payUserId,
            level: order_one.level,
          };
          fs.appendFile('/tmp/output.txt', JSON.stringify(data), 'utf8');
          console.log(data, 'order-success-data');
        } else if (
          txData.isSuccess &&
          txData.value != order_one.price.toString()
        ) {
          // 金额不对 失败
          await prisma.dobOrder.update({
            where: {
              id: order_one.id,
            },
            data: {
              status: PaymentStatus.FAILED,
            },
          });
        } else if (!txData.isSuccess && txData.value && txData.from) {
          // 支付状态不对 失败
          await prisma.dobOrder.update({
            where: {
              id: order_one.id,
            },
            data: {
              status: PaymentStatus.FAILED,
            },
          });
        }
      }
    }
  }
}
