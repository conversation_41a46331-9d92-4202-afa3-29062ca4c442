import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seed_reissue_cluster_temp(book_id: string) {
  //查询老的cluster orderdata等其他新信息
  const old_book_data = await prisma.book.findUnique({
    where: { id: book_id },
    include: { cluster: { include: { dobOrder: true } } },
  });

  //先清理老的cluster--通过唯一bookID
  const clusters = await prisma.cluster.findUnique({
    where: { bookId: book_id },
  });
  if (clusters) {
    console.log('aaa');
    await prisma.cluster.delete({
      where: { bookId: book_id },
    });
  }

  //写入新的cluster
  const new_cluster_id =
    '0xb09a7b74f08afe5246b415f134fcda946207d761ef92f315ca563cc9dd22c315';
  const new_cluster_typeHash =
    '0x52e5418248a2e3125f411670edb8633acc9a4e0d680941270c42d6fb89ef8002';
  const new_cluster_txHash =
    '0x87759e47b5f1200d63d2ce29622fd1c9e00304cac62c03aee1b56e9aaa1c37bf';
  let clusterid = '';
  if (old_book_data && old_book_data.cluster) {
    const new_cluster = await prisma.cluster.create({
      data: {
        name: old_book_data.cluster.name,
        description: old_book_data.cluster.description,
        decoder: old_book_data.cluster.decoder,
        pattern: old_book_data.cluster.pattern,
        clusterId: new_cluster_id,
        contractHash: old_book_data.cluster.contractHash,
        typeHash: new_cluster_typeHash,
        creator: old_book_data.cluster.creator,
        hash: new_cluster_txHash,
        ownerAddress: old_book_data.cluster.ownerAddress,
        timestamp: new Date().toString(),
        book: {
          connect: {
            id: book_id,
          },
        },
        ckbCellCost: 15000,
        mintFee: 3000,
        sporeCount: 0,
        perMintAmount: 0,
        withdrawn: 0,
      },
    });
    clusterid = new_cluster.id;
  }
  return clusterid;
}
