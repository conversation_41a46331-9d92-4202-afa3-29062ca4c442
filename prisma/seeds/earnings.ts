import {
  DnaLevel,
  EarningsType,
  PaymentStatus,
  Prisma,
  PrismaClient,
} from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

function getRandomEnumValue(): DnaLevel {
  const keys = Object.keys(DnaLevel);
  const randomIndex = Math.floor(Math.random() * keys.length);
  return DnaLevel[keys[randomIndex] as keyof typeof DnaLevel];
}

function getRandomNumber(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

export async function seed_earnings() {
  const user = await prisma.user.findUnique({
    where: {
      email: '<EMAIL>',
    },
    include: { books: { select: { cluster: { select: { id: true } } } } },
  });

  if (!user) return true;
  // 获取book
  if (!user['books']) return true;
  const books = user['books'];

  for (const c of books) {
    // 创建spore
    if (c.cluster && c.cluster.id) {
      const uuid = randomUUID();
      const level = getRandomEnumValue();
      const price = getRandomNumber(1, 10);

      // 生成购买记录
      const dobOrder: Prisma.DobOrderCreateInput = {
        dna: uuid + 'dna',
        level: level,
        price: price,
        cryptoPrice: price,
        dnaData: uuid + 'dnaData',
        status: PaymentStatus.DONE,
        earningsType: EarningsType.PROCESSING,
        payUser: {
          connect: { id: user.id },
        },
        cluster: {
          connect: { id: c.cluster.id },
        },
      };
      const doborder = await prisma.dobOrder.create({ data: dobOrder });

      const sporeData: Prisma.DobCreateInput = {
        dobId: uuid,
        ownerAddress: uuid + 'ownerAddress',
        utxoData: uuid + 'utxoData',
        metadata: uuid + 'metadata',
        dna: uuid + 'dna',
        level: level,
        cacheExpTime: 0,
        txHash: 'txhash' + uuid,
        cluster: {
          connect: {
            id: c.cluster.id,
          },
        },
        user: {
          connect: {
            id: user.id,
          },
        },
        dobOrder: {
          connect: {
            id: doborder.id,
          },
        },
        withdrawn: 0,
      };
      await prisma.dob.create({ data: sporeData });
    }
  }
}
