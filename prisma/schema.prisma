// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                 String    @id @default(uuid())
  provider           String
  providerAccountId  String
  refreshToken       String?
  accessToken        String?
  accessTokenExpires DateTime? @db.Timestamptz(6)
  createdAt          DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt          DateTime  @updatedAt @db.Timestamptz(6)

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Discord {
  id                     String    @id
  createdAt              DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt              DateTime  @updatedAt @db.Timestamptz(6)
  global_name            String?
  username               String?
  discriminator          String?
  accent_color           Int?
  avatar                 String?
  avatar_decoration      String?
  avatar_decoration_data String?
  banner                 String?
  banner_color           String?
  display_name           String?
  flags                  Int?
  locale                 String?
  mfa_enabled            Boolean?
  premium_type           Int?
  public_flags           Int?
  accessToken            String?
  refreshToken           String?
  provider               String?
  fetchedAt              DateTime? @db.Timestamptz(6)

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String @unique

  @@map("discords")
}

model Google {
  id          String   @id
  displayName String?
  email       String
  verified    Boolean
  avatar      String?
  createdAt   DateTime @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime @updatedAt @db.Timestamptz(6)

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String @unique

  @@map("googles")
}

model Twitter {
  id                String    @id
  createdAt         DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt         DateTime  @updatedAt @db.Timestamptz(6)
  name              String?
  profileImageUrl   String?
  username          String?   @unique
  accessToken       String?
  accessTokenExpiry DateTime? @db.Timestamptz(6)
  refreshToken      String?

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String @unique

  @@map("twitters")
}

model Wallet {
  id              Int       @id @default(autoincrement())
  address         String    @unique
  internalAddress String?   @unique
  userId          String?   @unique
  signCode        String?
  isSign          Boolean   @default(false)
  signInfo        String?
  signdateAt      DateTime? @db.Timestamptz(6)
  updatedAt       DateTime  @updatedAt @db.Timestamptz(6)

  @@map("wallet")
}

model User {
  id             String   @id @default(uuid())
  name           String?
  email          String?  @unique
  password       String?
  avatar         String?
  isWallet       Boolean  @default(false)
  emailConfirmed Boolean  @default(false)
  locked         Boolean  @default(false)
  role           Role     @default(MEMBER)
  createdAt      DateTime @default(now()) @db.Timestamptz(6)
  updatedAt      DateTime @updatedAt @db.Timestamptz(6)
  code           String   @unique @default(nanoid(8))
  inviteCode     String?

  twitter         Twitter?
  discord         Discord?
  google          Google?
  accounts        Account[]
  token           Token[]
  books           Book[]
  dobs            Dob[]
  printOrders     PrintOrder[]
  dobOrders       DobOrder[]
  payments        Payment[]
  withdrawalOrder WithdrawalOrder[]
  Author          Author[]

  @@map("users")
}

model Token {
  id         String    @id @default(uuid())
  token      String
  expiryTime DateTime  @db.Timestamptz(6)
  type       TokenType

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  @@unique([token, userId])
  @@index([userId])
  @@map("tokens")
}

model MagicLinkToken {
  id        String   @id @default(uuid())
  token     String
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  email     String   @unique

  @@map("maginlink_tokens")
}

model Book {
  id                  String    @id @default(uuid())
  title               String
  language            String
  category            String
  type                BookType  @default(PRINT_BOOK)
  subtitle            String?
  edition             String?
  editionStatement    String?
  bannerImages        String[]
  description         String
  contributorNotes    String?
  tableOfContents     String[]
  keywords            String?
  bisacMainCategory   String?
  bisacCategory2      String?
  bisacCategory3      String?
  audience            String?
  explicitContentType String?
  phase               DnaLevel? @default(GOLD)
  phaseRemain         Int?      @default(0)
  sales               Int?      @default(0)
  views               Int?      @default(0)
  createdAt           DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt           DateTime  @updatedAt @db.Timestamptz(6)

  contributors    Contributor[]
  copyright       Copyright?
  price           Price?
  specification   Specification?
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId          String
  status          BookStatus        @default(DRAFT)
  cluster         Cluster?
  printOrders     PrintOrder[]
  BookEarningsLog BookEarningsLog[]
  DobEarningsLog  DobEarningsLog[]
  WhiteList       WhiteList[]

  @@map("books")
}

model Contributor {
  id        String   @id @default(uuid())
  role      String   @default("author")
  firstname String
  lastname  String
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)
  book      Book     @relation(fields: [bookId], references: [id], onDelete: Cascade)
  bookId    String

  @@map("book_contributors")
}

model Copyright {
  id            String           @id @default(uuid())
  license       CopyrightLicense @default(PUBLIC_DOMAIN)
  holderName    String?
  copyrightYear String?
  createdAt     DateTime         @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime         @updatedAt @db.Timestamptz(6)
  book          Book             @relation(fields: [bookId], references: [id], onDelete: Cascade)
  bookId        String           @unique
  isbn          Isbn?

  @@map("book_copyrights")
}

model Isbn {
  id                String    @id @default(uuid())
  isFreeIsbn        Boolean   @default(false)
  isbnNum           String
  publisher         String
  contactName       String
  contactAddress    String
  contactCity       String
  contactPostalCode String
  contactState      String
  contactCountry    String
  contactPhone      String
  copyright         Copyright @relation(fields: [copyrightId], references: [id], onDelete: Cascade)
  copyrightId       String    @unique
  createdAt         DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt         DateTime  @updatedAt @db.Timestamptz(6)

  @@map("book_isbns")
}

model Specification {
  id               String   @id @default(uuid())
  sourceUrl        String
  coverUrl         String
  interiorColor    String
  printQuality     String
  paperType        String
  bookBinding      String
  coverFinish      String
  createdAt        DateTime @default(now()) @db.Timestamptz(6)
  updatedAt        DateTime @updatedAt @db.Timestamptz(6)
  book             Book     @relation(fields: [bookId], references: [id], onDelete: Cascade)
  bookId           String   @unique
  // lulu validation result
  sourceValidateId Int
  bookSize         String
  pageCount        Int
  coverValidateId  Int
  podPackageId     String

  @@map("book_specifications")
}

model Price {
  id            String    @id @default(uuid())
  goldCount     Int
  goldRemain    Int
  goldPrice     Decimal
  silverCount   Int
  silverRemain  Int
  silverPrice   Decimal
  copperCount   Int
  copperRemain  Int
  copperPrice   Decimal
  bluePrice     Decimal
  blueSales     Int       @default(0)
  openTime      DateTime  @default(now()) @db.Timestamptz(6)
  whiteOpenTime DateTime  @default(now()) @db.Timestamptz(6)
  whiteLevel    DnaLevel?
  createdAt     DateTime  @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime  @updatedAt @db.Timestamptz(6)
  book          Book      @relation(fields: [bookId], references: [id], onDelete: Cascade)
  bookId        String    @unique

  @@map("book_prices")
}

model Cluster {
  id            String     @id @default(uuid())
  clusterId     String     @unique
  contractHash  String
  typeHash      String?
  creator       String
  hash          String
  ownerAddress  String
  name          String
  description   String
  decoder       String
  pattern       String
  timestamp     String
  ckbCellCost   Int
  mintFee       Int
  perMintAmount Int
  sporeCount    Int?
  withdrawn     Decimal?   @default(0) @db.Decimal(precision: 15, scale: 5)
  createdAt     DateTime   @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime   @updatedAt @db.Timestamptz(6)
  book          Book       @relation(fields: [bookId], references: [id], onDelete: Cascade)
  bookId        String     @unique
  dobs          Dob[]
  dobOrder      DobOrder[]

  @@map("clusters")
}

model Dob {
  id                  String               @id @default(uuid())
  dobId               String               @unique
  txHash              String
  ownerAddress        String
  utxoData            String?
  metadata            String?
  dna                 String
  level               DnaLevel
  isAdmin             Boolean              @default(false)
  withdrawn           Decimal?             @default(0) @db.Decimal(precision: 15, scale: 5)
  cluster             Cluster              @relation(fields: [clusterId], references: [id])
  clusterId           String
  user                User?                @relation(fields: [userId], references: [id])
  userId              String?
  dobOrder            DobOrder?            @relation(fields: [dobOrderId], references: [id])
  dobOrderId          String?
  printOrder          PrintOrder?
  cacheExpTime        Int
  createdAt           DateTime             @default(now()) @db.Timestamptz(6)
  updatedAt           DateTime             @updatedAt @db.Timestamptz(6)
  DobEarningsLog      DobEarningsLog?
  encryptedBookSource EncryptedBookSource?

  @@map("dobs")
}

model DobLogs {
  id           Int    @id @default(autoincrement())
  operation    Int
  operationUid String
  dobId        String
  clusterId    String
  timestamp    String

  @@map("dob_logs")
}

model DobOrder {
  id           String        @id @default(uuid())
  dna          String
  level        DnaLevel
  amount       Int           @default(1)
  txhash       String?       @unique
  price        Decimal
  cryptoPrice  Decimal       @default(0)
  network      PayNetWork?
  dnaData      String
  earningsType EarningsType  @default(INIT)
  status       PaymentStatus
  payUser      User          @relation(fields: [payUserId], references: [id])
  payUserId    String
  cluster      Cluster       @relation(fields: [clusterId], references: [id])
  clusterId    String
  payment      Payment?      @relation(fields: [paymentId], references: [id])
  paymentId    String?       @unique
  dob          Dob[]
  createdAt    DateTime      @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime      @updatedAt @db.Timestamptz(6)

  @@map("dob_orders")
}

model PrintOrder {
  id                  String           @id @default(uuid())
  quantity            Int              @default(1)
  user                User             @relation(fields: [userId], references: [id])
  userId              String
  book                Book             @relation(fields: [bookId], references: [id])
  bookId              String
  dob                 Dob              @relation(fields: [dobId], references: [id])
  dobId               String           @unique
  // shipping address
  city                String
  countryCode         String
  email               String
  isBusiness          Boolean          @default(false)
  name                String?
  organization        String?
  phone               String
  postcode            String
  stateCode           String?
  street1             String
  street2             String?
  title               ShippingTitle    @default(DR)
  shippingLevel       ShippingLevel    @default(MAIL)
  shippingOptionLevel ShippingLevel    @default(MAIL)
  // evm
  txhash              String?
  network             PayNetWork?
  amount              Decimal?
  // print Job
  printJobId          String?          @unique
  // payment status
  payment             Payment?         @relation(fields: [paymentId], references: [id])
  paymentId           String?          @unique
  status              PrintOrderStatus @default(DRAFT)
  createdAt           DateTime         @default(now()) @db.Timestamptz(6)
  updatedAt           DateTime         @updatedAt @db.Timestamptz(6)

  @@map("print_orders")
}

model Payment {
  id            String          @id @default(uuid())
  user          User            @relation(fields: [userId], references: [id])
  userId        String
  transactionId String          @unique
  provider      PaymentProvider @default(STRIPE)
  amount        Decimal
  currency      CURRENCY        @default(USD)
  status        PaymentStatus   @default(PENDING)
  createdAt     DateTime        @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime        @updatedAt @db.Timestamptz(6)
  printOrder    PrintOrder?
  dobOrder      DobOrder?

  @@map("payments")
}

model BookEarningsLog {
  id               String   @id @default(uuid())
  goldEarnings     Decimal? @db.Decimal(precision: 15, scale: 5)
  silverEarnings   Decimal? @db.Decimal(precision: 15, scale: 5)
  copperEarnings   Decimal? @db.Decimal(precision: 15, scale: 5)
  authorEarnings   Decimal? @db.Decimal(precision: 15, scale: 5)
  platformEarnings Decimal? @db.Decimal(precision: 15, scale: 5)
  bookId           String   @unique
  book             Book     @relation(fields: [bookId], references: [id])
  createdAt        DateTime @default(now()) @db.Timestamptz(6)
  updatedAt        DateTime @updatedAt @db.Timestamptz(6)

  @@map("book_earnings_log")
}

model IsbnFree {
  id        Int          @id @default(autoincrement())
  isbnNum   String       @unique
  status    IsbnFreeType @default(UNUSED)
  createdAt DateTime     @default(now()) @db.Timestamptz(6)
  updatedAt DateTime     @updatedAt @db.Timestamptz(6)

  @@map("book_isbns_free")
}

model WithdrawalOrder {
  id                String           @id @default(uuid())
  collectionAddress String?
  withdrawalPrice   Decimal          @db.Decimal(precision: 15, scale: 5)
  withdrawalSource  WithdrawalSource
  withdrawalType    WithdrawalType
  visaName          String?
  visaCVC           String?
  status            PaymentStatus
  operand           String?
  remark            String?
  createdAt         DateTime         @default(now()) @db.Timestamptz(6)
  updatedAt         DateTime         @updatedAt @db.Timestamptz(6)
  user              User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId            String

  @@map("withdrawal_order")
}

model Issue {
  id        Int        @id @default(autoincrement())
  type      IssueType  @default(FAQ)
  group     IssueGroup
  language  Language   @default(CN)
  ask       String
  answer    String
  sort      Int?
  createdAt DateTime   @default(now()) @db.Timestamptz(6)

  @@map("issue")
}

model Author {
  id        String         @id @default(uuid())
  name      String
  surname   String
  email     String
  identity  AuthorIdentity @default(AUTHOR)
  abstract  String
  user      User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String
  status    BookStatus
  createdAt DateTime       @default(now()) @db.Timestamptz(6)

  @@map("author")
}

model BookSku {
  id               Int     @id @default(autoincrement())
  fullSku          String
  active           String
  bookType         String
  minPage          Int
  maxPage          Int
  basePriceUSD     Decimal @db.Decimal(precision: 10, scale: 4)
  perPagePriceUSD  Decimal @db.Decimal(precision: 10, scale: 4)
  basePriceGBP     Decimal @db.Decimal(precision: 10, scale: 4)
  perPagePriceGBP  Decimal @db.Decimal(precision: 10, scale: 4)
  basePriceEUR     Decimal @db.Decimal(precision: 10, scale: 4)
  perPagePriceEUR  Decimal @db.Decimal(precision: 10, scale: 4)
  basePriceAUD     Decimal @db.Decimal(precision: 10, scale: 4)
  perPagePriceAUD  Decimal @db.Decimal(precision: 10, scale: 4)
  basePriceCAD     Decimal @db.Decimal(precision: 10, scale: 4)
  perPagePriceCAD  Decimal @db.Decimal(precision: 10, scale: 4)
  trimWidthIN      Decimal @db.Decimal(precision: 10, scale: 4)
  trimHeightIN     Decimal @db.Decimal(precision: 10, scale: 4)
  trimWidthMM2     Int
  trimHeightMM     Int
  widthBleedIN     Decimal @db.Decimal(precision: 10, scale: 4)
  heightBleedIN    Decimal @db.Decimal(precision: 10, scale: 4)
  widthBleedMM     Decimal @db.Decimal(precision: 10, scale: 4)
  heightBleedMM    Decimal @db.Decimal(precision: 10, scale: 4)
  interiorColor    String
  printQuality     String
  bind             String
  Interior         String
  InteriorGSM      Int
  InteriorStock    String
  InteriorTone     String
  interiorPPI      String
  paperType        String
  lamination       String
  linenColor       String
  foilColor        String
  printInsideCover String

  @@map("book_skus")
}

model DobEarningsLog {
  id                 String   @id @default(uuid())
  level              DnaLevel
  price              Decimal? @db.Decimal(precision: 15, scale: 5)
  goldHoldEarnings   Decimal? @db.Decimal(precision: 15, scale: 5)
  silverHoldEarnings Decimal? @db.Decimal(precision: 15, scale: 5)
  copperHoldEarnings Decimal? @db.Decimal(precision: 15, scale: 5)
  blueHoldEarnings   Decimal? @db.Decimal(precision: 15, scale: 5)
  authorEarnings     Decimal? @db.Decimal(precision: 15, scale: 5)
  platformEarnings   Decimal? @db.Decimal(precision: 15, scale: 5)
  dob                Dob      @relation(fields: [dobId], references: [id])
  dobId              String   @unique
  book               Book     @relation(fields: [bookId], references: [id])
  bookId             String
  createdAt          DateTime @default(now()) @db.Timestamptz(6)
  updatedAt          DateTime @updatedAt @db.Timestamptz(6)

  @@map("dob_earnings_log")
}

model GolbalConfig {
  id             String   @id @default(uuid())
  ethPrice       Decimal? @db.Decimal(precision: 17, scale: 8)
  btcPrice       Decimal? @db.Decimal(precision: 17, scale: 8)
  ckbPrice       Decimal? @db.Decimal(precision: 17, scale: 8)
  isOpenwithdraw Boolean? @default(false)
  txHashTag      String?

  @@map("golbal_config")
}

model WhiteList {
  id        String   @id @default(uuid())
  address   String?  @unique
  email     String?  @unique
  createdAt DateTime @default(now()) @db.Timestamptz(6)
  updatedAt DateTime @updatedAt @db.Timestamptz(6)
  book      Book     @relation(fields: [bookId], references: [id], onDelete: Cascade)
  bookId    String

  @@map("white_list")
}

model platformDobs {
  id            Int          @id @default(autoincrement())
  CopperBerryId Int?         @default(0)
  MintTxHash    String?
  SporeId       String?
  status        IsbnFreeType @default(UNUSED)
  createdAt     DateTime     @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime     @updatedAt @db.Timestamptz(6)

  @@map("platform_dobs")
}

model EncryptedBookSource {
  dob           Dob      @relation(fields: [dobId], references: [dobId])
  dobId         String   @id @unique
  sourceUrl     String   @db.VarChar(255)
  adminPassword String?  @db.VarChar(255)
  userPassword  String?  @db.VarChar(255)
  createdAt     DateTime @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime @updatedAt @db.Timestamptz(6)

  @@map("encrypted_book_source")
}

model CkbTx {
  id Int @id @default(autoincrement())

  txHash String @db.VarChar()

  rawTx String @db.Text()

  status CkbTxStatus

  createdAt     DateTime @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime @updatedAt @db.Timestamptz(6)

  @@index([txHash])
}

enum CkbTxStatus {
  PREPARED
  SENT
  COMMITTED
  FAILED
  CONFIRMED
}

//Table ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑
//Enum ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓

enum Role {
  MEMBER
  ADMIN
  EDITOR
}

enum TokenType {
  CONFIRM_EMAIL
  RESET_PASSWORD
  REFRESH_TOKEN
}

enum BookType {
  PRINT_BOOK
  PHOTO_BOOK
  COMIC_BOOK
  MAGAZINE
  YEARBOOK
  CALENDAR
}

enum CopyrightLicense {
  STANDARD_COPYRIGHT
  CREATIVE_COMMONS
  PUBLIC_DOMAIN
}

enum InteriorColor {
  BLACK_WHITE_PREMIUM
  BLACK_WHITE_STANDARD
  PREMIUM_COLOR
  COLOR_STANDARD
}

enum PaperType {
  CREAM_60
  WHITE_60
  WHITE_COATED_80
}

enum BookBinding {
  COIL_BOUND
  HARDCOVER
  PAPERBACK
  SADDLE_STITCH
  LINEN_WRAP
}

enum CoverFinish {
  GLOSSY
  MATTE
}

enum BookStatus {
  DRAFT
  PENDING
  APPROVED
  PUBLISHED
  FAILED
}

enum ShippingLevel {
  MAIL
  PRIORITY_MAIL
  GROUND_HD
  GROUND_BUS
  GROUND
  EXPEDITED
  EXPRESS
}

enum PrintOrderStatus {
  DRAFT
  PENDING
  PAID
  CANCEL
  COMPLETE
}

enum CURRENCY {
  USD
}

enum PaymentProvider {
  STRIPE
  CRYTPO
}

enum PaymentStatus {
  PENDING
  PAID
  DONE
  FAILED
  CANCELED
}

enum ShippingTitle {
  MR
  MISS
  MRS
  MS
  DR
}

enum OrderType {
  PRINT_ORDER
  DOB_ORDER
}

enum DnaLevel {
  GOLD
  SILVER
  COPPER
  BLUE
}

enum PayNetWork {
  BTC
  ETH
  CKB
  USD
}

enum WithdrawalType {
  METAMASK
  COINBASE
  VISA
  BTC
  ETH
}

enum Language {
  CN
  EN
  JA
  FI
}

enum AuthorIdentity {
  AUTHOR
  AUTHOR_BROKER
  COPYRIGHT_OWNER
  INSTITUTION
  INVITEE
}

enum EarningsType {
  INIT
  PROCESSING
  FINISH
}

enum IsbnFreeType {
  UNUSED
  USED
}

enum IssueType {
  FAQ
  HELP
}

enum IssueGroup {
  FAQ
  PUBLISH_BOOK
  PUT_DOWN
  PERSONAL_DATA
  ACCOUNT
  OTHER
}

enum WithdrawalSource {
  AUTHOR
  DOB
}
