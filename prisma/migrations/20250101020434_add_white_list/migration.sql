-- AlterEnum
ALTER TYPE "PaymentStatus" ADD VALUE 'PAID';

-- AlterTable
ALTER TABLE "dob_orders" ALTER COLUMN "cryptoPrice" SET DATA TYPE DECIMAL(65,30);

-- AlterTable
ALTER TABLE "dobs" ADD COLUMN     "isAdmin" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "white_list" (
    "id" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "email" TEXT,
    "whiteOpenTime" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) NOT NULL,
    "bookId" TEXT NOT NULL,

    CONSTRAINT "white_list_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "white_list_address_key" ON "white_list"("address");

-- CreateIndex
CREATE UNIQUE INDEX "white_list_internalAddress_key" ON "white_list"("email");

-- CreateIndex
CREATE UNIQUE INDEX "white_list_bookId_key" ON "white_list"("bookId");

-- AddForeignKey
ALTER TABLE "white_list" ADD CONSTRAINT "white_list_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE CASCADE;
