-- CreateTable
CREATE TABLE "dob_earnings_log" (
    "id" TEXT NOT NULL,
    "level" "DnaLevel" NOT NULL,
    "price" DECIMAL(15,5),
    "goldHoldEarnings" DECIMAL(15,5),
    "silverHoldEarnings" DECIMAL(15,5),
    "copperHoldEarnings" DECIMAL(15,5),
    "blueHoldEarnings" DECIMAL(15,5),
    "authorEarnings" DECIMAL(15,5),
    "platformEarnings" DECIMAL(15,5),
    "bookId" TEXT NOT NULL,
    "holdUserId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dob_earnings_log_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "dob_earnings_log_bookId_key" ON "dob_earnings_log"("bookId");

-- AddForeignKey
ALTER TABLE "dob_earnings_log" ADD CONSTRAINT "dob_earnings_log_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dob_earnings_log" ADD CONSTRAINT "dob_earnings_log_holdUserId_fkey" FOREIGN KEY ("holdUserId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
