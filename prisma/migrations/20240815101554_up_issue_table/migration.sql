/*
  Warnings:

  - Changed the type of `group` on the `issue` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "IssueGroup" AS ENUM ('FAQ', 'PUBLISH_BOOK', 'PUT_DOWN', 'PER<PERSON><PERSON>L_DATA', 'ACCOUNT', 'OTHER');

-- AlterTable
ALTER TABLE "issue" DROP COLUMN "group",
ADD COLUMN     "group" "IssueGroup" NOT NULL;
