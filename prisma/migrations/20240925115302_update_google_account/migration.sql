/*
  Warnings:

  - You are about to drop the column `accessToken` on the `googles` table. All the data in the column will be lost.
  - You are about to drop the column `accessTokenExpiry` on the `googles` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `googles` table. All the data in the column will be lost.
  - You are about to drop the column `profileImageUrl` on the `googles` table. All the data in the column will be lost.
  - You are about to drop the column `refreshToken` on the `googles` table. All the data in the column will be lost.
  - You are about to drop the column `username` on the `googles` table. All the data in the column will be lost.
  - Added the required column `email` to the `googles` table without a default value. This is not possible if the table is not empty.
  - Added the required column `verified` to the `googles` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "googles_username_key";

-- AlterTable
ALTER TABLE "googles" DROP COLUMN "accessToken",
DROP COLUMN "accessTokenExpiry",
DROP COLUMN "name",
DROP COLUMN "profileImageUrl",
DROP COLUMN "refreshToken",
DROP COLUMN "username",
ADD COLUMN     "avatar" TEXT,
ADD COLUMN     "displayName" TEXT,
ADD COLUMN     "email" TEXT NOT NULL,
ADD COLUMN     "verified" BOOLEAN NOT NULL,
ALTER COLUMN "createdAt" SET DATA TYPE TIMESTAMPTZ(6),
ALTER COLUMN "updatedAt" SET DATA TYPE TIMESTAMPTZ(6);
