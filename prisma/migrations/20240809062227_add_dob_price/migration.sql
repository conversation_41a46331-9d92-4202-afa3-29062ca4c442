/*
  Warnings:

  - You are about to drop the column `priceInfo` on the `clusters` table. All the data in the column will be lost.
  - Added the required column `contractHash` to the `clusters` table without a default value. This is not possible if the table is not empty.
  - Added the required column `price` to the `dob_orders` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "WithdrawalType" AS ENUM ('METAMASK', 'COINBASE', 'VISA', 'BTC', 'ETH');

-- AlterTable
ALTER TABLE "clusters" DROP COLUMN "priceInfo",
ADD COLUMN     "contractHash" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "dob_orders" ADD COLUMN     "price" DECIMAL(65,30) NOT NULL;

-- CreateTable
CREATE TABLE "EarningsLog" (
    "id" TEXT NOT NULL,
    "earningsTotal" DECIMAL(65,30) NOT NULL,
    "earningsDay" DECIMAL(65,30) NOT NULL,
    "goldCount" INTEGER,
    "silverCount" INTEGER,
    "silverEarningsDay" DECIMAL(65,30),
    "copperCount" INTEGER,
    "copperEarningsDay" DECIMAL(65,30),
    "blueCount" INTEGER,
    "bookId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EarningsLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WithdrawalOrder" (
    "id" TEXT NOT NULL,
    "withdrawalPrice" DECIMAL(65,30) NOT NULL,
    "withdrawalType" "WithdrawalType" NOT NULL,
    "status" "PaymentStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "WithdrawalOrder_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Issue" (
    "id" SERIAL NOT NULL,
    "ask" TEXT NOT NULL,
    "answer" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Issue_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "EarningsLog_bookId_key" ON "EarningsLog"("bookId");

-- AddForeignKey
ALTER TABLE "EarningsLog" ADD CONSTRAINT "EarningsLog_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WithdrawalOrder" ADD CONSTRAINT "WithdrawalOrder_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
