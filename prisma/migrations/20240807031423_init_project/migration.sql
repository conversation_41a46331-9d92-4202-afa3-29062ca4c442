-- Create<PERSON><PERSON>
CREATE TYPE "Role" AS ENUM ('MEMBER', 'ADMIN', 'EDITOR');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "TokenType" AS ENUM ('CONFIRM_EMAIL', 'RESET_PASSWORD', 'REFRESH_TOKEN');

-- C<PERSON><PERSON><PERSON>
CREATE TYPE "BookType" AS ENUM ('PRINT_BOOK', 'PHOTO_BOOK', 'COMIC_BOOK', 'MAGAZINE', 'YEARBOOK', 'CALENDAR');

-- CreateEnum
CREATE TYPE "CopyrightLicense" AS ENUM ('STANDARD_COPYRIGHT', 'CREATIVE_COMMONS', 'PUBLIC_DOMAIN');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "InteriorColor" AS ENUM ('BLACK_WHITE_PREMIUM', 'BLACK_WHITE_STANDARD', 'PREMIUM_COLOR', 'COLOR_STANDARD');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "PaperType" AS ENUM ('CREAM_60', 'WHITE_60', 'WHITE_COATED_80');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "BookBinding" AS ENUM ('COIL_BOUND', 'HARDCOVER', 'PAPERBACK', 'SADDLE_STITCH', 'LINEN_WRAP');

-- CreateEnum
CREATE TYPE "CoverFinish" AS ENUM ('GLOSSY', 'MATTE');

-- CreateEnum
CREATE TYPE "BookStatus" AS ENUM ('DRAFT', 'PENDING', 'APPROVED', 'PUBLISHED');

-- CreateEnum
CREATE TYPE "ShippingLevel" AS ENUM ('MAIL', 'PRIORITY_MAIL', 'GROUND_HD', 'GROUND_BUS', 'GROUND', 'EXPEDITED', 'EXPRESS');

-- CreateEnum
CREATE TYPE "PrintOrderStatus" AS ENUM ('DRAFT', 'PAID', 'CANCEL', 'COMPLETE');

-- CreateEnum
CREATE TYPE "CURRENCY" AS ENUM ('USD');

-- CreateEnum
CREATE TYPE "PaymentProvider" AS ENUM ('STRIPE', 'CRYTPO');

-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('PENDING', 'DONE', 'FAILED', 'CANCELED');

-- CreateEnum
CREATE TYPE "ShippingTitle" AS ENUM ('MR', 'MISS', 'MRS', 'MS', 'DR');

-- CreateEnum
CREATE TYPE "OrderType" AS ENUM ('PRINT_ORDER', 'DOB_ORDER');

-- CreateEnum
CREATE TYPE "DnaLevel" AS ENUM ('GOLD', 'SILVER', 'CORPPER', 'BLUE');

-- CreateEnum
CREATE TYPE "PayNetWork" AS ENUM ('BTC', 'ETH');

-- CreateTable
CREATE TABLE "accounts" (
    "id" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refreshToken" TEXT,
    "accessToken" TEXT,
    "accessTokenExpires" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "discords" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "global_name" TEXT,
    "username" TEXT,
    "discriminator" TEXT,
    "accent_color" INTEGER,
    "avatar" TEXT,
    "avatar_decoration" TEXT,
    "avatar_decoration_data" TEXT,
    "banner" TEXT,
    "banner_color" TEXT,
    "display_name" TEXT,
    "flags" INTEGER,
    "locale" TEXT,
    "mfa_enabled" BOOLEAN,
    "premium_type" INTEGER,
    "public_flags" INTEGER,
    "accessToken" TEXT,
    "refreshToken" TEXT,
    "provider" TEXT,
    "fetchedAt" TIMESTAMP(3),
    "userId" TEXT NOT NULL,

    CONSTRAINT "discords_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "googles" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT,
    "profileImageUrl" TEXT,
    "username" TEXT,
    "accessToken" TEXT,
    "accessTokenExpiry" TIMESTAMP(3),
    "refreshToken" TEXT,
    "userId" TEXT NOT NULL,

    CONSTRAINT "googles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "twitters" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" TEXT,
    "profileImageUrl" TEXT,
    "username" TEXT,
    "accessToken" TEXT,
    "accessTokenExpiry" TIMESTAMP(3),
    "refreshToken" TEXT,
    "userId" TEXT NOT NULL,

    CONSTRAINT "twitters_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "wallet" (
    "id" SERIAL NOT NULL,
    "address" TEXT NOT NULL,
    "userId" TEXT,
    "signCode" TEXT,
    "isSign" BOOLEAN NOT NULL DEFAULT false,
    "signInfo" TEXT,
    "signdateAt" TIMESTAMP(3),
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "wallet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "password" TEXT,
    "isWallet" BOOLEAN NOT NULL DEFAULT false,
    "emailConfirmed" BOOLEAN NOT NULL DEFAULT false,
    "locked" BOOLEAN NOT NULL DEFAULT false,
    "role" "Role" NOT NULL DEFAULT 'MEMBER',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "code" TEXT NOT NULL,
    "inviteCode" TEXT,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tokens" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expiryTime" TIMESTAMP(3) NOT NULL,
    "type" "TokenType" NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "maginlink_tokens" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "email" TEXT NOT NULL,

    CONSTRAINT "maginlink_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "books" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "language" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "type" "BookType" NOT NULL DEFAULT 'PRINT_BOOK',
    "subtitle" TEXT,
    "edition" TEXT,
    "editionStatement" TEXT,
    "bannerImages" TEXT[],
    "description" TEXT NOT NULL,
    "contributorNotes" TEXT,
    "tableOfContents" TEXT,
    "keywords" TEXT NOT NULL,
    "bisacMainCategory" TEXT NOT NULL,
    "bisacCategory2" TEXT NOT NULL,
    "bisacCategory3" TEXT NOT NULL,
    "audience" TEXT,
    "explicitContentType" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "BookStatus" NOT NULL DEFAULT 'DRAFT',

    CONSTRAINT "books_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "book_contributors" (
    "id" TEXT NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'author',
    "firstname" TEXT NOT NULL,
    "lastname" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "bookId" TEXT NOT NULL,

    CONSTRAINT "book_contributors_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "book_copyrights" (
    "id" TEXT NOT NULL,
    "license" "CopyrightLicense" NOT NULL DEFAULT 'PUBLIC_DOMAIN',
    "holderName" TEXT,
    "copyrightYear" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "bookId" TEXT NOT NULL,

    CONSTRAINT "book_copyrights_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "book_isbns" (
    "id" TEXT NOT NULL,
    "isbnNum" TEXT NOT NULL,
    "publisher" TEXT NOT NULL,
    "contactName" TEXT NOT NULL,
    "contactAddress" TEXT NOT NULL,
    "contactCity" TEXT NOT NULL,
    "contactPostalCode" TEXT NOT NULL,
    "contactState" TEXT NOT NULL,
    "contactCountry" TEXT NOT NULL,
    "contactPhone" TEXT NOT NULL,
    "copyrightId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "book_isbns_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "book_specifications" (
    "id" TEXT NOT NULL,
    "sourceUrl" TEXT NOT NULL,
    "coverUrl" TEXT NOT NULL,
    "interiorColor" "InteriorColor" NOT NULL,
    "paperType" "PaperType" NOT NULL,
    "bookBinding" "BookBinding" NOT NULL,
    "coverFinish" "CoverFinish" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "packageCode" TEXT NOT NULL,
    "bookId" TEXT NOT NULL,
    "sourceValidateId" INTEGER NOT NULL,
    "bookSize" TEXT NOT NULL,
    "pageCount" INTEGER NOT NULL,
    "coverValidateId" INTEGER NOT NULL,
    "podPackageId" TEXT NOT NULL,

    CONSTRAINT "book_specifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "book_prices" (
    "id" TEXT NOT NULL,
    "goldCount" INTEGER NOT NULL,
    "goldPrice" DECIMAL(65,30) NOT NULL,
    "silverCount" INTEGER NOT NULL,
    "silverPrice" DECIMAL(65,30) NOT NULL,
    "copperCount" INTEGER NOT NULL,
    "copperPrice" DECIMAL(65,30) NOT NULL,
    "bluePrice" DECIMAL(65,30) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "bookId" TEXT NOT NULL,

    CONSTRAINT "book_prices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "clusters" (
    "id" SERIAL NOT NULL,
    "clusterId" TEXT NOT NULL,
    "creator" TEXT NOT NULL,
    "hash" TEXT NOT NULL,
    "ownerAddress" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "decoder" TEXT NOT NULL,
    "pattern" TEXT NOT NULL,
    "timestamp" TEXT NOT NULL,
    "ckbCellCost" INTEGER NOT NULL,
    "mintFee" INTEGER NOT NULL,
    "perMintAmount" INTEGER NOT NULL,
    "sporeCount" INTEGER NOT NULL,
    "priceInfo" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "bookId" TEXT NOT NULL,

    CONSTRAINT "clusters_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dobs" (
    "id" SERIAL NOT NULL,
    "dobId" TEXT NOT NULL,
    "ownerAddress" TEXT NOT NULL,
    "timestamp" TEXT NOT NULL,
    "utxoData" TEXT NOT NULL,
    "metadata" TEXT NOT NULL,
    "dna" TEXT NOT NULL,
    "clusterId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "cacheExpTime" INTEGER NOT NULL,

    CONSTRAINT "dobs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dob_logs" (
    "id" SERIAL NOT NULL,
    "operation" INTEGER NOT NULL,
    "operationUid" TEXT NOT NULL,
    "dobId" TEXT NOT NULL,
    "clusterId" TEXT NOT NULL,
    "timestamp" TEXT NOT NULL,

    CONSTRAINT "dob_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dob_orders" (
    "id" TEXT NOT NULL,
    "dna" TEXT NOT NULL,
    "level" "DnaLevel" NOT NULL,
    "txhash" TEXT,
    "network" "PayNetWork",
    "dnaData" TEXT NOT NULL,
    "status" "PaymentStatus" NOT NULL,
    "userId" TEXT NOT NULL,
    "clusterId" INTEGER NOT NULL,
    "paymentId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dob_orders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "print_orders" (
    "id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL DEFAULT 1,
    "userId" TEXT NOT NULL,
    "bookId" TEXT NOT NULL,
    "dobId" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "countryCode" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "isBusiness" BOOLEAN NOT NULL DEFAULT false,
    "name" TEXT,
    "organization" TEXT,
    "phone" TEXT NOT NULL,
    "postcode" TEXT NOT NULL,
    "stateCode" TEXT,
    "street1" TEXT NOT NULL,
    "street2" TEXT,
    "title" "ShippingTitle" NOT NULL DEFAULT 'DR',
    "shippingLevel" "ShippingLevel" NOT NULL DEFAULT 'MAIL',
    "shippingOptionLevel" "ShippingLevel" NOT NULL DEFAULT 'MAIL',
    "printJobId" TEXT,
    "paymentId" TEXT,
    "status" "PrintOrderStatus" NOT NULL DEFAULT 'DRAFT',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "print_orders_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "payments" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "provider" "PaymentProvider" NOT NULL DEFAULT 'STRIPE',
    "amount" DECIMAL(65,30) NOT NULL,
    "currency" "CURRENCY" NOT NULL DEFAULT 'USD',
    "status" "PaymentStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "accounts_provider_providerAccountId_key" ON "accounts"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "discords_userId_key" ON "discords"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "googles_username_key" ON "googles"("username");

-- CreateIndex
CREATE UNIQUE INDEX "googles_userId_key" ON "googles"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "twitters_username_key" ON "twitters"("username");

-- CreateIndex
CREATE UNIQUE INDEX "twitters_userId_key" ON "twitters"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "wallet_address_key" ON "wallet"("address");

-- CreateIndex
CREATE UNIQUE INDEX "wallet_userId_key" ON "wallet"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_code_key" ON "users"("code");

-- CreateIndex
CREATE INDEX "tokens_userId_idx" ON "tokens"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "tokens_token_userId_key" ON "tokens"("token", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "maginlink_tokens_email_key" ON "maginlink_tokens"("email");

-- CreateIndex
CREATE UNIQUE INDEX "book_copyrights_bookId_key" ON "book_copyrights"("bookId");

-- CreateIndex
CREATE UNIQUE INDEX "book_isbns_copyrightId_key" ON "book_isbns"("copyrightId");

-- CreateIndex
CREATE UNIQUE INDEX "book_specifications_bookId_key" ON "book_specifications"("bookId");

-- CreateIndex
CREATE UNIQUE INDEX "book_prices_bookId_key" ON "book_prices"("bookId");

-- CreateIndex
CREATE UNIQUE INDEX "clusters_clusterId_key" ON "clusters"("clusterId");

-- CreateIndex
CREATE UNIQUE INDEX "clusters_bookId_key" ON "clusters"("bookId");

-- CreateIndex
CREATE UNIQUE INDEX "dobs_dobId_key" ON "dobs"("dobId");

-- CreateIndex
CREATE UNIQUE INDEX "dob_orders_paymentId_key" ON "dob_orders"("paymentId");

-- CreateIndex
CREATE UNIQUE INDEX "print_orders_dobId_key" ON "print_orders"("dobId");

-- CreateIndex
CREATE UNIQUE INDEX "print_orders_printJobId_key" ON "print_orders"("printJobId");

-- CreateIndex
CREATE UNIQUE INDEX "print_orders_paymentId_key" ON "print_orders"("paymentId");

-- CreateIndex
CREATE UNIQUE INDEX "payments_transactionId_key" ON "payments"("transactionId");

-- AddForeignKey
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "discords" ADD CONSTRAINT "discords_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "googles" ADD CONSTRAINT "googles_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "twitters" ADD CONSTRAINT "twitters_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tokens" ADD CONSTRAINT "tokens_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "books" ADD CONSTRAINT "books_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "book_contributors" ADD CONSTRAINT "book_contributors_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "book_copyrights" ADD CONSTRAINT "book_copyrights_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "book_isbns" ADD CONSTRAINT "book_isbns_copyrightId_fkey" FOREIGN KEY ("copyrightId") REFERENCES "book_copyrights"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "book_specifications" ADD CONSTRAINT "book_specifications_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "book_prices" ADD CONSTRAINT "book_prices_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "clusters" ADD CONSTRAINT "clusters_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dobs" ADD CONSTRAINT "dobs_clusterId_fkey" FOREIGN KEY ("clusterId") REFERENCES "clusters"("clusterId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dobs" ADD CONSTRAINT "dobs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dob_orders" ADD CONSTRAINT "dob_orders_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dob_orders" ADD CONSTRAINT "dob_orders_clusterId_fkey" FOREIGN KEY ("clusterId") REFERENCES "clusters"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dob_orders" ADD CONSTRAINT "dob_orders_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "payments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "print_orders" ADD CONSTRAINT "print_orders_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "print_orders" ADD CONSTRAINT "print_orders_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "print_orders" ADD CONSTRAINT "print_orders_dobId_fkey" FOREIGN KEY ("dobId") REFERENCES "dobs"("dobId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "print_orders" ADD CONSTRAINT "print_orders_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "payments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
