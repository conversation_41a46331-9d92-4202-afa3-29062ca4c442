-- CreateTable
CREATE TABLE "book_skus" (
    "id" SERIAL NOT NULL,
    "fullSku" TEXT NOT NULL,
    "active" TEXT NOT NULL,
    "bookType" TEXT NOT NULL,
    "minPage" INTEGER NOT NULL,
    "maxPage" INTEGER NOT NULL,
    "basePriceUSD" DECIMAL(10,4) NOT NULL,
    "perPagePriceUSD" DECIMAL(10,4) NOT NULL,
    "basePriceGBP" DECIMAL(10,4) NOT NULL,
    "perPagePriceGBP" DECIMAL(10,4) NOT NULL,
    "basePriceEUR" DECIMAL(10,4) NOT NULL,
    "perPagePriceEUR" DECIMAL(10,4) NOT NULL,
    "basePriceAUD" DECIMAL(10,4) NOT NULL,
    "perPagePriceAUD" DECIMAL(10,4) NOT NULL,
    "basePriceCAD" DECIMAL(10,4) NOT NULL,
    "perPagePriceCAD" DECIMAL(10,4) NOT NULL,
    "trimWidthIN" DECIMAL(10,4) NOT NULL,
    "trimHeightIN" DECIMAL(10,4) NOT NULL,
    "trimWidthMM2" INTEGER NOT NULL,
    "trimHeightMM" INTEGER NOT NULL,
    "widthBleedIN" DECIMAL(10,4) NOT NULL,
    "heightBleedIN" DECIMAL(10,4) NOT NULL,
    "widthBleedMM" DECIMAL(10,4) NOT NULL,
    "heightBleedMM" DECIMAL(10,4) NOT NULL,
    "interiorColor" TEXT NOT NULL,
    "printQuality" TEXT NOT NULL,
    "bind" TEXT NOT NULL,
    "Interior" TEXT NOT NULL,
    "InteriorGSM" INTEGER NOT NULL,
    "InteriorStock" TEXT NOT NULL,
    "InteriorTone" TEXT NOT NULL,
    "interiorPPI" TEXT NOT NULL,
    "paperType" TEXT NOT NULL,
    "lamination" TEXT NOT NULL,
    "linenColor" TEXT NOT NULL,
    "foilColor" TEXT NOT NULL,
    "printInsideCover" TEXT NOT NULL,

    CONSTRAINT "book_skus_pkey" PRIMARY KEY ("id")
);
