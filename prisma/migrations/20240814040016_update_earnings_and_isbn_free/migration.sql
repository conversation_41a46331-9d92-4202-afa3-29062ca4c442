/*
  Warnings:

  - You are about to drop the column `userId` on the `dob_orders` table. All the data in the column will be lost.
  - You are about to drop the `earnings_log` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `payUserId` to the `dob_orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `level` to the `dobs` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "IsbnFreeType" AS ENUM ('UNUSED', 'USED');

-- DropForeignKey
ALTER TABLE "dob_orders" DROP CONSTRAINT "dob_orders_userId_fkey";

-- DropForeignKey
ALTER TABLE "earnings_log" DROP CONSTRAINT "earnings_log_bookId_fkey";

-- AlterTable
ALTER TABLE "book_isbns" ADD COLUMN     "isFreeIsbn" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "dob_orders" DROP COLUMN "userId",
ADD COLUMN     "payUserId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "dobs" ADD COLUMN     "level" "DnaLevel" NOT NULL,
ADD COLUMN     "totalWithdrawAmount" DECIMAL(65,30),
ADD COLUMN     "withdrawAmount" DECIMAL(65,30);

-- DropTable
DROP TABLE "earnings_log";

-- CreateTable
CREATE TABLE "book_earnings_log" (
    "id" TEXT NOT NULL,
    "goldEarnings" DECIMAL(65,30),
    "silverEarnings" DECIMAL(65,30),
    "copperEarnings" DECIMAL(65,30),
    "authorEarnings" DECIMAL(65,30),
    "platformEarnings" DECIMAL(65,30),
    "bookId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "book_earnings_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "book_isbns_free" (
    "id" SERIAL NOT NULL,
    "isbnNum" TEXT NOT NULL,
    "status" "IsbnFreeType" NOT NULL DEFAULT 'UNUSED',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "book_isbns_free_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "book_earnings_log_bookId_key" ON "book_earnings_log"("bookId");

-- CreateIndex
CREATE UNIQUE INDEX "book_isbns_free_isbnNum_key" ON "book_isbns_free"("isbnNum");

-- AddForeignKey
ALTER TABLE "dob_orders" ADD CONSTRAINT "dob_orders_payUserId_fkey" FOREIGN KEY ("payUserId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "book_earnings_log" ADD CONSTRAINT "book_earnings_log_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
