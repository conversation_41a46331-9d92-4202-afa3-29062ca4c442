/*
  Warnings:

  - You are about to drop the column `booksUrl` on the `author` table. All the data in the column will be lost.
  - You are about to drop the column `isWritten` on the `author` table. All the data in the column will be lost.
  - Made the column `abstract` on table `author` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateEnum
CREATE TYPE "AuthorIdentity" AS ENUM ('AUTHOR', 'AUTHOR_BROKER', 'COPYRIGHT_OWNER', 'INSTITUTION', 'INVITEE');

-- AlterTable
ALTER TABLE "author" DROP COLUMN "booksUrl",
DROP COLUMN "isWritten",
ADD COLUMN     "identity" "AuthorIdentity" NOT NULL DEFAULT 'AUTHOR',
ALTER COLUMN "abstract" SET NOT NULL;
