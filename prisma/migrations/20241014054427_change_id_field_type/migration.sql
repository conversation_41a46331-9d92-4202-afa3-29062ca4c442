/*
  Warnings:

  - The primary key for the `accounts` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `author` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `book_contributors` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `book_copyrights` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `book_earnings_log` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `book_isbns` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `book_prices` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `book_specifications` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `books` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `clusters` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `discords` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `dob_earnings_log` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `dob_orders` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `dobs` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `golbal_config` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `googles` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `maginlink_tokens` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `payments` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `print_orders` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `tokens` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `twitters` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `users` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `withdrawal_order` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- AlterTable
ALTER TABLE "accounts" DROP CONSTRAINT "accounts_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "accounts_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "author" DROP CONSTRAINT "author_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "author_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "book_contributors" DROP CONSTRAINT "book_contributors_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "book_contributors_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "book_copyrights" DROP CONSTRAINT "book_copyrights_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "book_copyrights_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "book_earnings_log" DROP CONSTRAINT "book_earnings_log_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "book_earnings_log_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "book_isbns" DROP CONSTRAINT "book_isbns_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "book_isbns_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "book_prices" DROP CONSTRAINT "book_prices_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "book_prices_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "book_specifications" DROP CONSTRAINT "book_specifications_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "book_specifications_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "books" DROP CONSTRAINT "books_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "books_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "clusters" DROP CONSTRAINT "clusters_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "clusters_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "discords" DROP CONSTRAINT "discords_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "discords_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "dob_earnings_log" DROP CONSTRAINT "dob_earnings_log_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "dob_earnings_log_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "dob_orders" DROP CONSTRAINT "dob_orders_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "dob_orders_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "dobs" DROP CONSTRAINT "dobs_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "dobs_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "golbal_config" DROP CONSTRAINT "golbal_config_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "golbal_config_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "googles" DROP CONSTRAINT "googles_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "googles_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "maginlink_tokens" DROP CONSTRAINT "maginlink_tokens_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "maginlink_tokens_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "payments" DROP CONSTRAINT "payments_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "payments_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "print_orders" DROP CONSTRAINT "print_orders_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "print_orders_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "tokens" DROP CONSTRAINT "tokens_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "tokens_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "twitters" DROP CONSTRAINT "twitters_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "twitters_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "users" DROP CONSTRAINT "users_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "withdrawal_order" DROP CONSTRAINT "withdrawal_order_pkey",
ALTER COLUMN "id" SET DATA TYPE VARCHAR,
ADD CONSTRAINT "withdrawal_order_pkey" PRIMARY KEY ("id");
