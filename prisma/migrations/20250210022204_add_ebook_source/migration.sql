-- CreateTable
CREATE TABLE "encrypted_book_source" (
    "dobId" TEXT NOT NULL,
    "sourceUrl" VARCHAR(255) NOT NULL,
    "password" VARCHAR(255),
    "createdAt" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(6) NOT NULL,

    CONSTRAINT "encrypted_book_source_pkey" PRIMARY KEY ("dobId")
);

-- CreateIndex
CREATE UNIQUE INDEX "encrypted_book_source_dobId_key" ON "encrypted_book_source"("dobId");

-- AddForeignKey
ALTER TABLE "encrypted_book_source" ADD CONSTRAINT "encrypted_book_source_dobId_fkey" FOREIGN KEY ("dobId") REFERENCES "dobs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
