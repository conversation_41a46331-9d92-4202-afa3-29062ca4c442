/*
  Warnings:

  - The values [USD] on the enum `PayNetWork` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "PayNetWork_new" AS ENUM ('BTC', 'ETH', 'CKB', 'STRIPE');
ALTER TABLE "dob_orders" ALTER COLUMN "network" TYPE "PayNetWork_new" USING ("network"::text::"PayNetWork_new");
ALTER TABLE "print_orders" ALTER COLUMN "network" TYPE "PayNetWork_new" USING ("network"::text::"PayNetWork_new");
ALTER TYPE "PayNetWork" RENAME TO "PayNetWork_old";
ALTER TYPE "PayNetWork_new" RENAME TO "PayNetWork";
DROP TYPE "PayNetWork_old";
COMMIT;
