/*
  Warnings:

  - The values [STRIPE] on the enum `PayNetWork` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `whiteOpenTime` on the `white_list` table. All the data in the column will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "PayNetWork_new" AS ENUM ('BTC', 'ETH', 'CKB', 'USD');
ALTER TABLE "dob_orders" ALTER COLUMN "network" TYPE "PayNetWork_new" USING ("network"::text::"PayNetWork_new");
ALTER TABLE "print_orders" ALTER COLUMN "network" TYPE "PayNetWork_new" USING ("network"::text::"PayNetWork_new");
ALTER TYPE "PayNetWork" RENAME TO "PayNetWork_old";
ALTER TYPE "PayNetWork_new" RENAME TO "PayNetWork";
DROP TYPE "PayNetWork_old";
COMMIT;

-- AlterTable
ALTER TABLE "book_prices" ADD COLUMN     "whiteOpenTime" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "white_list" DROP COLUMN "whiteOpenTime";
