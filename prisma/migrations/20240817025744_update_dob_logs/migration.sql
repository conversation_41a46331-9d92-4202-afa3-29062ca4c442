/*
  Warnings:

  - You are about to drop the column `holdUserId` on the `dob_earnings_log` table. All the data in the column will be lost.
  - You are about to drop the column `dobOrderId` on the `dobs` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[dobId]` on the table `dob_earnings_log` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `dobId` to the `dob_earnings_log` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "dob_earnings_log" DROP CONSTRAINT "dob_earnings_log_holdUserId_fkey";

-- DropForeignKey
ALTER TABLE "dobs" DROP CONSTRAINT "dobs_dobOrderId_fkey";

-- DropIndex
DROP INDEX "dobs_dobOrderId_key";

-- AlterTable
ALTER TABLE "dob_earnings_log" DROP COLUMN "holdUserId",
ADD COLUMN     "dobId" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "dob_orders" ADD COLUMN     "dobId" TEXT;

-- AlterTable
ALTER TABLE "dobs" DROP COLUMN "dobOrderId";

-- CreateIndex
CREATE UNIQUE INDEX "dob_earnings_log_dobId_key" ON "dob_earnings_log"("dobId");

-- AddForeignKey
ALTER TABLE "dob_orders" ADD CONSTRAINT "dob_orders_dobId_fkey" FOREIGN KEY ("dobId") REFERENCES "dobs"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dob_earnings_log" ADD CONSTRAINT "dob_earnings_log_dobId_fkey" FOREIGN KEY ("dobId") REFERENCES "dobs"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
