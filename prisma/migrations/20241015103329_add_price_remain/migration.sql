/*
  Warnings:

  - Added the required column `copperRemain` to the `book_prices` table without a default value. This is not possible if the table is not empty.
  - Added the required column `goldRemain` to the `book_prices` table without a default value. This is not possible if the table is not empty.
  - Added the required column `silverRemain` to the `book_prices` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "book_prices" ADD COLUMN     "copperRemain" INTEGER NOT NULL,
ADD COLUMN     "goldRemain" INTEGER NOT NULL,
ADD COLUMN     "silverRemain" INTEGER NOT NULL;
