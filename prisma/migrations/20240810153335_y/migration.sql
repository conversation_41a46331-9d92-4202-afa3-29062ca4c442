/*
  Warnings:

  - You are about to drop the `EarningsLog` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `Issue` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `WithdrawalOrder` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "EarningsLog" DROP CONSTRAINT "EarningsLog_bookId_fkey";

-- DropForeignKey
ALTER TABLE "WithdrawalOrder" DROP CONSTRAINT "WithdrawalOrder_userId_fkey";

-- DropTable
DROP TABLE "EarningsLog";

-- DropTable
DROP TABLE "Issue";

-- DropTable
DROP TABLE "WithdrawalOrder";

-- CreateTable
CREATE TABLE "earnings_log" (
    "id" TEXT NOT NULL,
    "earningsTotal" DECIMAL(65,30) NOT NULL,
    "earningsDay" DECIMAL(65,30) NOT NULL,
    "goldCount" INTEGER,
    "silverCount" INTEGER,
    "silverEarningsDay" DECIMAL(65,30),
    "copperCount" INTEGER,
    "copperEarningsDay" DECIMAL(65,30),
    "blueCount" INTEGER,
    "bookId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "earnings_log_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "withdrawal_order" (
    "id" TEXT NOT NULL,
    "withdrawalPrice" DECIMAL(65,30) NOT NULL,
    "withdrawalType" "WithdrawalType" NOT NULL,
    "status" "PaymentStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "withdrawal_order_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "issue" (
    "id" SERIAL NOT NULL,
    "group" TEXT NOT NULL,
    "ask" TEXT NOT NULL,
    "answer" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "issue_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "author" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "surname" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "isWritten" INTEGER NOT NULL,
    "abstract" TEXT,
    "booksUrl" TEXT NOT NULL,

    CONSTRAINT "author_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "earnings_log_bookId_key" ON "earnings_log"("bookId");

-- AddForeignKey
ALTER TABLE "earnings_log" ADD CONSTRAINT "earnings_log_bookId_fkey" FOREIGN KEY ("bookId") REFERENCES "books"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "withdrawal_order" ADD CONSTRAINT "withdrawal_order_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
