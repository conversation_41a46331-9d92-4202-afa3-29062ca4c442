/*
  Warnings:

  - The primary key for the `clusters` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `dobs` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - A unique constraint covering the columns `[dobOrderId]` on the table `dobs` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `dobOrderId` to the `dobs` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "dob_orders" DROP CONSTRAINT "dob_orders_clusterId_fkey";

-- AlterTable
ALTER TABLE "clusters" DROP CONSTRAINT "clusters_pkey",
ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "clusters_pkey" PRIMARY KEY ("id");
DROP SEQUENCE "clusters_id_seq";

-- AlterTable
ALTER TABLE "dob_orders" ALTER COLUMN "clusterId" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "dobs" DROP CONSTRAINT "dobs_pkey",
ADD COLUMN     "dobOrderId" TEXT NOT NULL,
ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "id" SET DATA TYPE TEXT,
ADD CONSTRAINT "dobs_pkey" PRIMARY KEY ("id");
DROP SEQUENCE "dobs_id_seq";

-- CreateIndex
CREATE UNIQUE INDEX "dobs_dobOrderId_key" ON "dobs"("dobOrderId");

-- AddForeignKey
ALTER TABLE "dobs" ADD CONSTRAINT "dobs_dobOrderId_fkey" FOREIGN KEY ("dobOrderId") REFERENCES "dob_orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dob_orders" ADD CONSTRAINT "dob_orders_clusterId_fkey" FOREIGN KEY ("clusterId") REFERENCES "clusters"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
