/*
  Warnings:

  - Added the required column `bluePrice` to the `earnings_log` table without a default value. This is not possible if the table is not empty.
  - Added the required column `buyTotal` to the `earnings_log` table without a default value. This is not possible if the table is not empty.
  - Added the required column `copperPrice` to the `earnings_log` table without a default value. This is not possible if the table is not empty.
  - Added the required column `silverPrice` to the `earnings_log` table without a default value. This is not possible if the table is not empty.
  - Added the required column `yield` to the `earnings_log` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "earnings_log" ADD COLUMN     "blueEarningsDay" DECIMAL(65,30),
ADD COLUMN     "bluePrice" DECIMAL(65,30) NOT NULL,
ADD COLUMN     "buyTotal" DECIMAL(65,30) NOT NULL,
ADD COLUMN     "copperPrice" DECIMAL(65,30) NOT NULL,
ADD COLUMN     "goldPrice" DECIMAL(65,30),
ADD COLUMN     "silverPrice" DECIMAL(65,30) NOT NULL,
ADD COLUMN     "yield" DECIMAL(65,30) NOT NULL;
