/*
  Warnings:

  - The values [CORPPER] on the enum `DnaLevel` will be removed. If these variants are still used in the database, this will fail.
  - You are about to alter the column `goldEarnings` on the `book_earnings_log` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(15,5)`.
  - You are about to alter the column `silverEarnings` on the `book_earnings_log` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(15,5)`.
  - You are about to alter the column `copperEarnings` on the `book_earnings_log` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(15,5)`.
  - You are about to alter the column `authorEarnings` on the `book_earnings_log` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(15,5)`.
  - You are about to alter the column `platformEarnings` on the `book_earnings_log` table. The data in that column could be lost. The data in that column will be cast from `Decimal(65,30)` to `Decimal(15,5)`.
  - You are about to drop the column `totalWithdrawAmount` on the `dobs` table. All the data in the column will be lost.
  - You are about to drop the column `withdrawAmount` on the `dobs` table. All the data in the column will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "DnaLevel_new" AS ENUM ('GOLD', 'SILVER', 'COPPER', 'BLUE');
ALTER TABLE "books" ALTER COLUMN "phase" DROP DEFAULT;
ALTER TABLE "books" ALTER COLUMN "phase" TYPE "DnaLevel_new" USING ("phase"::text::"DnaLevel_new");
ALTER TABLE "dobs" ALTER COLUMN "level" TYPE "DnaLevel_new" USING ("level"::text::"DnaLevel_new");
ALTER TABLE "dob_orders" ALTER COLUMN "level" TYPE "DnaLevel_new" USING ("level"::text::"DnaLevel_new");
ALTER TYPE "DnaLevel" RENAME TO "DnaLevel_old";
ALTER TYPE "DnaLevel_new" RENAME TO "DnaLevel";
DROP TYPE "DnaLevel_old";
ALTER TABLE "books" ALTER COLUMN "phase" SET DEFAULT 'GOLD';
COMMIT;

-- AlterTable
ALTER TABLE "book_earnings_log" ALTER COLUMN "goldEarnings" SET DATA TYPE DECIMAL(15,5),
ALTER COLUMN "silverEarnings" SET DATA TYPE DECIMAL(15,5),
ALTER COLUMN "copperEarnings" SET DATA TYPE DECIMAL(15,5),
ALTER COLUMN "authorEarnings" SET DATA TYPE DECIMAL(15,5),
ALTER COLUMN "platformEarnings" SET DATA TYPE DECIMAL(15,5);

-- AlterTable
ALTER TABLE "clusters" ADD COLUMN     "withdrawn" DECIMAL(15,5);

-- AlterTable
ALTER TABLE "dobs" DROP COLUMN "totalWithdrawAmount",
DROP COLUMN "withdrawAmount",
ADD COLUMN     "withdrawn" DECIMAL(15,5);
