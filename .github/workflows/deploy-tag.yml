name: Deploy Tag version to aws with ssh

on:
  push:
    tags:
      - 'v*'

env:
  DOCKER_IMAGE_NAME: book-service

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Build the source code tarball
        uses: a7ul/tar-action@v1.1.0
        id: compress
        with:
          command: c
          cwd: .
          files: |
            ./prisma
            ./src
            ./.dockerignore
            ./docker-compose.yml
            ./Dockerfile
            ./aspose.lic
            ./package.json
            ./nest-cli.json
            ./pnpm-lock.yaml
            ./tsconfig.json
            ./tsconfig.build.json
            ./.eslintrc.js
            ./.prettierrc
          outPath: book-service.tar.gz

      - name: Get the output
        run: echo "The output was ${{ steps.compress.outputs.done }}"

      - name: Sync docker image to remote server
        uses: easingthemes/ssh-deploy@main
        env:
          SSH_PRIVATE_KEY: ${{secrets.SSH_PRIVATE_KEY}}
          ARGS: '-rltgoDzv0 --delete'
          REMOTE_HOST: ${{secrets.REMOTE_TAG_HOST}}
          REMOTE_USER: ${{secrets.REMOTE_USER}}
          SOURCE: 'book-service.tar.gz'
          TARGET: '/data/deploy/book-service-latest.tar.gz'
          SCRIPT_BEFORE: |
            whoami
            ls -al /data/deploy/
          SCRIPT_AFTER: |
            whoami
            ls -al /data/deploy/
            tar zxvf /data/deploy/book-service-latest.tar.gz -C /data/deploy/book-service/ && \
            docker compose -f /data/book-service/docker-compose.yml build book-service && \
            docker compose -f /data/book-service/docker-compose.yml up -d --remove-orphans && \
            rm -rf /data/deploy/book-service/* && \
            echo $RSYNC_STDOUT
