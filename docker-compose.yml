# version: '3.8'
services:
  postgres:
    image: postgis/postgis:13-master
    restart: always
    environment:
      - POSTGRES_USER=book
      - POSTGRES_PASSWORD=book
    volumes:
      - postgres:/var/lib/postgresql/data
    ports:
      - '5432:5432'

  monitor:
    image: louislam/uptime-kuma:1
    restart: always
    volumes:
      - uptime-kuma:/app/data
    ports:
      - '3001:3001'
    links:
      - postgres

  book-script:
    build: .
    restart: no
    env_file: ./.env
    command:
      - /bin/bash
      - -c
      - |
        # npx prisma db push && \
        npx prisma migrate deploy && npx prisma db seed

  book-service:
    build: .
    restart: always
    env_file: ./.env
    ports:
      - '3000:3000'
    command:
      - /bin/bash
      - -c
      - |
        npx prisma generate && npm run start:prod

  cache:
    image: redis:6

  directus:
    #image: directus/directus:10.13.1
    build:
      context: ./directus
      dockerfile: Dockerfile
    ports:
      - 8055:8055
    volumes:
      - ./directus/uploads:/directus/uploads
      - ./directus/extensions:/directus/extensions
    depends_on:
      - cache
      - postgres
    env_file:
      - ./directus/.env

volumes:
  directus:
  postgres:
  uptime-kuma:
